<?php

namespace App\Console;

use App\Model\Orm;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputInterface;

abstract class BaseCommand extends Command
{

	use LoggerAwareTrait;
	use LockableTrait;

	private bool $isLockable = false;

	private ?string $env = null;

	protected function init(): void
	{
		if (property_exists($this, 'orm')) {
			assert($this->orm instanceof Orm);
			$this->orm->reconnect();
		}
		$this->logger?->info('Command {command} ({class}) started.', ['command' => $this->getName(), 'class' => static::class]);
	}

	protected function start(?InputInterface $input = null): void
	{
		$this->init();

		if (!($this instanceof BaseHeurekaImportCommand)) {
			$this->checkLock();
		}
	}

	protected function end(int $exitCode = 0): int
	{
		$this->logger?->info('Command {command} ({class}) finished with exitCode {exitCode}.', ['command' => $this->getName(), 'class' => static::class, 'exitCode' => $exitCode]);

		if (!($this instanceof \App\Console\Erp\BaseCommand)) {
			$this->releaseLock();
		}

		return $exitCode;
	}

	protected function setIsLockable(bool $isLockable = true, ?string $env = null): self
	{
		$this->isLockable = $isLockable;
		$this->env = $env;

		if (!property_exists($this, 'configService') && $isLockable && $env === null) {
			throw new \RuntimeException('Please add ConfigService as dependency to your command.');
		}

		return $this;
	}

	protected function checkLock(): void
	{
		if ($this->isLockable) {
			if (property_exists($this, 'configService')) {
				$this->env = $this->configService->get('env');
			}

			$name = $this->env . ':' . static::class;

			if ( ! $this->lock($name)) {
				throw new \RuntimeException(sprintf('The command "%s" is already running in another process.', $name));
			}
		}
	}

	protected function releaseLock(): void
	{
		$this->release();
	}

}
