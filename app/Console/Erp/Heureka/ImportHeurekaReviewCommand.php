<?php declare(strict_types = 1);

namespace App\Console\Erp\Heureka;

use App\Console\Erp\BaseHeurekaImportCommand;
use App\Model\Erp\Connector\HeurekaReviewConnector;
use App\Model\Erp\Importer\HeurekaReviewImporter;
use App\Model\Erp\Importer\HeurekaReviewImporterFactory;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\ImportCache;
use App\Model\Orm;
use App\Model\OrmCleaner;
use SuperKoderi\ConfigService;
use SuperKoderi\MutationHolder;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand(
	name: 'erp:import:heurekaReview',
	description: 'Review heureka import',
)]
// [AsCronTask(expression: '# #(9-15) * * *', transports: 'cronCommands')]
final class ImportHeurekaReviewCommand extends BaseHeurekaImportCommand
{
	private HeurekaReviewImporter $importer;

	public function __construct(
		Orm $orm,
		OrmCleaner $ormCleaner,
		MutationHolder $mutationHolder,
		ConfigService $configService,
		MessageBusInterface $messageBus,
		private readonly HeurekaReviewImporterFactory $heurekaReviewImporterFactory,
		private readonly HeurekaReviewConnector $heurekaReviewConnector,
	)
	{
		parent::__construct($orm, $ormCleaner, $mutationHolder, $configService, $messageBus);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->beginExecution($this->getDescription(), $output);

		$this->importer = $this->heurekaReviewImporterFactory->create($this->heurekaReviewConnector);
		$this->processBatches($output, $input);

		$this->endExecution($output);
		return $this->end(self::SUCCESS);
	}

	protected function processBatch(): Result
	{
		assert($this->importer instanceof HeurekaReviewImporter);
		return $this->importer->import(
			ImportCache::TYPE_HEUREKA_REVIEW
		);
	}

}
