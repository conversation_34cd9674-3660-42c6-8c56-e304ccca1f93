<?php

namespace App\Model;

use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use SuperKoderi\ConfigService;
use SuperKoderi\hasCacheTrait;
use SuperKoderi\hasCustomFieldTrait;
use SuperKoderi\hasFormDefaultDataTrait;

/**
 *
 * @property int $id {primary}
 * @property string $name {default ''}
 * @property string $position
 * @property string $link {default ''}
 * @property string $annotation {default ''}
 * @property DateTimeImmutable|null $publicTo  {default '+100 year'}
 * @property DateTimeImmutable|null $publicFrom {default 'now'}
 * @property DateTimeImmutable|null $created {default 'now'}
 * @property int $public {default 0}
 * @property int $enabledInChilds {default 0}
 * @property int|null $newWindow {default 0}
 * @property ArrayHash $customFieldsJson {container JsonContainer}

 * RELATIONS
 * @property BannerImage[]|OneHasMany $images {1:m BannerImage::$banner, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property Tree[]|ManyHasMany $categories {m:m Tree, oneSided=true, isMain=true}}
 *
 *
 * VIRTUAL
 * @property-read BannerImage|null $image {virtual}
 * @property-read int $width {virtual}
 * @property-read int $height {virtual}
 * @property ArrayHash|null $cf {virtual}

 * @property-read string $nicePositionName {virtual}
 *
 */
class Banner extends \Nextras\Orm\Entity\Entity
{
	use hasFormDefaultDataTrait;
	use hasCacheTrait;
	use hasCustomFieldTrait;

	private ConfigService $configService;

	public function injectService(ConfigService $configService): void
	{
		$this->configService = $configService;
	}

	protected function getterImage(): ?BannerImage
	{
		return $this->images->toCollection()->fetch();
	}


	protected function getterHeight(): int
	{
		if ($this->position && isset($this->configService->getParam('banners', 'positions')[$this->position]['height'])) {
			return $this->configService->getParam('banners', 'positions')[$this->position]['height'];
		} else {
			return 0;
		}
	}


	protected function getterWidth(): int
	{
		if ($this->position && isset($this->configService->getParam('banners', 'positions')[$this->position]['width'])) {
			return $this->configService->getParam('banners', 'positions')[$this->position]['width'];
		} else {
			return 0;
		}
	}

	public function getterNicePositionName(): string
	{
		$allPositions = $this->configService->getParam('banners', 'positions');
		return (isset($allPositions[$this->position])) ? $allPositions[$this->position]['name'] : $this->position;
	}
}
