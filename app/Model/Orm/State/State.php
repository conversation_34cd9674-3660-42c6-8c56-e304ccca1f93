<?php

namespace App\Model;

use Nette\Utils\ArrayHash;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use SuperKoderi\hasFormDefaultDataTrait;

/**
 * @property int $id {primary}
 * @property string $code iso2
 * @property string $name
 * @property ArrayHash $vatRates {container JsonContainer}
 * @property int $public {default 1}
 * @property string $defaultVatRate {default 'default'}
 *
 * RELATIONS
 * @property Mutation[]|ManyHasMany $mutations {m:m Mutation::$states}
 * @property User[]|OneHasMany|null $users {1:m User::$state}
 * @property Order[]|OneHasMany|null $orders {1:m Order::$state}
 * @property Order[]|OneHasMany|null $dOrders {1:m Order::$dState}
 * @property OneHasMany|MutationTransports[]|null $transports {1:m MutationTransports::$state}
 * @property Place[]|OneHasMany $places {1:m Place::$state, cascade=[persist, remove]}
 *
 * VIRTUAL
 * @property-read int|float $vatRateDefaultValue {virtual}
 *
 */
class State extends Entity
{
	use hasFormDefaultDataTrait;

	const DEFAULT_ID = 1;
	const DEFAULT_CODE = 'CZ';

	const COOKIE_NAME_SELECTED_STATE = 'selectedState';

	protected function getterVatRateDefaultValue(): mixed
	{
		return $this->vatRates->default;
	}

}
