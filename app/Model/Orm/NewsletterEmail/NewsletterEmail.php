<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $email
 * @property DateTimeImmutable $createdTime {default now}
 * @property string $hash
 * @property bool $emailConfirm {default false}
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$newsletterEmails}
 *
 * VIRTUAL
 */


class NewsletterEmail extends Entity
{

}
