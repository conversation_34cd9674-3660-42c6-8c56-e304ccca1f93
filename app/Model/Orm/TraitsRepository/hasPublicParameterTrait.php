<?php declare(strict_types = 1);

namespace SuperKoderi;

use App\Model\Orm;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;

trait hasPublicParameterTrait
{

	private bool $publicOnly = true;

	public function getById($id): ?IEntity
	{
		$cond['id'] = $id;
		$cond = $this->addPublicCond($cond);
		return $this->getBy($cond);
	}

	public function getBy(array $cond): ?IEntity
	{
		$cond = $this->addPublicCond($cond);
		return parent::getBy($cond);
	}

	public function findBy(array $cond): ICollection
	{
		$cond = $this->addPublicCond($cond);
		return parent::findBy($cond);
	}

	public function findById(mixed $ids): ICollection
	{
		$cond = $this->addPublicCond([]);
		$cond['id'] = $ids;

		return parent::findBy($cond);
	}

	public function getPublicOnly(): bool
	{
		return $this->publicOnly;
	}

	public function setPublicOnly(bool $public = true): void
	{
		$this->publicOnly = $public;
	}

	public function getPublicOnlyWhere(): array
	{
		/** @var Orm $orm */
		$orm = $this->getModel();
		if ($this->publicOnly && $orm->getPublicOnly()) {
			return $this->getPublicOnlyWhereParams();

		} else {
			return [];
		}
	}

	private function addPublicCond(array $cond1): array
	{
		$cond = $cond1;

		/** @var Orm $orm */
		$orm = $this->getModel();
		if ($this->publicOnly && $orm->getPublicOnly()) {
			if (isset($cond1[0]) && ($cond1[0] === ICollection::OR || $cond1[0] === ICollection::AND)) {
				$cond = [
					ICollection::AND,
					$this->getPublicOnlyWhere(),
					$cond1,
				];
			} else {
				$cond = array_merge($cond1, $this->getPublicOnlyWhere());
			}
		}

		return $cond;
	}

	private function getNowDateTime(): DateTimeImmutable
	{
		$now = new DateTimeImmutable();
		return $now->setTime((int) $now->format('H'), (int) $now->format('i'), 0, 0);
	}

	abstract public function getPublicOnlyWhereParams(): array;

}
