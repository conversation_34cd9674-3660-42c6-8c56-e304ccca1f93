<?php declare(strict_types=1);

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use App\Model\Orm\JsonContainer; //phpcs:ignore

/**
 * @property int $id {primary}
 * @property string $extId {default null}
 * @property string $reviewData {container JsonContainer}
 * @property DateTimeImmutable $date
 * @property DateTimeImmutable $createdAt {default now}
 * @property string $source {enum self::SOURCE_*}
 * @property string $importCode {default null}
 * @property DateTimeImmutable|null $syncTime {default null}
 * @property string|null $syncChecksum {default null}
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 */
abstract class ShopReview extends Entity
{
	public const SOURCE_HEUREKA = 'heureka';
	public const SOURCE_ZBOZI = 'zbozi';
	public const SOURCE_GOOGLE = 'google';

}
