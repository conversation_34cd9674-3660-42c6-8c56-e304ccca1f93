<?php
declare(strict_types = 1);

namespace App\Model;

use DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\HasMany;

/**
 * @property int $id                      {primary}
 * @property int|null $extId                {default null}
 * @property string $name
 * @property string $perex
 * @property string $url
 * @property DateTimeImmutable $date    {default now}
 * @property DateTimeImmutable $updated
 * @property string $author
 * @property string $source {enum self::SOURCE_*}
 *
 * RELATIONS
 * @property null|LibraryImage $libraryImage {m:1 LibraryImage, oneSided=true}
 * @property HasMany<Product> $products {m:m Product::$magazineReviews, isMain=true, cascade=[persist, remove]}
 *
 * VIRTUAL
 * @property Product|null $firstProduct {virtual}
 */
abstract class Review extends Entity
{
	protected const SOURCE_HEUREKA = 'heureka';

	public function getterFirstProduct(): ?Product
	{
		/** @var Product|null $product */
		$product = $this->products->toCollection()->limitBy(1)->fetch();

		return $product;
	}
}
