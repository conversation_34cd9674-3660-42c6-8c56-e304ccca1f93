<?php

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property int $id {primary}
 * @property Product $product {m:1 Product::$reviews}
 * @property User|null $user {m:1 User::$reviews}
 * @property int $isWebMaster {default 0}
 * @property int $isMain {default 0}
 * @property string|null $importCode
 * @property string|null $name
 * @property string|null $email
 * @property string|null $text
 * @property string|null $pros
 * @property string|null $cons
 * @property int|null $stars
 * @property int|null $priceRatio
 * @property int|null $qualityRatio
 * @property int|null $recommend
 * @property int $public {default 0}
 * @property string|null $answerPerson
 * @property DateTimeImmutable|null $answerDate
 * @property string|null $answerText
 * @property DateTimeImmutable|null $publicTime
 * @property DateTimeImmutable|null $date {default 'now'}
 *
 * @property int|null $userId {virtual}
 * @property string|null $dateString {virtual}
 *
 * @property Mutation|null $mutation {m:1 Mutation::$reviews} {default 1}
 */
class ProductReview extends \Nextras\Orm\Entity\Entity
{

	protected function getterDateString(): ?string
	{
		return $this->date?->format("Y-m-d H:i:s");
	}

	protected function getterUserId(): ?int
	{
		return $this->user?->id;
	}

}
