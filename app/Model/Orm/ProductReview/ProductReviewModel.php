<?php
/**
 * Created by PhpStorm.
 * User: vojta
 * Date: 24.11.17
 * Time: 21:02
 */

namespace App\Model;


use Nextras\Dbal\Utils\DateTimeImmutable;

class ProductReviewModel
{
	function __construct(
		private Orm $orm,
	) {}


	public function updateReview(ProductReview $productReview, ?int $stars, ?string $text): void
	{
		$this->orm->productReview->persistAndFlush($productReview);
		$productReview->stars = $stars;
		$productReview->text = $text;
		$this->recalculateCache($productReview->product);
	}

	public function addReview(Product $product, $name, $date, $stars, $text, $email, $isWebMaster, $userId, $recommend,
		?string $pros = null, ?string $cons = null, $isMain = 0, ?string $importCode = null): void
	{
		$productReview = new ProductReview();

		$productReview->product = $product;
		$productReview->name = $name;
		$productReview->email = $email;
		$productReview->stars = $stars;
		$productReview->date = $date;
		$productReview->text = $text;
		$productReview->recommend = $recommend;
		$productReview->isWebMaster = $isWebMaster;
		$productReview->isMain = $isMain;
		$productReview->public = 0;
		$productReview->pros = $pros;
		$productReview->cons = $cons;
		$productReview->importCode = $importCode;

		if ($userId) {
			$productReview->userId = $userId;
		}

		$this->orm->productReview->persistAndFlush($productReview);
	}


	private function recalculateCache(Product $product): void
	{
		$mapper = $this->orm->productReview->getMapper();
		\assert($mapper instanceof ProductReviewMapper);
		$statistic = $mapper->getStatistic($product)->fetch();

		$product->reviewAverage = $statistic->average;
		$this->orm->product->persistAndFlush($product);
	}


}
