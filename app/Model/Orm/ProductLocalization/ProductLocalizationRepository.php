<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Orm\Repository\Repository;
use SuperKoderi\hasPublicParameterTrait;
use SuperKoderi\HasSimpleSave;

/**
 * @method ProductLocalization|null getById($id)
 * @method ProductLocalization save(?ProductLocalization $entity, array $data)
 */
final class ProductLocalizationRepository extends Repository
{

	use hasPublicParameterTrait;
	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [ProductLocalization::class];
	}

	public function getPublicOnlyWhereParams(): array
	{
		/** @var Orm $orm */
		$orm = $this->getModel();
		$ret = [
			'public' => 1,
			'mutation' => $orm->getMutation(),
		];

		$now = $this->getNowDateTime();

		$ret['product->publicFrom<='] = $now;
		$ret['product->publicTo>='] = $now;

		return $ret;
	}

}
