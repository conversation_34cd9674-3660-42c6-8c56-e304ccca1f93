<?php


namespace App\Model;


class ProductLocalizationModel
{
	/**
	 * @var ProductLocalizationRepository
	 */
	private ProductLocalizationRepository $repository;
	/**
	 * @var ProductTreeRepository
	 */
	private ProductTreeRepository $productTreeRepository;

	public function __construct(ProductLocalizationRepository $repository,
								ProductTreeRepository $productTreeRepository)
	{
		$this->repository = $repository;
		$this->productTreeRepository = $productTreeRepository;
	}


	public function create(Mutation $mutation, Product $product): void
	{
		$productLocalization = new ProductLocalization();
		$productLocalization->mutation = $mutation;

		$product->productLocalizations->add($productLocalization);
		$this->repository->persistAndFlush($productLocalization);
	}


	public function delete(Mutation $mutation, Product $product): void
	{
		$productLocalization = $this->repository->getBy([
			'product' => $product,
			'mutation' => $mutation,
		]);


		if ($productLocalization) {
			$this->repository->removeAndFlush($productLocalization);
		}
	}

	public function attachTo(ProductLocalization $productLocalization, array $treeIds = []): void
	{
		$product = $productLocalization->product;
		$mutation = $productLocalization->mutation;

		$collectionToDelete = $this->productTreeRepository->findBy([
			'product' => $product,
			'tree->rootId' => $mutation->rootId,
			'tree!=' => $treeIds
		]);

		foreach ($collectionToDelete as $item) {
			$this->productTreeRepository->removeAndFlush($item);
		}

		foreach ($treeIds as $key=>$treeId) {
			$entity = $this->productTreeRepository->getBy([
				'product' => $product,
				'tree' => $treeId
			]);

			if (!$entity) {
				$entity = new ProductTree();
				$this->productTreeRepository->attach($entity);
			}

			$entity->product = $product;
			$entity->tree = $treeId;
			$entity->sort = $key;
			$this->productTreeRepository->persistAndFlush($entity);
		}
	}
}
