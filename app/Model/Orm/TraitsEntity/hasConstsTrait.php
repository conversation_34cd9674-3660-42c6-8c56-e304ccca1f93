<?php

namespace SuperKoderi;

use Nette\Utils\Strings;
use ReflectionClass;

trait hasConstsTrait
{
	/**
	 * @param string $prefix
	 * @param string $translationPrefix podpora pro preklady, kdyz se z konstant sestavuje pole pro select a chceme hodnotu pouzit jako lang prekladu
	 * @return array
	 */
	static public function getConstsByPrefix(string $prefix, string $translationPrefix = ''): array
	{
		$classObject = new ReflectionClass(__CLASS__);
		$consts = [];

		foreach ($classObject->getConstants() as $key => $value) {
			if (Strings::startsWith($key, $prefix)) {
				$consts[$value] = $translationPrefix . $value;
			}
		}

		return $consts;
	}
}
