<?php


namespace App\Model;


use App\Tratis\Orm\hasCamelCase;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class AttachedFileMapper extends DbalMapper
{
	use hasCamelCase;
	protected $tableName = 'tree_file';

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);
		$conventions->setMapping('parent', 'parentId');
		return $conventions;
	}
}
