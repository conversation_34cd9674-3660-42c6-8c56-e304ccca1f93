<?php


namespace SuperKoderi\BucketFilter;


use App\Model\Product;
use Nette\SmartObject;
use Nextras\Orm\Collection\ICollection;


/**
 * @property-read array|int[] $itemIds
 * @property ICollection $items
 * @property int $count
 * @property int $totalCount
 */
class Result
{
	use SmartObject;

	private ICollection $items;

	private int $count;

	private int $totalCount;


	protected function setItems(ICollection $items): void
	{
		$this->items = $items;
	}

	protected function setCount(int $count): void
	{
		$this->count = $count;
	}

	protected function setTotalCount(int $totalCount): void
	{
		$this->totalCount = $totalCount;
	}

	protected function getItemIds(): array
	{
		$ids = [];

		foreach ($this->items as $item) {
			assert($item instanceof Product);
			$ids[] = $item->id;
		}

		return $ids;
	}

	protected function getItems(): ICollection
	{
		return $this->items;
	}

	protected function getCount(): int
	{
		return $this->count;
	}

	protected function getTotalCount(): int
	{
		return $this->totalCount;
	}

}
