<?php declare(strict_types = 1);

namespace SuperKoderi;

use App\Model\CatalogTree;
use App\Model\CommonTree;
use App\Model\Mutation;
use App\Model\Orm;
use App\Model\Tree;

/**
 * @property-read CommonTree $lostPassword
 * @property-read CommonTree $resetPassword
 * @property-read CommonTree $step1
 * @property-read CommonTree $step2
 * @property-read CommonTree $step3
 * @property-read CatalogTree $eshop
 * @property-read CatalogTree $eshopVines
 * @property-read CommonTree $search
 * @property-read CommonTree $basket
 * @property-read CommonTree $blog
 * @property-read CommonTree $compare
 * @property-read CommonTree $userSection
 * @property-read CommonTree $userLogin
 */
class Pages
{

	private Orm $orm;

	private ConfigService $configService;

	private array $cache = [];

	private ?array $uidList = null;

	private Mutation $mutation;

	public function __construct(Orm $orm, ConfigService $configService, Mutation $mutation)
	{
		$this->configService = $configService;
		$this->orm = $orm;

		$this->mutation = $mutation;
	}


	public function __get(string $uid): Tree
	{
		if (!isset($this->cache[$this->mutation->getRealRootId() . '-' . $uid])) {
			if ($uid === 'title') {
				$ret = $this->orm->tree->getById($this->mutation->getRealRootId());
			} else {
				$cond = [
				'rootId' => $this->mutation->getRealRootId(),
						'uid' => $uid,
				];
				$cond = array_merge($cond, $this->orm->tree->getPublicOnlyWhere());
				$ret = $this->orm->tree->getBy($cond);
			}

			if (!$ret) {
				// object not found by UID
				if ($this->configService->getParam('isDev')) {
					trigger_error('Cant find Page by UID ' . $uid, E_USER_NOTICE);
				}

				$ret = new CommonTree();
				$this->orm->tree->attach($ret);
			}

			$this->cache[$this->mutation->getRealRootId() . '-' . $uid] = $ret;
		}

		return $this->cache[$this->mutation->getRealRootId() . '-' . $uid];
	}


	public function __isset(string $name): bool
	{
		if ($this->uidList === null) {
			$this->uidList = $this->orm->tree->findBy([
				'uid!=' => '',
				'rootId' => $this->mutation->getRealRootId(),
			])->fetchPairs('uid', 'uid');
		}

		return isset($this->uidList[$name]);
	}

}




interface IPagesFactory
{

	function create(Mutation $mutation): Pages;

}
