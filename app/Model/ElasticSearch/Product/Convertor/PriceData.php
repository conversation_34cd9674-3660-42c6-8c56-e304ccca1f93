<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\PriceLevel;
use App\Model\PriceLevelModel;
use App\Model\Product;
use SmartEmailing\Api\Model\Price;

class PriceData implements Convertor
{

	public function __construct(
		private PriceLevelModel $priceLevelModel,
	)
	{
	}

	function convert(Product $product): array
	{
		$mutation = $product->getMutation();
		$data = [];

		$priceLevels = $this->priceLevelModel->getAllPriceLevel();

		foreach ($mutation->states as $state) {
			foreach ($priceLevels as $priceLevel) {
				assert($priceLevel instanceof PriceLevel);
				$nonZeroPricesVat = [];

				$someVariantHasSupply = false;
				foreach ($product->activeVariants as $variant) {
					$someVariantHasSupply = $variant->isInStock;
					if ($someVariantHasSupply) {
						break;
					}
				}

				$firstPriceDiscount = null;
				foreach ($product->activeVariants as $variant) {

					if ($variant->isInStock || !$someVariantHasSupply) {

						if ($priceswithVat = $variant->priceWithVat($mutation, $priceLevel, $state, true)) {
							$nonZeroPricesVat[] = $priceswithVat;
						}


					}
				}

				$data['statePricesWithVat'][$state->code][$priceLevel->type] = [];
				if ($nonZeroPricesVat !== []) {
					$data['statePricesWithVat'][$state->code][$priceLevel->type] = $nonZeroPricesVat;
				}

				$data['isInDiscount'][$state->code][$priceLevel->type] = (bool)$product->isAction;
			}
		}

		$productLocalization = $product->getLocalization($mutation);
		$data['isPublic'] = (bool)$productLocalization?->public;

		return $data;
	}

}
