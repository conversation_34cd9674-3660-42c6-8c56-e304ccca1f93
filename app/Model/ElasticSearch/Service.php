<?php declare(strict_types=1);

namespace App\Model\ElasticSearch;


use App\Model\EsIndex;
use App\Model\Orm;
use Elastica\Document;
use Elastica\Exception\NotFoundException;
use Elastica\Exception\ResponseException;
use Elastica\Response;
use LogicException;
use SuperKoderi\MutationHolder;

class Service
{
	public function __construct(
		private Orm $orm,
		private MutationHolder $mutationHolder,
	)
	{
	}

	public function replaceDoc(EsIndex $esIndex, Entity $elasticEntity): Response
	{
		try {
			return $this->updateDoc($esIndex, $elasticEntity);
		} catch (ResponseException) {
			return $this->createDoc($esIndex, $elasticEntity);
		}
	}

	public function createDoc(EsIndex $esIndex, Entity $elasticEntity): Response
	{
		return $esIndex->index->addDocument(
			$this->getDocument($elasticEntity, $esIndex)
		);
	}

	public function updateDoc(EsIndex $esIndex, Entity $elasticEntity): Response
	{
		return $esIndex->index->updateDocument(
			$this->getDocument($elasticEntity, $esIndex)
		);
	}

	public function deleteDoc(EsIndex $esIndex, Entity $elasticEntity): ?Response
	{
		try {
			return $esIndex->index
				->deleteById($elasticEntity->getId());
		} catch (NotFoundException) {
			return null;
		}
	}


	protected function getDocument(Entity $elasticEntity, EsIndex $esIndex): Document
	{
		$originalMutationFromOrm = null;
		try {
			$originalMutationFromOrm = $this->orm->getMutation();
		} catch (LogicException) {}

		$originalMutationFromHolder = $this->mutationHolder->getMutation();

		$this->orm->setMutation($esIndex->mutation);
		$this->mutationHolder->setMutation($esIndex->mutation);

		$newEsDocument = new Document(
			$elasticEntity->getId(),
			$elasticEntity->getData($esIndex->mutation)
		);

		if ($originalMutationFromOrm !== null) {
			$this->orm->setMutation($originalMutationFromOrm);
		}
		if ($originalMutationFromHolder !== null) {
			$this->mutationHolder->setMutation($originalMutationFromHolder);
		}

		return $newEsDocument;
	}
}
