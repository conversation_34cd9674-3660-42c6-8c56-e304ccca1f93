<?php //declare(strict_types=1);
//
//namespace App\Model;
//
//use App\Exceptions\SkippedException;
//use App\Model\ElasticSearch\All\Facade;
//use App\Model\Erp\Importer\IProductImporter;
//use App\Model\Erp\ImportProcessor\BaseImportProcessor;
//use App\Model\Erp\ImportProcessor\ICleanCache;
//use App\Model\Sentry\SentryLogger;
//use App\Utils\DateTime;
//use Contributte\Monolog\LoggerManager;
//use DateTimeImmutable;
//use Nette\DI\Container;
//use Nette\Utils\ArrayHash;
//use Nette\Utils\Json;
//use Nette\Utils\Strings;
//use Psr\Log\LoggerInterface;
//use SuperKoderi\ConfigService;
//use SuperKoderi\MutationHolder;
//use SuperKoderi\MutationsHolder;
//
//class HeurekaReviewImportProcessor extends BaseImportProcessor implements ICleanCache
//{
//	protected array $allParameters;
//
//	protected bool $updateInternal;
//
//	public function __construct(
//		Container $container,
//		Orm $orm,
//		ConfigService $configService,
//		LoggerManager $loggerManager,
//		SentryLogger $sentryLogger,
//		IProductImporter $productImporter,
//		private ProductReviewModel $productReviewModel,
//		private Facade $allElasticFacade,
//		private MutationsHolder $mutationsHolder,
//		private MutationHolder $mutationHolder,
//	)
//	{
//		parent::__construct($container, $orm, $configService, $loggerManager, $sentryLogger, $productImporter);
//		$this->updateInternal = $this->config['products']['updateInternalValues'];
//	}
//
//
//	public function readHeurekaReview(ImportCache $importCache, LoggerInterface $logger): void
//	{
//		assert($importCache->type === ImportCache::TYPE_HEUREKA_REVIEW);
//
//		$defaultMutation = $this->mutationsHolder->getDefault();
//		$this->mutationHolder->setMutation($defaultMutation);
//		$this->orm->setMutation($defaultMutation);
//
//		if ($this->orm->importCache->findBy([
//				'type'   => $importCache->type,
//				'status' => [ImportCache::STATUS_READY],
//				'extId'  => $importCache->extId,
//			])->countStored() > 0) {
//			throw new SkippedException('Newer import pending.');
//		}
//
//		$productReview = $this->orm->productReview->getBy(['importCode' => $importCache->extId]);
//
//		if ($productReview?->syncChecksum === $this->createChecksum($importCache->data)) {
//			throw new SkippedException('Checksum is same.');
//		}
//
//		$this->doImport($importCache, $defaultMutation, $productReview);
//	}
//
//	private function createChecksum(ArrayHash $data): string
//	{
//		return md5(Json::encode($data));
//	}
//
//	private function findProduct(ImportCache $importCache): ?Product
//	{
//		if ($product = $this->orm->product->getByExtCode($importCache->data->productno)) {
//			return $product;
//		}
//
//		if ($productVariant = $this->orm->productVariant->getBy(['ean' => $importCache->data->ean])) {
//			return $productVariant->product;
//		}
//
//		return null;
//	}
//
//	public function getArrayOfString(?string $string): array
//	{
//		if (empty($string)) {
//			return [];
//		}
//
//		$strings = explode(PHP_EOL, $string);
//
//		foreach ($strings as $index => &$pro) {
//			$strings[$index] = Strings::trim($pro);
//		}
//
//		return $strings;
//	}
//
//	private function doImport(ImportCache $importCache, Mutation $mutation, ?ProductReview $productReview = null): void
//	{
//		$productEntity = $this->findProduct($importCache);
//		if ($productEntity === null) {
//			throw new SkippedException('Product not found.');
//		}
//
//		foreach ($importCache->data->reviews as $review) {
//			$this->productReviewModel->addReview(
//				$productEntity,
//				$importCache->data->product_name,
//				DateTimeImmutable::createFromInterface(DateTime::from($review->unix_timestamp)), // @phpstan-ignore-line
//				$review->rating,
//				$review->summary,
//				$importCache->data->email,
//				true,
//				null,
//				$review->recommends,
//				$review->pros,
//				$review->cons,
//				0,
//				$importCache->extId
//			);
//		}
//
//		$productEntity->reviewAverage = $this->orm->productReview->getStatistic($productEntity)->fetch()->average ?? 0.0;
//		$this->orm->persistAndFlush($productEntity);
//	}
//
//	/**
//	 * @throws Exception
//	 */
//	public function cleanCache(): array
//	{
//		return $this->cleanOldCacheByDate('products', ImportCache::TYPE_HEUREKA_REVIEW);
//	}
//
//}
