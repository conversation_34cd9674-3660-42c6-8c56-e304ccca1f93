<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Model\Erp\Exception\LoggedException;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\ArrayHash;

class HeurekaShopReviewConnector extends XmlConnector implements ReadCollection
{
	private const LoggerType = 'heurekaShopReview';

	protected function startup(LoggerManager $loggerManager): void
	{
		if ($this->config->connectors->rest->verboseLog) {
			$this->logger = $loggerManager->get(self::LoggerType);
		}
	}

	public function getRows(Query $query): ArrayHash
	{
		return new ArrayHash();
	}

	public function getJsonSource(Query $query): ?string
	{
		return null;
	}

	/**
	 * @throws LoggedException
	 */
	public function getXmlSource(): string
	{
		if (!$xmlUrl = $this->configService->get('mutations', 'cs', 'heurekaShopReviewXmlUrl')) {
			throw new LoggedException('Heureka shop review url missing.');
		}
		return $this->downloadXml($xmlUrl);
	}

	public function getData(string $action, array $params = []): ArrayHash
	{
		return new ArrayHash();
	}

	public function putData(string $action, array $data): ArrayHash
	{
		return new ArrayHash();
	}
}
