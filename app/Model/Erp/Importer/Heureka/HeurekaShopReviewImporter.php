<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\DTO\ShopReview\HeurekaShopReviewDto;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\ImportCache;
use Exception;
use Nette\Utils\FileSystem;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Prewk\XmlStringStreamer;
use Prewk\XmlStringStreamer\Parser\StringWalker;
use Prewk\XmlStringStreamer\Stream\File;
use Throwable;

class HeurekaShopReviewImporter extends BaseImporter
{

	/**
	 * @throws Exception // Class: File
	 */
	public function import(string $importType): Result
	{

		$batchSize = $this->config->review->import->batchLimit ?? 10;
		$createdTime = new DateTimeImmutable();

		$xmlFile = $this->connector->getXmlSource();
		$count = 0;

		$this->logger->info('Import start');
		$deleted = $this->importCacheMapper->deleteByStatus([ImportCache::STATUS_IMPORTED, ImportCache::STATUS_SKIPPED/*, ImportCache::STATUS_ERROR*/], ImportCache::TYPE_HEUREKA_SHOP_REVIEW);
		$this->logger->info('Import cache table flush', ['deleted' => $deleted]);

		$stream = new File($xmlFile, 1024);
		$parser = new StringWalker();

		$streamer = new XmlStringStreamer($parser, $stream);

		$inserting = [];
		$insertCallback = function () use (&$inserting) {
			try {
				$this->importCacheMapper->insert($inserting);
			} catch (Throwable $e) {
				$this->logger->error('Inserting rows failed: ' . $e->getMessage());
			}
			$inserting = [];
		};

		while ($node = $streamer->getNode()) {
			$xmlNode = simplexml_load_string((string) $node);
			try {
				assert($xmlNode instanceof \SimpleXMLElement);
				$dto = HeurekaShopReviewDto::fromXmlElement($xmlNode);
				$data = Json::encode($dto);

				$rowData     = [
					'type'        => $importType,
					'extChecksum' => md5($data),
					'data'        => $data,
					'extId'       => $xmlNode->rating_id,
					'createdTime' => $createdTime,
					'status'      => ImportCache::STATUS_READY,
				];
				$inserting[] = $rowData;

				if ((count($inserting) % $batchSize) === 0) {
					$insertCallback();
				}

				$count++;
			} catch (JsonException) {
				// if JSON is invalid continue to next row
			}
		}

		$result = new Result();
		$result->count = $count;

		$this->logger->info('Import end.', ['imported' => $count]);

		try {
			FileSystem::delete($xmlFile);
			$this->logger->info('XML file deleted.');
		} catch (Throwable $e) {
			$this->logger->error('XML file could not be deleted.', ['exception' => $e]);
		}

		return $result;
	}

}
