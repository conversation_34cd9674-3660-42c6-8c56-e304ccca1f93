<?php declare(strict_types = 1);

namespace SuperKoderi\Admin\Components\Voucher\Form;

use App\Model\Voucher;
use App\Model\User;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use SuperKoderi\ConfigService;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasOrmTrait;
use SuperKoderi\Translator;

/**
 * @property-read DefaultTemplate $template
 */
class ShellForm extends Control
{

	use hasOrmTrait;
	use hasConfigServiceTrait;

	private User $userEntity;

	private Translator $translator;
	private ConfigService $configService;

	public function __construct(
		User $userEntity,
		ConfigService $configService,
		Translator $translator
	)
	{
		$this->userEntity = $userEntity;
		$this->translator = $translator;
		$this->configService = $configService;
	}



	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$mutations = $this->orm->mutation->findAll()->fetchPairs('id', 'name');
		$form->addSelect('mutation', 'select_mutation', $mutations);
		$types = $this->configService->get('voucherTypes');
		$kinds = $this->configService->get('voucherKinds');
		$form->addSelect('type', 'label_voucherType', $types)->setRequired('Type is required');

		$form->addSelect('kind', 'label_voucherKind', $kinds)->setRequired('Type is required');

		$form->addSubmit('send', 'send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(Form $form): void
	{
		bd($form->errors);
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		$voucher = new Voucher();
		$this->orm->voucher->attach($voucher);
		$voucher->mutation = $values->mutation;
		$voucher->created = $this->userEntity->id;
		$voucher->type = $values->type;
		$voucher->kind = $values->kind;
		$voucher->discountAmount = 0;
		$voucher->discountPercent = 0;

		$this->orm->persistAndFlush($voucher);

		$this->presenter->redirect('edit', ['id' => $voucher->id]);
	}

}


interface IShellFormFactory
{

	public function create(User $userEntity): ShellForm;

}
