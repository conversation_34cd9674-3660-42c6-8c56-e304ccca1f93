<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form;

use App\Model\ElasticSearch\Common\Facade;
use App\Model\Mutation;
use App\Model\Orm;
use App\Model\Routable;
use App\Model\User;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\AdminModule\Components\Form\FormData\BaseFormData;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use ArrayIterator;
use Closure;
use LogicException;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\CustomField\SuggestUrls;
use SuperKoderi\LinkFactory;
use SuperKoderi\Translator;
use SuperKoderi\TranslatorDB;

class Form extends Control
{

	public const TEMPLATE_PARTS_DIR = __DIR__ . '/parts';

	private const INDEXABLE_IN_COMMON = [];  //AuthorLocalization::class, ..

	/** @var ICollection|Mutation[] */
	private ICollection $mutations;

	public function __construct(
		private EntityLocalizationFacade $entityLocalizationFacade,
		private LocalizationEntity $entityLocalization,
		private User $userEntity,
		private SuggestUrls $urls,
		private Translator $translator,
		private TranslatorDB $translatorDB,
		private Builder $formBuilder,
		private Handler $formHandler,
		private LinkFactory $linkFactory,
		private Orm $orm,
		private ShellFormFactory $shellFormFactory,
		private Facade $commonElasticFacade,
		private \App\Model\ElasticSearch\All\Facade $allElasticFacade,
	)
	{
		$this->onAnchor[] = Closure::fromCallable([$this, 'init']);
	}


	private function init(): void
	{
		$this->mutations = $this->orm->mutation->findAll();
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);

		$template->headerItems = new ArrayIterator();

		if ($this->entityLocalization instanceof Routable) {
			$template->linksToFront = $this->linkFactory->linkTranslateToNette($this->entityLocalization, ['show' => 1, 'mutation' => $this->entityLocalization->getMutation()]);
		} else {
			$template->linksToFront = null;
		}

		$template->parent = $this->entityLocalization->getParent();
		$template->entityLocalization = $this->entityLocalization;
		$template->mutation = $this->entityLocalization->getMutation();
		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link(':Admin:File:upload');

		$template->otherMutations = $this->entityLocalization->getParent()->getLocalizations()->findBy(['mutation!=' => $this->entityLocalization->getMutation()]);
		$activeMutationLangCodes = [];
		foreach ($this->entityLocalization->getParent()->getLocalizations() as $localization) {
			assert($localization instanceof LocalizationEntity);
			$activeMutationLangCodes[] = $localization->getMutation()->langCode;
		}

		$template->missingMutations = $this->mutations->findBy(['langCode!=' => $activeMutationLangCodes]);

		$template->userEntity = $this->userEntity;

		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}


	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$form = new \Nette\Application\UI\Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		if ($this->entityLocalization instanceof LocalizationEntity
		) {
			$this->formBuilder->build($form, $this->entityLocalization);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(\Nette\Application\UI\Form $form): void
	{
		bd($form->errors);
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(\Nette\Application\UI\Form $form, BaseFormData $data): void
	{
		if ($this->entityLocalization instanceof LocalizationEntity
		) {
			$this->formHandler->handle($this->entityLocalization, $this->userEntity, $data);

			if (in_array(get_class($this->entityLocalization), self::INDEXABLE_IN_COMMON)) {
				$this->commonElasticFacade->updateNow($this->entityLocalization, $this->entityLocalization->getMutation());
			}

			$this->allElasticFacade->updateNow($this->entityLocalization);

		}

		$this->presenter->redirect('edit', ['id' => $this->entityLocalization->getId()]);
	}


	public function handleDelete(): void
	{
		if (in_array(get_class($this->entityLocalization), self::INDEXABLE_IN_COMMON)) {
			$this->commonElasticFacade->deleteNow($this->entityLocalization, $this->entityLocalization->getMutation());
		}

		$this->allElasticFacade->delete($this->entityLocalization);

		$this->entityLocalizationFacade->remove($this->entityLocalization);
		$this->presenter->redirect('default');
	}


	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create($this->entityLocalization->getParent(), $this->entityLocalizationFacade);
	}

}
