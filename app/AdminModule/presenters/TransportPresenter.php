<?php

namespace AdminModule;

use App\Model\MutationTransports;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Http\SessionSection;
use Nette\Utils\ArrayHash;
use SuperKoderi\MoneyHelper;
use SuperKoderi\StringHelper;
use Tracy\Debugger;

class TransportPresenter extends BasePresenter
{

	private SessionSection $filterSession;

	protected ?MutationTransports $object;

	protected function startup(): void
	{
		parent::startup();

		$this->filterSession = $this->session->getSection('transportFilter');
		if (!isset($this->filterSession->data)) {
			$this->filterSession->data = null;
		}
	}

	public function actionDefault(): void
	{
		$this->template->transports = $this->orm->mutationTransports
			->findByFilter($this->filterSession->data)
			->orderBy('mutation');
	}

	public function renderDefault(): void
	{
		$this->template->filterSession = $this->filterSession;
	}

	public function actionEdit(int $id): void
	{
		$this->object = $this->orm->mutationTransports->getById($id);

		if (!$this->object) {
			$this->redirect("default");
		}
	}


	protected function createComponentEditForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);
		$form->addText('priceDPH', 'priceDPH')->setRequired();
		$form->addText('freeFrom', 'label_transportFreeFrom')->setRequired();

		$form->addText('deliveryDayFrom', 'transport_deliveryDayFrom')->setRequired();
		$form->addText('deliveryDayTo', 'transport_deliveryDayTo');

		// hodina pro kazdy sklad
		$stockskContainer = $form->addContainer('stocks');
		foreach ($this->orm->stock->findAll() as $stock) {
			$input = $stockskContainer->addText($stock->alias, $stock->name)->setTranslator(null)->setRequired();
			$stockAlias = $stock->alias;

			if (isset($this->object->deliveryHourStock->$stockAlias)) {
				$input->setDefaultValue($this->object->deliveryHourStock->$stockAlias);
			}
		}

		if ($this->object) {
			$form->setDefaults($this->object->getFormData($form));
		}

		$form->addSubmit('save', 'Save');

		$form->onSuccess[] = [$this, 'editFormSucceeded'];
		return $form;
	}


	public function editFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		try {
			$valuesAll = $form->getHttpData();
			$this->object->priceDPH = MoneyHelper::getNormalizedFloat($values->priceDPH, 2);
			$this->object->freeFrom = MoneyHelper::getNormalizedFloat($values->freeFrom, 2);
			$this->object->deliveryDayFrom = (int)$values->deliveryDayFrom;
			$this->object->deliveryDayTo = StringHelper::intTonull($values->deliveryDayTo);
			$this->object->deliveryHourStock = $values->stocks;

			$this->orm->mutationTransports->persistAndFlush($this->object);

			$this->flashMessage('msg_ok_saved', 'ok');
			if ($this->object) {
				if (isset($valuesAll['saveBack'])) {
					$this->redirect('default');
				}

				if ($this->isAjax()) {
					$this->redrawControl();
				} else {
					$this->redirect('this');
				}
			} else {
				$this->redirect('default');
			}

		} catch (AbortException $e) {
			throw $e;
		} catch (\Throwable $e) {
			Debugger::log($e, \Tracy\ILogger::ERROR);
			$form->addError('msg_operation_failed');
			$this->flashMessage('msg_operation_failed', 'error');
		}
	}


	protected function createComponentFilterForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$form->addSelect('mutation', 'label_mutation', $this->orm->mutation->findAll()->fetchPairs('id', 'name'))
			->setPrompt('all');

		if ($this->filterSession->data) {
			$form->setDefaults($this->filterSession->data);
		}

		$form->addSubmit('save', 'save_button');

		$form->onSuccess[] = [$this, 'filterFormSucceeded'];
		return $form;
	}

	public function filterFormSucceeded(UI\Form $form): void
	{
		$values = $form->getValues();
		$this->filterSession->data = $values;
		$this->redirect('this');
	}

	public function handleClearFilter(): void
	{
		$this->filterSession->data = null;
		$this->redirect('default');
	}
}
