<?php

namespace FrontModule;

use App\Model\Order;
use App\Model\OrderItem;
use App\Model\Product;
use App\Model\ProductVariant;
use Nette\DI\Attributes\Inject;
use SuperKoderi\Components\IMessageForFormFactory;
use SuperKoderi\Components\IPrebasketFactory;
use SuperKoderi\Components\IVariantPickerFactory;
use SuperKoderi\Components\MessageForForm;
use SuperKoderi\Components\Prebasket;
use SuperKoderi\Components\VariantPicker;
use SuperKoderi\EasyMessages;


class PrebasketPresenter extends BasePresenter
{
	#[Inject]
	public IMessageForFormFactory $messageForFormFactory;

	#[Inject]
	public EasyMessages $easyMessages;

	#[Inject]
	public IPrebasketFactory $prebasketFactory;

	#[Inject]
	public IVariantPickerFactory $variantPickerFactory;

	private ?ProductVariant $variant = null;

	private Product $product;

	private int $amount;

	private string $uniqueKey;


	public function actionDefault(): void
	{
		$productId = $this->getParameter('productId');
		$variantId = $this->getParameter('variantId');
		$amount = $this->getParameter('amount');

		$this->setObject($this->orm->tree->getByUid('preprebasket'));

		$product = $this->orm->product->getById($productId);

		if (!$product) {
			$this->flashMessage('invalid_products', 'error');
			$this->redirect($this->mutation->pages->eshop);
		}

		$this->product = $product;
		$this->amount = $amount;

		$productLocalization = $product->getLocalization($this->mutation);

		$variant = null;
		if ($variantId) {
			$variant = $this->orm->productVariant->getActiveVariant($variantId);
		} else {
			$allActiveVariants = $product->activeVariants->fetchAll();
			if (count($allActiveVariants) == 1) {
				$variant = $allActiveVariants[0];
			}
		}


		$errorMsg = null;
		if ($variant) {
			if ($variant->priceWithVat($this->mutation, $this->priceLevel, $this->currentState) <= 0) {
				$this->flashMessage('invalid_product', 'error');
				$this->redirect($productLocalization, ['v' => $variant->id]);
			}

			if ($variant->totalSupplyCount <= 0) {
				$this->flashMessage('invalid_product_supply_count', 'error');
				$this->redirect($productLocalization, ['v' => $variant->id]);
			}


			$canAdd = true;
			if ($variant->product->isVoucher) {
				$nonVoucherProductOrderItems = $this->basket->getFutureOrder()->items->toCollection()->findBy([
					'type' => [OrderItem::TYPE_PRODUCT, OrderItem::TYPE_SET],
					'subType!=' => 'voucher',
				]);
				if ($nonVoucherProductOrderItems->count() > 0) {
					$errorMsg = 'basket_has_product_cant_add_voucher';
					$canAdd = false;
				}

 			} else {
				$voucherProductOrderItems = $this->basket->getFutureOrder()->items->toCollection()->findBy([
					'type' => OrderItem::TYPE_PRODUCT,
					'subType=' => 'voucher',
				]);
				if ($voucherProductOrderItems->count() > 0) {
					$errorMsg = 'basket_has_voucher_cant_add_product';
					$canAdd = false;
				}
			}


			$this->variant = $variant;
			if ($canAdd) {
				$this->uniqueKey = $this->basket->addProductVariant($variant, $amount, $this->params);
			}

		}

		$this->template->errorMsg = $errorMsg;
	}


	public function renderDefault(): void
	{
		$this->template->variant = $this->variant;
		$this->template->product = $this->product;

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}


	protected function createComponentPrebasket(): Prebasket
	{
		return $this->prebasketFactory->create($this->variant, $this->amount, $this->uniqueKey, $this->priceLevel, $this->currentState);
	}


	protected function createComponentVariantPicker(): VariantPicker
	{
		return $this->variantPickerFactory->create($this->product, $this->amount, $this->priceLevel, $this->currentState);
	}

}
