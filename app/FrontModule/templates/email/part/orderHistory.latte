
<h2 style="font-size:20px; line-height: 26px; margin:30px 0 20px;">{_order_sum_products_title}</h2>

<table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin:0 auto; padding:0; font-size:14px; font-family: Arial, Helvetica, sans-serif; width:100%; text-align: left;">
	{* Products *}
	<tbody>
	{foreach $data->parentItems as $i}
		<tr>
			{* Obrázek *}
			<td class="sk-pr0 sk-wauto" {if $i->type == 'payment' || $i->type == 'transport'}colspan="2"{/if} style="background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 15px 10px 0; font-size: 12px; vertical-align: middle;{if $i->type != 'payment' && $i->type != 'transport'} width: 50px;{/if}">
				{*$i->type*}
				{if !in_array($i->type, ['payment', 'transport', 'voucher', 'service'])}
					{if $i->variant}
					<a class="sk-hide" href="{$mutation->getBaseUrlWithPrefix()}/{$i->variant->product->getLocalization($mutation)->alias}?v={$i->variant->id}" style="text-decoration: none;">
						{if isset($i->variant->firstImage)}
							{php $img = $i->variant->firstImage->getSize('xxs')}
							<img src="{$mutation->getBaseUrl()}{$img->src}" alt="{$i->name}" width="{$img->width}" height="{$img->height}" style="vertical-align: top; border: none; display: block; max-width: 100%; margin: auto; height: auto;">
						{else}
							<img src="{$mutation->getBaseUrl()}/static/img/noimg.jpg" alt="" width="50" height="50" />
						{/if}
					</a>
{*					{elseif $i->class}*}
{*						<a class="sk-hide" href="{$mutation->getBaseUrlWithPrefix()}{$i->class->alias}" style="text-decoration: none;">*}
{*							{if isset($i->class->firstImage)}*}
{*								{php $img = $i->class->firstImage->getSize('sm')}*}
{*								<img src="{$mutation->getBaseUrl()}/{$img->src}" alt="{$i->name}" *}{*width="{$img->width}" height="{$img->height}"*}{* style="vertical-align: top; border: none; display: block; width: 100%; height: auto;">*}
{*							{else}*}
{*								<img src="{$mutation->getBaseUrl()}/static/img/noimg.jpg" alt="" width="50" height="50" />*}
{*							{/if}*}
{*						</a>*}
					{/if}
				{/if}
			</td>

			{* Název a další údaje *}
			<td style="background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 0; vertical-align: middle;">
				<p style="margin: 0; font-size: 14px; line-height: 21px;">
					{if $i->amount > 1} {$i->amount}x {/if}

					{$i->name} <br>
					<span n:if="$i->variantName"{*style="color: #56626a;"*}>
						{$i->variantName}
					</span>


					{if $i->services->count()}<br>{/if}

					{php $priceExtra = 0}
					{if $i->services}
						{foreach $i->services as $service}
							<br>
							<span class="b-cart-summary__extra u-color-suplementary">
								+ {$service->name}
							</span>
							{php $priceExtra += $service->unitPriceDPH}
						{/foreach}
					{/if}
				</p>
			</td>

			{* Cena *}
			<td style="font-weight: bold; width: 120px; background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 0 10px 15px; vertical-align: middle; text-align: right;">
				<p style="margin: 0; font-size: 14px; line-height: 21px;">
					{$i->totalPriceDPH+($i->amount*$priceExtra)|priceFormat} <br>
				</p>
			</td>
		</tr>


		{*voucher*}
		{if $i->productVouchers && $i->productVouchers->count()}
			{foreach $i->productVouchers as $productVoucher}
				<tr>
					<td colspan="3" style="border: 2px solid #fff;"></td>
				</tr>
				<tr>
					{*
					<td style="background: #fff; border: 1px solid #eee; border-width: 1px 0 2px 1px; border-radius: 3px 0 0 3px; padding: 10px 15px; vertical-align: middle;">
						obrázek
					</td>
					*}
					<td colspan="2" style="background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 15px 10px 0; vertical-align: middle;">
						{$productVoucher->name}
					</td>
					<td style="font-weight: bold; width: 120px; background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 0 10px 15px; vertical-align: middle; text-align: right;">
						{$productVoucher->unitPriceDPH|priceFormat}
					</td>
				</tr>
			{/foreach}
		{/if}

		

		<tr>
			<td colspan="3" style="border: 2px solid #fff;">

			</td>
		</tr>

		{*{if isset($presentProducts[$i->productStringKey])}*}
		{*{/if}*}

		{*{if isset($setPartProducts[$i->productStringKey])}*}
			{*<tr>*}
				{*<td>části setu*}
					{*<ul>*}
						{*{foreach $setPartProducts[$i->productStringKey] as $setPart}*}
							{*{dump $present->product->alias}*}
							{*<li>{$setPart->name}</li>*}
						{*{/foreach}*}
					{*</ul>*}
				{*</td>*}
			{*</tr>*}
		{*{/if}*}
		{* if $i->type == 'set'}Set - {/if}*}
		{*{if $i->type == 'setPart'}Část setu - {/if}*}
		{*{if $i->type == 'present'}Dárek - {/if*}
		{*if isset($setPartProducts[$i->productStringKey])}*}
			{*<tr>*}
				{*<td>části setu*}
					{*<ul>*}
						{*{foreach $setPartProducts[$i->productStringKey] as $setPart}*}
							{*<li>{$setPart->name}</li>*}
						{*{/foreach}*}
					{*</ul>*}
				{*</td>*}
			{*</tr>*}
		{*{/if*}
	{/foreach}

	{* Vouchers *}
	{if $data->vouchers->count()}
		{foreach $data->vouchers as $voucher}
			<tr>
				<td colspan="2" style="background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 15px 10px 0; vertical-align: middle;">
					{_'title_voucher'}: {$voucher->name}
				</td>
				<td style="font-weight: bold; width: 120px; background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 0 10px 15px; vertical-align: middle; text-align: right;">
					{$voucher->unitPriceDPH|priceFormat}
				</td>
			</tr>
			<tr>
				<td colspan="3" style="padding: 0 15px; border: 1px solid #fff;">

				</td>
			</tr>
		{/foreach}
	{/if}

	{* Discounts  *}
	{if $data->discounts->count()}
		{foreach $data->discounts as $discount}
			<tr>
				<td colspan="2" style="background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 15px 10px 0; vertical-align: middle;">
					{$discount->name}
				</td>
				<td style="font-weight: bold; width: 120px; background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 0 10px 15px; vertical-align: middle; text-align: right;">
					{$discount->unitPriceDPH|priceFormat}
				</td>
			</tr>
			<tr>
				<td colspan="3" style="padding: 0 15px; border: 1px solid #fff;">

				</td>
			</tr>
		{/foreach}
	{/if}

	{* Gifts *}
	{php $hasGifts = false}
	{foreach $data->parentItems as $i}
		{if $i->presents && $i->presents->count()}
			{php $hasGifts = true}
		{/if}
	{/foreach}

	{if $hasGifts}
		<tr>
			<td colspan="3">
				<h3>
					{_gifts}
				</h3>
			</td>
		</tr>
		{foreach $data->parentItems as $i}
			{if $i->presents && $i->presents->count()}
				{foreach $i->presents as $present}
					<tr>
						<td colspan="3" style="width: 120px; background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 0; vertical-align: middle;">
							{$present->name}
						</td>
					</tr>
				{/foreach}
			{/if}
		{/foreach}
	{/if}

		<tr n:if="$data->transport">
			<td colspan="2" style="background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 15px 10px 0; vertical-align: middle;">
				<span>{_'order_delivery'}:</span>
				{$data->transport->name}
			</td>
			<td style="font-weight: bold; width: 120px; background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 0 10px 15px; vertical-align: middle; text-align: right;">
				<strong>
					{if $data->transport->unitPriceDPH}
						{$data->transport->unitPriceDPH|priceFormat}
					{else}
						{_'free'}
					{/if}
				</strong>
			</td>
		</tr>
		<tr n:if="$data->payment">
			<td colspan="2" style="background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 15px 10px 0; vertical-align: middle;">
				<span>{_'order_payment'}:</span>
				{$data->payment->name}
			</td>
			<td style="font-weight: bold; width: 120px; background: #fff; border-bottom: 1px solid #e2ceaf; padding: 10px 0 10px 15px; vertical-align: middle; text-align: right;">
				<strong>
					{if $data->payment->unitPriceDPH}
						{$data->payment->unitPriceDPH|priceFormat}
					{else}
						{_'free'}
					{/if}
				</strong>
			</td>
		</tr>
	</tbody>
</table>

{* Celková cena *}
<p style="text-align: right; margin: 15px 0 20px; font-size: 18px; line-height: 24px;">
	<span style="font-size: 14px;">{_'total_price'}: {$data->totalPrice|priceFormat}</span><br>
	{_'total_price_vat'}: <strong>{$data->totalPriceDPH|priceFormat}</strong>
</p>
