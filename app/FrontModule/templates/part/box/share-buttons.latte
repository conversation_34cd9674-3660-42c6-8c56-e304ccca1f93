{default $url =  isset($url) ? $url : null}
<div class="b-share-buttons" n:if="$url">
    <div class="b-share-buttons__title">{_'share_buttons_title'}</div>
    {* <div class="b-share-buttons__sep"></div> *}
    <ul class="b-share-buttons__list">
        <li class="b-share-buttons__item u-hide" data-controller="Clipboard" data-Clipboard-text-value="{$mutation->getBaseUrl()}{$url}">
            <a href="{$url}" class="b-share-buttons__link" data-action="Clipboard#copy" title="{_'clipboard'}" data-action-tooltip="{_'clipboard_active'}">
                {('social-url')|icon}
                <span class="u-vhide">{_'clipboard'}</span>
            </a>
        </li>
        <li class="b-share-buttons__item">
            <a class="b-share-buttons__link" title="Facebook" rel="nofollow noopener" target="_blank" href="https://www.facebook.com/sharer/sharer.php?u={$url}">
                {('social-facebook')|icon}
                <span class="u-vhide">{_'share_buttons_facebook'}</span>
            </a>
        </li>
        {* <li class="b-share-buttons__item">
            <a class="b-share-buttons__link" title="Instagram" rel="nofollow noopener" target="_blank" href="https://www.instagram.com/?url={$url|webalize}">{('social-instagram')|icon}</a>
        </li> *}
        <li class="b-share-buttons__item">
            <a class="b-share-buttons__link" title="Twitter" rel="nofollow noopener" target="_blank" href="https://twitter.com/intent/tweet?url={$url}">
                {('social-twitter')|icon}
                <span class="u-vhide">{_'share_buttons_twitter'}</span>
            </a>
        </li>
    </ul>
</div>