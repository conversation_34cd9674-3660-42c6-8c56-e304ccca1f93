{default $class = null}
{default $direction = null}
{default $menu = null}

<nav n:class="m-social-networks, $class" n:ifcontent>
    <ul n:class="m-social-networks__list, $direction ? 'm-social-networks__list--' . ($direction)" n:inner-foreach="$menu as $key => $item" n:ifcontent>
        <li class="m-social-networks__item">
            <a href="{$item->url}" class="m-social-networks__link" target="_blank" rel="noopener noreferrer">
                {('social-' . $key)|icon}
                <span n:class="$direction == 'vertical' ? m-social-networks__link-text : u-vhide">{$item->text|noescape}</span>
            </a>
        </li>
    </ul>
</nav>