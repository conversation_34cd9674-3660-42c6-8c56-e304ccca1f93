<script>
	(function () {
		var className = document.documentElement.className;
		className = className.replace('no-js', 'js');

		(function() {
			var mediaHover = window.matchMedia('(hover: none), (pointer: coarse), (pointer: none)');
			mediaHover.addListener(function(media) {
				document.documentElement.classList[media.matches ? 'add' : 'remove']('no-hoverevents');
				document.documentElement.classList[!media.matches ? 'add' : 'remove']('hoverevents');
			});
			className += (mediaHover.matches ? ' no-hoverevents' : ' hoverevents');
		})();

		// var supportsCover = 'CSS' in window && typeof CSS.supports === 'function' && CSS.supports('object-fit: cover');
		// className += (supportsCover ? ' ' : ' no-') + 'objectfit';

		// fix iOS zoom issue: https://docs.google.com/document/d/1KclJmXyuuErcvit-kwCC6K2J7dClRef43oyGVCqWxFE/edit#heading=h.sgbqg5nzhvu9
		var ua = navigator.userAgent.toLowerCase();
		var isIOS = /ipad|iphone|ipod/.test(ua) && !window.MSStream;

		if (isIOS === true) {
			var viewportTag = document.querySelector("meta[name=viewport]");
			viewportTag.setAttribute("content", "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no");
		}

		document.documentElement.className = className;
	}());

	// Cookie head script
	(function () {
		var match = document.cookie.match(new RegExp('(^| )CookieConsent=([^;]+)'));
		document.documentElement.dataset.showCookie = true;
		if (match) {
			try {
				var cookieState = JSON.parse(match[2]);
				if(cookieState.id && cookieState.datetime && cookieState.storages) {
					document.documentElement.dataset.showCookie = false;
					window.cookieState = cookieState;
				};
			} catch (error) {}
		}
	}());
</script>
