<div class="b-filters__group">
	<p class="b-filters__title">
		{$filterItem->title}:
	</p>

	<ul class="b-filters__list">
		{if isset($filterItem->entity)}
			{capture $unit}{_'pname_unit_'.$filterItem->entity->id}{/capture}
		{else}
			{capture $unit}{/capture}
		{/if}

		{php $cleanFilterParamCopy = $cleanFilterParam}
		{php $cleanFilterParamCopy['ranges'][$filterItem->name]['min'] = null}
		{php $cleanFilterParamCopy['ranges'][$filterItem->name]['max'] = null}

		{capture $link}{link 'this', filter => $cleanFilterParamCopy}{/capture}
		{php $link = urldecode(htmlspecialchars_decode($link))}

		<li class="b-filters__item">
			<a href="{$link}" class="b-filters__remove" data-naja data-naja-loader="body"{if $linkSeo->hasNofollow($object, ['filter' => $cleanFilterParamCopy])} rel="nofollow"{/if}>
				{if $filterItem->inputValueMin != $filterItem->selectedMin && $filterItem->inputValueMax != $filterItem->selectedMax}
					{$filterItem->selectedMin}{if $unit} {$unit}{/if} - {$filterItem->selectedMax}{if $unit} {$unit}{/if}
				{elseif $filterItem->inputValueMin != $filterItem->selectedMin}
					{_from} {$filterItem->selectedMin}{if $unit} {$unit}{/if}
				{elseif $filterItem->inputValueMax != $filterItem->selectedMax}
					{_to} {$filterItem->selectedMax}{if $unit} {$unit}{/if}
				{/if}
			</a>
		</li>
	</ul>
</div>
