{default $forceOpen = false}
{default $id = false}

<fieldset class="f-filter__group{if $filterItem->isOpen || $forceOpen} is-open{/if}" data-controller="FilterGroup">
	<legend class="f-filter__title">
		<button type="button" class="btn f-filter__name" data-action="FilterGroup#toggle" id="{$id}-btn" aria-controls="{$id}" aria-expanded="{$filterItem->isOpen || $forceOpen ? 'true' : 'false'}">
			<span>{$filterItem->title}</span>
			<span class="f-filter__name--count" n:if="count($filterItem->activeValues)">{count($filterItem->activeValues)}</span>
		</button>
	</legend>


	<div class="f-filter__inner" id="{$id}" role="region" aria-labelledby="{$id}-btn" {if !($filterItem->isOpen || $forceOpen)}hidden=""{/if}>
		<ul class="f-filter__list">
			{foreach $filterItem->values as $value}
				{capture $link}{link 'this', filter => $value->followingFilterParameters, 'pager-page' => null}{/capture}
				{php $link = urldecode(htmlspecialchars_decode($link))}


				<li class="f-filter__item{if isset($filterItem->visibleValuesCount) && $filterItem->visibleValuesCount && $iterator->getCounter() > $filterItem->visibleValuesCount} u-js-hide{/if}"{if isset($filterItem->visibleValuesCount) && $filterItem->visibleValuesCount && $iterator->getCounter() > $filterItem->visibleValuesCount} data-filtergroup-target="hidden"{/if}>
					<label n:class="inp-item, inp-item--checkbox, $value->isActive ? 'is-active'">
						<input type="checkbox"
							name="{$value->inputName}"
							value="{$value->inputValue}"
							id="id-{$value->inputValue}"
							data-action="change->FilterGroup#submitForm"
							class="inp-item__inp"
							{if !$value->isActive && $value->count == 0} disabled{/if}
							{if isset($value->isActive) && $value->isActive} checked{/if}>
						<span class="inp-item__text">
							{if !$value->isActive && $value->count == 0}
								{$value->name}
							{else}
								<a href="{$link}"{if $linkSeo->hasNofollow($object, ['filter' => $value->followingFilterParameters])} rel="nofollow"{/if} tabindex="-1">
									{$value->name}
								</a>
							{/if}
							<span class="inp-item__count" n:if="$value->count">({$value->count})</span>
						</span>
					</label>
				</li>
			{/foreach}
		</ul>

		{if $filterItem->showMoreButton}
			<p class="u-ta-r">
				<button type="button" class="btn btn--link f-filter__more" data-action="FilterGroup#toggleHidden" data-toggle-text="{_'btn_less_values'}">{_'btn_more_values'}</button>
			</p>
		{/if}
	</div>
</fieldset>
