<?php declare(strict_types = 1);

/** @noinspection PhpRedundantCatchClauseInspection */

/** @noinspection PhpUnusedParameterInspection */

namespace SuperKoderi\Components;

use App\Model\Mutation;
use App\Model\PriceLevel;
use App\Model\Tree;
use App\Model\User;
use App\Model\UserModel;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Security\AuthenticationException;
use Nette\Utils\ArrayHash;
use SuperKoderi\Email;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\hasOrmTrait;
use SuperKoderi\hasTranslatorTrait;
use SuperKoderi\MutationHolder;
use SuperKoderi\Security\Acl;

/**
 * @property-read DefaultTemplate $template
 */
class RegistrationForm extends UI\Control
{

	use hasMessageForFormComponentTrait;
	use hasConfigServiceTrait;
	use hasOrmTrait;
	use hasTranslatorTrait;

	private Mutation $mutation;

	public function __construct(
		private Tree $object,
		private UserModel $userModel,
		private Email\ICommonFactory $commonEmailFactory,
		MutationHolder $mutationHolder,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->render(__DIR__ . '/registrationForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addText('firstname', 'form_label_firstname')->setRequired();
		$form->addText('lastname', 'form_label_lastname')->setRequired();
		$form->addEmail('email', 'form_label_email')
			->setRequired('E-mail is required');
		$form->addPassword('password', 'form_label_password')
			->setRequired();
		$form->addPassword('passwordVerify', 'form_label_password2')
			->setRequired()
			->addRule(UI\Form::EQUAL, 'form_password_not_same', $form['password']);
		$form->addCheckbox('agree')->setRequired();

		$form->addSubmit('save', 'btnRegister');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onValidate[] = [$this, 'editFormValidate'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function editFormValidate(UI\Form $form, ArrayHash $values): void
	{
		$user = $this->userModel->getByEmail($values->email, $this->mutation);

		if ($user) {
			$link1 = $this->presenter->link($this->mutation->pages->userLogin, ['email' => $values->email]);
			$link2 = $this->presenter->link($this->mutation->pages->lostPassword, ['email' => $values->email]);
			$strTranslated = $this->translator->translate('mail_exist_register');

			if (strpos($strTranslated, '%link1%') !== false) {
				$strTranslated = str_replace('%link1%', $link1, $strTranslated);
			}

			if (strpos($strTranslated, '%link2%') !== false) {
				$strTranslated = str_replace('%link2%', $link2, $strTranslated);
			}

			$form->addError($strTranslated, false);
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();

		$user = new User();
		$this->orm->user->attach($user);
		$user->role = Acl::ROLE_USER;
		$user->priceLevel = $this->orm->priceLevel->getBy(["type" => PriceLevel::TYPE_PRICE_10]);
		$user->mutations->add($this->mutation);

		$user = $this->userModel->save($user, $valuesAll);

		try {
			$this->commonEmailFactory
				->create()
				->send('', $user->email, 'login', (array) $valuesAll);

			$this->presenter->getUser()->login($valuesAll['email'], $valuesAll['password']);

			$this->flashMessage('form_register_ok', 'ok');
			$this->presenter->redirect($this->mutation->pages->eshop);

		} catch (AuthenticationException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

}

interface IRegistrationFormFactory
{

	function create(Tree $object): RegistrationForm;

}
