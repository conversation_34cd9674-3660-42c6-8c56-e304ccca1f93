<?php

namespace SuperKoderi\Components;

use App\Model\Mutation;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use SuperKoderi\Basket;
use SuperKoderi\EasyMessages;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\ImageResizer;
use SuperKoderi\MutationHolder;
use SuperKoderi\TranslatorDB;

/**
 * @property-read DefaultTemplate $template
 */
class OrderStep1Form extends UI\Control
{
	use hasMessageForFormComponentTrait;

	private TranslatorDB $translator;
	private Basket $basket;
	private EasyMessages $easyMessages;
	private ImageResizer $imageResizer;
	private Mutation $mutation;

	public function __construct(
		MutationHolder $mutationHolder,
		TranslatorDB $translator,
		Basket $basket,
		EasyMessages $easyMessages,
		ImageResizer $imageResizer
	)
	{
		$this->translator = $translator;
		$this->basket = $basket;
		$this->easyMessages = $easyMessages;
		$this->imageResizer = $imageResizer;
		$this->mutation = $mutationHolder->getMutation();
	}


	public function render(): void
	{
		$cardErrorMsg = $this->easyMessages->get(EasyMessages::KEY_CARD);
		foreach ($cardErrorMsg as $msg) {
			$this->flashMessage($msg->text, $msg->type);
		}

		$this->template->mutation = $this->mutation;
		$this->template->imageResizer = $this->imageResizer;
		$this->template->basket = $this->basket;
		$this->template->order = $this->basket->getFutureOrder();
		$this->template->pages = $this->mutation->pages;
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . "/orderStep1Form.latte");
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);
		$form->addText('transport');

		if ($this->basket->getTransportKey() !== null) {
			$form['transport']->setValue($this->basket->getTransportKey());
		}

		$form->addText('payment');

		if ($this->basket->getPaymentKey() !== null) {
			$form['payment']->setValue($this->basket->getPaymentKey());
		}

		$form->addSubmit('next', 'Next');
		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onValidate[] = [$this, 'formValidate'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}


	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formValidate(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();
		if (isset($valuesAll['next'])) {
			if (empty($values->payment) || empty($values->transport)) {
				$form->addError('step1_msg_error_delivery_payment_is_required');
			}

			if (!empty($values->payment) && !in_array($values->transport, explode(' ', $this->basket->payments[$values->payment]['transports']))) {
				$form->addError('step1_msg_error_delivery_payment_bad_combinaton');
			}
		} else {
			$this->basket->cleanPayment();
			$this->basket->cleanTransport();

			if (!empty($values->payment) && !in_array($values->transport, explode(' ', $this->basket->payments[$values->payment]['transports']))) {
				$form['payment']->setValue('');
			}
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();
		$this->basket->setTransport($values->transport);
		$this->basket->setPayment($values->payment);


		if (isset($valuesAll['next'])) {
			$this->presenter->redirect($this->mutation->pages->step2); /** @phpstan-ignore-line */
		} else {
			if ($this->presenter->isAjax()) {
				$this->presenter->redrawControl('header');
				$this->presenter->redrawControl('basketHeader');
				$this->redrawControl();
			} else {
				$this->presenter->redirect('this');
			}
		}

	}
}


interface IOrderStep1FormFactory
{
	function create(): OrderStep1Form;
}
