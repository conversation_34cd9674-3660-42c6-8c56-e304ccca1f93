<?php

namespace SuperKoderi\Components;


use App\Model\CommonTree;
use App\Model\Mutation;
use App\Model\Orm;
use App\Model\ProductVariant;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Entity\IEntity;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\hasTranslatorTrait;

/**
 * @property-read DefaultTemplate $template
 */
class ToggleLanguage extends UI\Control
{
	use hasMessageForFormComponentTrait;
	use hasTranslatorTrait;
	use hasConfigServiceTrait;

	/** @var IEntity */
	private IEntity $object;


	/**
	 * @var Mutation
	 */
	private Mutation $mutation;
	/**
	 * @var Orm
	 */
	private Orm $orm;

	public function __construct(IEntity $object, Mutation $mutation, Orm $orm)
	{
		$this->object = $object;
		$this->mutation = $mutation;
		$this->orm = $orm;

	}


	private function initTemplate(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->object = $this->object;
	}


	/**
	 * @return array
	 */
	private function getHrefLangs(): array
	{
		$hrefLangs = [];
		$mutations = $this->orm->mutation->findAll();
		if ($this->object instanceof CommonTree && (bool)$this->object->uid) {
			/** @var Mutation $m */
			foreach ($mutations as $m) {
				if ((!(bool)$this->configService->get('mutations', $m->langCode))) {
					continue;
				}
				$tree = $this->orm->tree->getByUidAndRootId($this->object->uid, $m->rootId);
				if ($tree) {
					$hrefLangs[$m->langCode] = $m->getBaseUrlWithPrefix(). '/' . $tree->alias;
				}
			}
		} elseif ($this->object instanceof ProductVariant) {
			foreach ($mutations as $m) {
				if (!(bool)$this->configService->get('mutations', $m->langCode)) {
					continue;
				}
				$loc = $this->object->getLocalization($m);
				if ($loc) {
					$hrefLangs[$m->langCode] = $m->getBaseUrlWithPrefix(). '/' . $this->object->product->getLocalization($this->mutation)->getAlias();
				}
			}
		}


		return $hrefLangs;
	}

	public function render(): void
	{
		$this->initTemplate();
		$this->template->mutations = $this->orm->mutation->findAll();
		$this->template->mutation = $this->mutation;
		$this->template->hrefLangs = $this->getHrefLangs();
		$this->template->render(__DIR__ . '/toggleLanguage.latte');
	}
}


interface IToggleLanguageFactory
{
	/**
	 * @param IEntity $object
	 * @param Mutation $mutation
	 * @return ToggleLanguage
	 */
	function create(IEntity $object, Mutation $mutation): ToggleLanguage;
}
