{varType App\Model\Order $order}

{if $order}

	<h2>{_'order_detail_title'} {$order->number}</h2>


	<p>
	<strong>{_'order_status'}:</strong> {_'order_status_' . $order->status}<br>
	<strong>{_'order_created'}:</strong> {$order->created|date:'j. n. Y, H:i'}<br>
	{* @todo jk datum vyrizeni zatim neni *}
	<strong>{_'order_delivery'}: </strong>{$order->transport->name}<br>
	<strong>{_'order_payment'}: </strong>{$order->payment->name}<br>
	{* {$order->products->count()}<br> *}
	{* <strong></strong>{$order->totalPriceDPH|priceFormat} {_'price_tax'}<br>
	<strong></strong>{$order->totalPrice|priceFormat} {_'price_without_tax'}<br> *}
	
	{if $order->infotext}
		<strong>{_'form_label_note'}: </strong>{$order->infotext}<br>
	{/if}
	{* @todo jk faktura zatim neni *}
	</p>
	<h3>{_'title_invoice_address'}</h3>
	<p>
		{* fakturacni *}
		<strong>{_'form_label_name'}, {_'form_label_surname'|lower}:</strong> {$order->firstname} {$order->lastname}<br>
		<strong>{_'form_label_street'}:</strong> {$order->street}<br>
		<strong>{_'form_label_city'}:</strong> {$order->city}<br>
		<strong>{_'form_label_zip'}:</strong> {$order->zip}<br>
		<strong>{_'form_label_email'}:</strong> {$order->email}<br>
		<strong>{_'form_label_phone'}:</strong> {$order->phone}<br>

		{if $order->company}<strong>{_'form_label_company'}:</strong> {$order->company}<br>{/if}
		{if $order->ic}<strong>{_'form_label_ic'}:</strong> {$order->ic}<br>{/if}
		{if $order->dic}<strong>{_'form_label_dic'}:</strong> {$order->dic}<br>{/if}
	</p>

	<h3>{_'title_delivery_address'}</h3>
		{* dodaci *}
		{if empty($order->dCity)}
			<p class="message">{_'order_delivery_address_note_empty'}</p>
		{else}
			<p>
			<strong>{_'form_label_name'}, {_'form_label_surname'|lower}:</strong> {$order->dFirstname} {$order->dLastname}<br>
			{if $order->dCompany}<strong>{_'form_label_company'}:</strong> {$order->dCompany}<br>{/if}
			<strong>{_'form_label_street'}:</strong> {$order->dStreet}<br>
			<strong>{_'form_label_city'}:</strong> {$order->dCity}<br>
			<strong>{_'form_label_zip'}:</strong> {$order->dZip}<br>

			{if $order->dPhone}<strong>{_'form_label_phone'}:</strong> {$order->dPhone}<br>{/if}
			</p>
		{/if}
	<div>
		<div class="u-table-responsive">
			<table class="b-cart-summary">
				<thead>
					<tr>
						<th>{_'title_product_buy'}</th>
						<th>{_'amount'}</th>
						{* <th>{_'order_item_status'}</th> *}
						<th>{_'unit_price_vat'}</th>
						{* <th>{_'unit_price'}</th> *}
						<th>{_'total_price_vat'}</th>
					</tr>
				</thead>
				<tbody>
				{if $order->products->count()}
					{foreach $order->products as $productOrderItem}
						<tr>
							<td>
							{if $productOrderItem->isTypeProduct && $productOrderItem->variant}
								<a href="{plink $productOrderItem->variant}">
									{$productOrderItem->name}
								</a>
							{else}
								<span>
									{$productOrderItem->name}
								</span>
							{/if}
							</td>
							<td class="u-ta-r">
								{$productOrderItem->amount}
							</td>
							{*<td>*}
								{* @todo jk ERP stav - zatim neni *}
							{*</td>*}
							<td class="c-basket__total--price">
								{$productOrderItem->unitPriceDPH|priceFormat}
							</td>
							{* <td class="c-basket__total--price">
								{$productOrderItem->unitPrice|priceFormat}
							</td> *}
							<td class="c-basket__total--price">
								<strong>{$productOrderItem->totalPriceDPH|priceFormat}</strong>
							</td>
						</tr>
					{/foreach}
				{/if}
				<tr>
					<td colspan="3">
						<span>{_'order_delivery'}:</span>
						{$order->transport->name}
					</td>
					<td class="c-basket__total--price">
						<strong>
							{if $order->transport->unitPriceDPH}
								{$order->transport->unitPriceDPH|priceFormat}
							{else}
								{_'free'}
							{/if}
						</strong>
					</td>
				</tr>
				<tr>
					<td colspan="3">
						<span>{_'order_payment'}:</span>
						{$order->payment->name}
					</td>
					<td class="c-basket__total--price">
						<strong>
							{if $order->payment->unitPriceDPH}
								{$order->payment->unitPriceDPH|priceFormat}
							{else}
								{_'free'}
							{/if}
						</strong>
					</td>
				</tr>
				{if $order->vouchers->count()}
					{foreach $order->vouchers as $voucherOrderItem}
						<tr>
							<td colspan="3">
								<span>{_'title_voucher'}:</span>
								{$voucherOrderItem->name}
							</td>
							<td class="c-basket__total--price">
								<strong>
									{$voucherOrderItem->unitPriceDPH|priceFormat}
								</strong>
							</td>
						</tr>
					{/foreach}
				{/if}
				</tbody>
			</table>
		</div>
	</div>
	{* <p>
		<strong>{_'order_delivery'}:</strong> 
		{$order->transport->name}
		{if $order->transport->unitPriceDPH}
			{$order->transport->unitPriceDPH|priceFormat}
		{else}
			{_'free'}
		{/if}
		<br>
		<strong>{_'order_payment'}:</strong> 
		{$order->payment->name}
			{if $order->payment->unitPriceDPH}
			{$order->payment->unitPriceDPH|priceFormat}
		{else}
			{_'free'}
		{/if}
	</p> *}
	<p class="u-ta-r">
		{_'total_price'}: {$order->totalPrice|priceFormat}<br>
		<span class="u-text-lg">{_'total_price_vat'}: <strong>{$order->totalPriceDPH|priceFormat}</strong></span>
	</p>

	{* @todo jk voucher - zatim nejsou*}
	{* {if $order->vouchers->count() || $order->discounts->count()}
		<table>
			<thead>
				<tr>
					<td>
						{_title_voucher}
					</td>
					<td>
						{_total_price_vat}
					</td>
				</tr>
			</thead>
			<tbody>
				{if $order->vouchers->count()}
					{foreach $order->vouchers as $voucherOrderItem}
						<tr>
							<td>
								{$voucherOrderItem->name}
							</td>
							<td>
								{$voucherOrderItem->unitPriceDPH|priceFormat}
							</td>
						</tr>
					{/foreach}
				{/if}

				{if $order->discounts->count()}
					{foreach $order->discounts as $voucherOrderItem}
						<tr>
							<td>
								{$voucherOrderItem->name}
							</td>
							<td>
								{$voucherOrderItem->unitPriceDPH|priceFormat}
							</td>
						</tr>
					{/foreach}
				{/if}
			</tbody>
		</table>
	{/if} *}

	{* @todo jk darky? *}
	{* {foreach $order->products as $productOrderItem}
		{if $productOrderItem->presents}
			{foreach $productOrderItem->presents as $present}
				<p>
					{if isset($present->product->alias)}
						<a n:href="$present->product">
							{$present->name}
						</a>
					{else}
						{$present->name}
					{/if}
				</p>
			{/foreach}
		{/if}
	{/foreach} *}



{/if}
