<?php declare(strict_types=1);

namespace SuperKoderi\Components;

use App\Model\Mutation;
use App\Model\PriceLevel;
use App\Model\State;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Entity\IEntity;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasTranslatorTrait;

/**
 * @property-read DefaultTemplate $template
 */
class CustomContentRenderer extends UI\Control
{

	use hasTranslatorTrait;
	use hasConfigServiceTrait;

	private IEntity $object;
	private Mutation $mutation;
	private PriceLevel $priceLevel;
	private State $state;

	public function __construct(
		IEntity $object,
		Mutation $mutation,
		PriceLevel $priceLevel,
		State $state
	)
	{
		$this->object = $object;
		$this->mutation = $mutation;
		$this->priceLevel = $priceLevel;
		$this->state = $state;
	}


	public function render(array $props = []): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->templates = FE_TEMPLATE_DIR;
		$template->defaultTemplateDirectory = FE_TEMPLATE_DIR . '/part/customContent';
		$template->isDev = $this->configService->get('isDev');
		$template->props = $props;
		$template->object = $this->object;
		$template->mutation = $this->mutation;
		$template->pages = $this->mutation->pages;
		$template->state = $this->state;
		$template->priceLevel = $this->priceLevel;
		assert(method_exists($this->object, 'getCCModules'));
		if ($this->object->getCCModules()) {
			assert(isset($this->object->cc));
			$template->defaultObjectCC = $this->object->cc ?? [];
		} else {
			$template->defaultObjectCC = [];
		}

		$template->render(__DIR__ . '/customContentRenderer.latte');
	}

}


interface ICustomContentRendererFactory
{

	public function create(IEntity $object, Mutation $mutation, PriceLevel $priceLevel, State $state): CustomContentRenderer;

}
