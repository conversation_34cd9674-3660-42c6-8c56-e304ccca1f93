parameters:
	config:
		erp:
			connectors:
				infosConnector: []

			products:
				limit: 200 #ERP API limit parameter
				publicNew: false #if true, publish new products
				updateInternalValues: false #if false, internal names are set only when creating a new product

			heureka_review:
				import:
					batchLimit: 10
					verboseLog: true
				process:
					batchLimit: 100
					verboseLog: true

			heureka_shop_review:
				import:
					batchLimit: 10
					verboseLog: true
				process:
					batchLimit: 100
					verboseLog: true

services:
	#connectors - must be named, there can be more than one connectors of the same class (with different config)
	erp.infosConnector: App\Model\Erp\Connector\InfosErpConnector('infosConnector')
	erp.heurekaReviewConnector: App\Model\Erp\Connector\HeurekaReviewConnector('heurekaReviewConnector')
	erp.heurekaShopReviewConnector: App\Model\Erp\Connector\HeurekaShopReviewConnector('heurekaShopReviewConnector')

	#importers - must be named, there can be more than one importers of the same class (with different connectors)
	erp.ProductImporter: App\Model\Erp\Importer\InfosProductImporter(@erp.infosConnector)
	erp.HeurekaReviewImporter: App\Model\Erp\Importer\HeurekaReviewImporterFactory(@erp.heurekaReviewConnector)
	erp.HeurekaShopReviewImporter: App\Model\Erp\Importer\HeurekaShopReviewImporterFactory(@erp.heurekaShopReviewConnector)

	#exporters - must be named, there can be more than one exporters of the same class (with different connectors)
#	erp.OrderExporter: App\Model\Erp\Exporter\AlOrderExporter(@erp.infosConnector)

	#import processors - must be named, there can be more than one processors of the same interface
	erp.ProductImportProcessor: App\Model\Erp\ImportProcessor\InfosProductImportProcessor

	#readers
#	- App\Model\Erp\Processor\Reader\HeurekaReviewReader
#	- App\Model\Erp\Processor\Reader\HeurekaShopReviewReader


monolog:
	channel:
		erpInfo:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/info.log, 15, Monolog\Logger::INFO)
		erpError:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/error.log, 15, Monolog\Logger::ERROR)

		heureka_review_import:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/heureka_review_import.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		heureka_review_process:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/heureka_review_process.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		heureka_shop_review_import:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/heureka_shop_review_import.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		heureka_shop_review_process:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/heureka_shop_review_process.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor
