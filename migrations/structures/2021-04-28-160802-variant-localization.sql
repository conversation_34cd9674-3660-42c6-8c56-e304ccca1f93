DROP TABLE IF EXISTS `product_variant_localization`;
CREATE TABLE `product_variant_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `variantId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `active` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;



ALTER TABLE `product_variant_localization`
ADD FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `product_variant_localization`
ADD FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

