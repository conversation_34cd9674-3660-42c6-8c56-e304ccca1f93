@use 'sass:math';
@use 'config';
@use 'base/variables';
@use 'base/mixins';
@use 'base/extends';

.row-main {
	$s: &;
	position: relative;
	display: flow-root;
	width: 100%;
	max-width: variables.$row-main-width;
	margin: 0 auto;
	padding: 0 math.div(variables.$row-main-gutter, 2);

	& > :last-child {
		margin-bottom: 0;
	}

	.row-main {
		padding-right: 0;
		padding-left: 0;
	}
	#{$s}--small {
		max-width: variables.$row-main-width-small - 2 * variables.$grid-gutter;
	}

	// modifiers
	&--small {
		max-width: variables.$row-main-width-small;
	}

	&--expand {
		max-width: none;
	}
	// MQ
	@media (config.$xl-up) {
		padding-right: variables.$row-main-gutter;
		padding-left: variables.$row-main-gutter;
	}
}
.grid {
	@extend %reset-ol;
	@extend %grid;
	margin-bottom: -(variables.$grid-gutter);
	margin-left: -(variables.$grid-gutter);
	&__cell {
		@extend %reset-ol-li;
		@extend %grid__cell;
		position: relative;
		border: variables.$grid-gutter solid transparent;
		border-width: 0 0 variables.$grid-gutter variables.$grid-gutter;

		// hide the border in MS high contrast mode
		border-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3C/svg%3E");
		&--top {
			align-self: flex-start;
		}
		&--middle {
			align-self: center;
		}
		&--bottom {
			align-self: flex-end;
		}
		&--eq {
			display: flex;
			> * {
				flex: 1 1 auto;
			}
		}
	}

	// VARIANTs
	&--scroll {
		@extend %grid--scroll;
	}
	&--nowrap {
		flex-wrap: nowrap;
	}
	&--eq {
		align-items: stretch;

		& > * {
			display: flex;
			flex-wrap: wrap;

			& > * {
				width: 100%;
				box-sizing: border-box;
			}
		}
	}
	&--middle {
		align-items: center;
	}
	&--bottom {
		align-items: flex-end;
	}
	&--center {
		justify-content: center;
	}
	&--right {
		justify-content: flex-end;
	}
	&--space-between {
		justify-content: space-between;
	}
	&--0 {
		margin-bottom: 0;
		margin-left: 0;
	}
	&--0 > &__cell {
		border-width: 0;
	}

	&--lg {
		margin-bottom: -40px;
		margin-left: -30px;
	}
	&--lg > &__cell {
		border: 30px solid transparent;
		border-width: 0 0 40px 30px;
	}

	// MQ
	@media (config.$lg-up) {
		&--lg {
			margin-bottom: -60px;
			margin-left: -60px;
		}
		&--lg > &__cell {
			border: 60px solid transparent;
			border-width: 0 0 60px 60px;
		}
	}
}
.size {
	@include mixins.generate-grid-size();
}

// .order {
// 	@include generate-grid-order();
// }
