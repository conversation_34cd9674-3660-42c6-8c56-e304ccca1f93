const realFavicon = require('gulp-real-favicon');
const log = require('fancy-log');
const colors = require('ansi-colors');
const fs = require('fs-extra');
const path = require('path');
const config = require('./helpers/getConfig.js');

const faviconData = config.favicon;
const srcMasterPicture = faviconData.src;
const srcDest = config.src.favicon;
const tplPath = faviconData.tplPathCode;
const manifestFile = path.join(srcDest, 'site.webmanifest');
const faviconSrcDir = `${srcDest}favicon.ico`;
const faviconDestDir = `${faviconData.faviconDestPath}favicon.ico`;

let name, short_name, start_url, theme_color, background_color, description, orientation;
if (faviconData) {
	if (faviconData.manifest) {
		name = faviconData.manifest.name;
		short_name = faviconData.manifest.short_name;
		start_url = faviconData.manifest.start_url;
		theme_color = faviconData.manifest.theme_color;
		background_color = faviconData.manifest.background_color;
		description = faviconData.manifest.description;
		orientation = faviconData.manifest.orientation;
	}
}

module.exports = function favicon(done) {
	let options = {
		masterPicture: srcMasterPicture,
		dest: srcDest,
		iconsPath: srcDest,
		design: {
			ios: {
				pictureAspect: 'backgroundAndMargin',
				backgroundColor: '#ffffff',
				margin: '14%',
				assets: {
					ios6AndPriorIcons: false,
					ios7AndLaterIcons: false,
					precomposedIcons: false,
					declareOnlyDefaultIcon: true,
				},
			},
			desktopBrowser: {
				design: 'raw',
				imageScale: 0.8,
			},
			windows: {
				pictureAspect: 'noChange',
				backgroundColor: background_color,
				onConflict: 'override',
				assets: {
					windows80Ie10Tile: false,
					windows10Ie11EdgeTiles: {
						small: false,
						medium: true,
						big: false,
						rectangle: false,
					},
				},
			},
			androidChrome: {
				pictureAspect: 'backgroundAndMargin',
				margin: '25%',
				backgroundColor: '#ffffff00',
				themeColor: theme_color,
				manifest: {
					display: 'standalone',
					orientation: orientation,
					onConflict: 'override',
					declared: false,
				},
				assets: {
					legacyIcon: false,
					lowResolutionIcons: false,
				},
			},
			safariPinnedTab: {
				pictureAspect: 'blackAndWhite',
				threshold: 60.15625,
				themeColor: theme_color,
			},
		},
		settings: {
			scalingAlgorithm: 'Mitchell',
			errorOnImageTooSmall: false,
			readmeFile: false,
			htmlCodeFile: false,
			usePathAsIs: false,
		},
		markupFile: 'favicon.json',
	};

	fs.access(srcMasterPicture, fs.F_OK, (err) => {
		if (err) {
			console.error(err);
			return;
		}

		realFavicon.generateFavicon(options, () => {
			manifest(); // addition of values from config.js
			fs.rename(faviconSrcDir, faviconDestDir)
				// .then(() => log.info(colors.green('favicon.ico has been copied!')))
				.catch((err) => console.error(err));
		});
	});

	done();
};

function manifest() {
	fs.readFile(manifestFile, (err, data) => {
		if (err) {
			console.error('Error: Manifest does not exist!');
			return;
		}
		let contentJson = data.toString();
		let content = JSON.parse(contentJson);
		content.name = name;
		content.short_name = short_name;
		if (description) {
			content.description = description;
		}
		if (orientation) {
			content.orientation = orientation;
		}
		content.start_url = start_url;
		content.theme_color = theme_color;
		content.background_color = background_color;

		const index = content.icons.findIndex(({ src }) => src === 'android-chrome-192x192.png');
		content.icons = [...content.icons, { ...content.icons[index], purpose: 'maskable' }];

		contentJson = JSON.stringify(content);

		fs.writeFile(manifestFile, contentJson, () => {});

		var fullMessage =
			`In <head></head> change code to:` +
			colors.yellow(
				`
				\r<link rel="apple-touch-icon" sizes="180x180" href="` +
					tplPath +
					`/apple-touch-icon.png">
				\r<link rel="icon" type="image/png" sizes="32x32" href="` +
					tplPath +
					`/favicon-32x32.png">
				\r<link rel="icon" type="image/png" sizes="16x16" href="` +
					tplPath +
					`/favicon-16x16.png">
				\r<link rel="manifest" href="` +
					tplPath +
					`/site.webmanifest">
				\r<link rel="mask-icon" href="` +
					tplPath +
					`/safari-pinned-tab.svg" color="` +
					theme_color +
					`">
				\r<meta name="msapplication-TileColor" content="` +
					theme_color +
					`">
				\r<meta name="theme-color" content="` +
					background_color +
					`">
				\r<link rel="shortcut icon" href="favicon.ico">`,
			);
		log.info(fullMessage);
	});
}
