import $ from 'jquery';
import { Controller } from 'stimulus';

export default class DataGrid extends Controller {
	loadScripts = async () => {
		// eslint-disable-next-line
		naja.initialize();

		const Happy = await import(/* webpackChunkName: "happy-inputs" */ 'happy-inputs');
		let happy = new Happy.default();
		window.happy = happy;
		happy.init();

		await import(/* webpackChunkName: "popper" */ 'popper.js');
		await import(/* webpackChunkName: "bootstrap" */ 'bootstrap');
		await import(/* webpackChunkName: "datepicker" */ 'bootstrap-datepicker');
		await import(/* webpackChunkName: "datepicker-locales" */ 'bootstrap-datepicker/js/locales/bootstrap-datepicker.cs.js');
		await import(/* webpackChunkName: "jquery-ui-sortable" */ 'jquery-ui-sortable');
		await import(/* webpackChunkName: "ublaboo-datagrid" */ 'ublaboo-datagrid');
		const NetteForms = await import(/* webpackChunkName: "nette-forms" */ 'nette-forms');
		await import(/* webpackChunkName: "datagrid-instant-url-refresh" */ '../static/datagrid-instant-url-refresh.js');
		await import(/* webpackChunkName: "datagrid-spinners" */ 'ublaboo-datagrid/assets/datagrid-spinners.js');
		await import(/* webpackChunkName: "bootstrap-select" */ 'bootstrap-select');

		NetteForms.initOnLoad();
		window.Nette = NetteForms;
		$('*[data-provide="datepicker"]').datepicker({
			language: 'cs',
		});
	};

	connect() {
		this.loadScripts().then(() => {
			// clickabel all tr
			$(document).on('click', '.datagrid tbody tr', function() {
				const $btn = $(this)
					.find('a.btn-primary')
					.first()[0];

				if ($btn) {
					$btn.click();
				}
			});

			$('*[data-provide="datepicker"]').datepicker({
				language: 'cs',
			});

			$(document).on('click', '[data-datagrid-editable-url]', function() {
				$(this)
					.find('textarea')
					.select();
			});

			var shiftPressed = false;
			$(document).on('keydown', '[data-datagrid-editable-url] textarea', function(event) {
				if (event.key == 'Shift') {
					shiftPressed = true;
				}
			});
			$(document).on('keyup', '[data-datagrid-editable-url] textarea', function(event) {
				if (event.key == 'Shift') {
					shiftPressed = false;
				}
			});
			$(document).on('keydown', '[data-datagrid-editable-url] textarea', function(event) {
				if (event.key === 'Enter' && shiftPressed == false) {
					$(this).blur();
				}
			});

			// TODO: nutno prepsat / vyresit lip
			// pokud oznacim nekolik zaznamu, pak kliknu do filtru a entrem odeslu, tak se odesle cely formular datagridu a jelikoz je tlacitko pro hromadne smazani submit tak se jeho akce vyvola
			$('.datagrid [data-autosubmit]').focusin(function() {
				$('.group-delete-button')
					.removeAttr('type')
					.attr('type', 'button');
			});
			$('.datagrid [data-autosubmit]').focusout(function() {
				$('.group-delete-button')
					.removeAttr('type')
					.attr('type', 'submit');
			});

			$(document).on('keydown', '.datagrid .datagrid-row-inline-add .form-control', function(event) {
				if (event.key === 'Enter') {
					$(this)
						.parent()
						.siblings('.col-action')
						.find('.btn-primary')
						.click();
				}
				if (event.key === 'Escape') {
					$(this)
						.parent()
						.siblings('.col-action')
						.find('.btn-danger')
						.click();
				}
			});
		});
	}
}
