/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.0.1 (2019-02-21)
 */
!function(j){"use strict";var o=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},q=function(n,r){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(r.apply(null,e))}},$=function(e){return function(){return e}},W=function(e){return e};function d(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=o.concat(e);return r.apply(null,n)}}var e,t,n,r,i,a,u,s,l,c,f,h,m,g,p,v,b,y=function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return!n.apply(null,e)}},C=$(!1),w=$(!0),x=C,N=w,E=function(){return z},z=(r={fold:function(e,t){return e()},is:x,isSome:x,isNone:N,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:n,orThunk:t,map:E,ap:E,each:function(){},bind:E,flatten:E,exists:x,forall:N,filter:E,equals:e=function(e){return e.isNone()},equals_:e,toArray:function(){return[]},toString:$("none()")},Object.freeze&&Object.freeze(r),r),S=function(n){var e=function(){return n},t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:N,isNone:x,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return S(e(n))},ap:function(e){return e.fold(E,function(e){return S(e(n))})},each:function(e){e(n)},bind:r,flatten:e,exists:r,forall:r,filter:function(e){return e(n)?o:z},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(x,function(e){return t(n,e)})},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},T={some:S,none:E,from:function(e){return null===e||e===undefined?z:S(e)}},k=function(t){return function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(e)===t}},A=k("string"),R=k("object"),D=k("array"),M=k("null"),B=k("boolean"),_=k("function"),O=k("number"),H=(i=Array.prototype.indexOf)===undefined?function(e,t){return Y(e,t)}:function(e,t){return i.call(e,t)},P=function(e,t){return-1<H(e,t)},K=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o,e)}return r},L=function(e,t){for(var n=0,r=e.length;n<r;n++)t(e[n],n,e)},X=function(e,t){for(var n=[],r=[],o=0,i=e.length;o<i;o++){var a=e[o];(t(a,o,e)?n:r).push(a)}return{pass:n,fail:r}},V=function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r,e)&&n.push(i)}return n},I=function(e,t,n){return L(e,function(e){n=t(n,e)}),n},F=function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n,e))return T.some(o)}return T.none()},U=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n],n,e))return T.some(n);return T.none()},Y=function(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return n;return-1},G=Array.prototype.push,J=function(e,t){return function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!Array.prototype.isPrototypeOf(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);G.apply(t,e[n])}return t}(K(e,t))},Q=function(e,t){for(var n=0,r=e.length;n<r;++n)if(!0!==t(e[n],n,e))return!1;return!0},Z=Array.prototype.slice,ee=function(e,t){return V(e,function(e){return!P(t,e)})},te=function(e){return 0===e.length?T.none():T.some(e[0])},ne=function(e){return 0===e.length?T.none():T.some(e[e.length-1])},re=_(Array.from)?Array.from:function(e){return Z.call(e)},oe="undefined"!=typeof window?window:Function("return this;")(),ie=function(e,t){return function(e,t){for(var n=t!==undefined&&null!==t?t:oe,r=0;r<e.length&&n!==undefined&&null!==n;++r)n=n[e[r]];return n}(e.split("."),t)},ae={getOrDie:function(e,t){var n=ie(e,t);if(n===undefined||null===n)throw e+" not available on this browser";return n}},ue=function(){return ae.getOrDie("URL")},se={createObjectURL:function(e){return ue().createObjectURL(e)},revokeObjectURL:function(e){ue().revokeObjectURL(e)}},le=j.navigator,ce=le.userAgent,fe=function(e){return"matchMedia"in j.window&&j.matchMedia(e).matches};m=/Android/.test(ce),u=(u=!(a=/WebKit/.test(ce))&&/MSIE/gi.test(ce)&&/Explorer/gi.test(le.appName))&&/MSIE (\w+)\./.exec(ce)[1],s=-1!==ce.indexOf("Trident/")&&(-1!==ce.indexOf("rv:")||-1!==le.appName.indexOf("Netscape"))&&11,l=-1!==ce.indexOf("Edge/")&&!u&&!s&&12,u=u||s||l,c=!a&&!s&&/Gecko/.test(ce),f=-1!==ce.indexOf("Mac"),h=/(iPad|iPhone)/.test(ce),g="FormData"in j.window&&"FileReader"in j.window&&"URL"in j.window&&!!se.createObjectURL,p=fe("only screen and (max-device-width: 480px)")&&(m||h),v=fe("only screen and (min-width: 800px)")&&(m||h),b=-1!==ce.indexOf("Windows Phone"),l&&(a=!1);var de,he={opera:!1,webkit:a,ie:u,gecko:c,mac:f,iOS:h,android:m,contentEditable:!h||g||534<=parseInt(ce.match(/AppleWebKit\/(\d*)/)[1],10),transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",caretAfter:8!==u,range:j.window.getSelection&&"Range"in j.window,documentMode:u&&!l?j.document.documentMode||7:10,fileApi:g,ceFalse:!1===u||8<u,cacheSuffix:null,container:null,overrideViewPort:null,experimentalShadowDom:!1,canHaveCSP:!1===u||11<u,desktop:!p&&!v,windowsPhone:b},me=window.Promise?window.Promise:function(){function r(e,t){return function(){e.apply(t,arguments)}}var e=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},i=function(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],c(e,r(o,this),r(u,this))},t=i.immediateFn||"function"==typeof setImmediate&&setImmediate||function(e){setTimeout(e,1)};function a(r){var o=this;null!==this._state?t(function(){var e=o._state?r.onFulfilled:r.onRejected;if(null!==e){var t;try{t=e(o._value)}catch(n){return void r.reject(n)}r.resolve(t)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function o(e){try{if(e===this)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if("function"==typeof t)return void c(r(t,e),r(o,this),r(u,this))}this._state=!0,this._value=e,s.call(this)}catch(n){u.call(this,n)}}function u(e){this._state=!1,this._value=e,s.call(this)}function s(){for(var e=0,t=this._deferreds.length;e<t;e++)a.call(this,this._deferreds[e]);this._deferreds=null}function l(e,t,n,r){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r}function c(e,t,n){var r=!1;try{e(function(e){r||(r=!0,t(e))},function(e){r||(r=!0,n(e))})}catch(o){if(r)return;r=!0,n(o)}}return i.prototype["catch"]=function(e){return this.then(null,e)},i.prototype.then=function(n,r){var o=this;return new i(function(e,t){a.call(o,new l(n,r,e,t))})},i.all=function(){var s=Array.prototype.slice.call(1===arguments.length&&e(arguments[0])?arguments[0]:arguments);return new i(function(o,i){if(0===s.length)return o([]);var a=s.length;function u(t,e){try{if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if("function"==typeof n)return void n.call(e,function(e){u(t,e)},i)}s[t]=e,0==--a&&o(s)}catch(r){i(r)}}for(var e=0;e<s.length;e++)u(e,s[e])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(e){e(t)})},i.reject=function(n){return new i(function(e,t){t(n)})},i.race=function(o){return new i(function(e,t){for(var n=0,r=o.length;n<r;n++)o[n].then(e,t)})},i}(),ge=function(e,t){return"number"!=typeof t&&(t=0),setTimeout(e,t)},pe=function(e,t){return"number"!=typeof t&&(t=1),setInterval(e,t)},ve=function(t,n){var r,e;return(e=function(){var e=arguments;clearTimeout(r),r=ge(function(){t.apply(this,e)},n)}).stop=function(){clearTimeout(r)},e},be={requestAnimationFrame:function(e,t){de?de.then(e):de=new me(function(e){t||(t=j.document.body),function(e,t){var n,r=j.window.requestAnimationFrame,o=["ms","moz","webkit"];for(n=0;n<o.length&&!r;n++)r=j.window[o[n]+"RequestAnimationFrame"];r||(r=function(e){j.window.setTimeout(e,0)}),r(e,t)}(e,t)}).then(e)},setTimeout:ge,setInterval:pe,setEditorTimeout:function(e,t,n){return ge(function(){e.removed||t()},n)},setEditorInterval:function(e,t,n){var r;return r=pe(function(){e.removed?clearInterval(r):t()},n)},debounce:ve,throttle:ve,clearInterval:function(e){return clearInterval(e)},clearTimeout:function(e){return clearTimeout(e)}},ye=/^(?:mouse|contextmenu)|click/,Ce={keyLocation:1,layerX:1,layerY:1,returnValue:1,webkitMovementX:1,webkitMovementY:1,keyIdentifier:1},we=function(){return!1},xe=function(){return!0},Ne=function(e,t,n,r){e.addEventListener?e.addEventListener(t,n,r||!1):e.attachEvent&&e.attachEvent("on"+t,n)},Ee=function(e,t,n,r){e.removeEventListener?e.removeEventListener(t,n,r||!1):e.detachEvent&&e.detachEvent("on"+t,n)},ze=function(e,t){var n,r,o=t||{};for(n in e)Ce[n]||(o[n]=e[n]);if(o.target||(o.target=o.srcElement||j.document),he.experimentalShadowDom&&(o.target=function(e,t){if(e.composedPath){var n=e.composedPath();if(n&&0<n.length)return n[0]}return t}(e,o.target)),e&&ye.test(e.type)&&e.pageX===undefined&&e.clientX!==undefined){var i=o.target.ownerDocument||j.document,a=i.documentElement,u=i.body;o.pageX=e.clientX+(a&&a.scrollLeft||u&&u.scrollLeft||0)-(a&&a.clientLeft||u&&u.clientLeft||0),o.pageY=e.clientY+(a&&a.scrollTop||u&&u.scrollTop||0)-(a&&a.clientTop||u&&u.clientTop||0)}return o.preventDefault=function(){o.isDefaultPrevented=xe,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},o.stopPropagation=function(){o.isPropagationStopped=xe,e&&(e.stopPropagation?e.stopPropagation():e.cancelBubble=!0)},!(o.stopImmediatePropagation=function(){o.isImmediatePropagationStopped=xe,o.stopPropagation()})==((r=o).isDefaultPrevented===xe||r.isDefaultPrevented===we)&&(o.isDefaultPrevented=we,o.isPropagationStopped=we,o.isImmediatePropagationStopped=we),"undefined"==typeof o.metaKey&&(o.metaKey=!1),o},Se=function(e,t,n){var r=e.document,o={type:"ready"};if(n.domLoaded)t(o);else{var i=function(){return"complete"===r.readyState||"interactive"===r.readyState&&r.body},a=function(){Ee(e,"DOMContentLoaded",a),Ee(e,"load",a),n.domLoaded||(n.domLoaded=!0,t(o))},u=function(){i()&&(Ee(r,"readystatechange",u),a())},s=function(){try{r.documentElement.doScroll("left")}catch(e){return void be.setTimeout(s)}a()};!r.addEventListener||he.ie&&he.ie<11?(Ne(r,"readystatechange",u),r.documentElement.doScroll&&e.self===e.top&&s()):i()?a():Ne(e,"DOMContentLoaded",a),Ne(e,"load",a)}},ke=function(){var h,m,g,p,v,b=this,y={};m="mce-data-"+(+new Date).toString(32),p="onmouseenter"in j.document.documentElement,g="onfocusin"in j.document.documentElement,v={mouseenter:"mouseover",mouseleave:"mouseout"},h=1,b.domLoaded=!1,b.events=y;var C=function(e,t){var n,r,o,i,a=y[t];if(n=a&&a[e.type])for(r=0,o=n.length;r<o;r++)if((i=n[r])&&!1===i.func.call(i.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return};b.bind=function(e,t,n,r){var o,i,a,u,s,l,c,f=j.window,d=function(e){C(ze(e||f.event),o)};if(e&&3!==e.nodeType&&8!==e.nodeType){for(e[m]?o=e[m]:(o=h++,e[m]=o,y[o]={}),r=r||e,a=(t=t.split(" ")).length;a--;)l=d,s=c=!1,"DOMContentLoaded"===(u=t[a])&&(u="ready"),b.domLoaded&&"ready"===u&&"complete"===e.readyState?n.call(r,ze({type:u})):(p||(s=v[u])&&(l=function(e){var t,n;if(t=e.currentTarget,(n=e.relatedTarget)&&t.contains)n=t.contains(n);else for(;n&&n!==t;)n=n.parentNode;n||((e=ze(e||f.event)).type="mouseout"===e.type?"mouseleave":"mouseenter",e.target=t,C(e,o))}),g||"focusin"!==u&&"focusout"!==u||(c=!0,s="focusin"===u?"focus":"blur",l=function(e){(e=ze(e||f.event)).type="focus"===e.type?"focusin":"focusout",C(e,o)}),(i=y[o][u])?"ready"===u&&b.domLoaded?n({type:u}):i.push({func:n,scope:r}):(y[o][u]=i=[{func:n,scope:r}],i.fakeName=s,i.capture=c,i.nativeHandler=l,"ready"===u?Se(e,l,b):Ne(e,s||u,l,c)));return e=i=0,n}},b.unbind=function(e,t,n){var r,o,i,a,u,s;if(!e||3===e.nodeType||8===e.nodeType)return b;if(r=e[m]){if(s=y[r],t){for(i=(t=t.split(" ")).length;i--;)if(o=s[u=t[i]]){if(n)for(a=o.length;a--;)if(o[a].func===n){var l=o.nativeHandler,c=o.fakeName,f=o.capture;(o=o.slice(0,a).concat(o.slice(a+1))).nativeHandler=l,o.fakeName=c,o.capture=f,s[u]=o}n&&0!==o.length||(delete s[u],Ee(e,o.fakeName||u,o.nativeHandler,o.capture))}}else{for(u in s)o=s[u],Ee(e,o.fakeName||u,o.nativeHandler,o.capture);s={}}for(u in s)return b;delete y[r];try{delete e[m]}catch(d){e[m]=null}}return b},b.fire=function(e,t,n){var r;if(!e||3===e.nodeType||8===e.nodeType)return b;for((n=ze(null,n)).type=t,n.target=e;(r=e[m])&&C(n,r),(e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow)&&!n.isPropagationStopped(););return b},b.clean=function(e){var t,n,r=b.unbind;if(!e||3===e.nodeType||8===e.nodeType)return b;if(e[m]&&r(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName)for(r(e),t=(n=e.getElementsByTagName("*")).length;t--;)(e=n[t])[m]&&r(e);return b},b.destroy=function(){y={}},b.cancel=function(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}};ke.Event=new ke,ke.Event.bind(j.window,"ready",function(){});var Te,Ae,Re,De,Me,Be,_e,Oe,He,Pe,Le,Ve,Ie,Fe,Ue,je,qe,$e,We="sizzle"+-new Date,Ke=j.window.document,Xe=0,Ye=0,Ge=Tt(),Je=Tt(),Qe=Tt(),Ze=function(e,t){return e===t&&(Le=!0),0},et=typeof undefined,tt={}.hasOwnProperty,nt=[],rt=nt.pop,ot=nt.push,it=nt.push,at=nt.slice,ut=nt.indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]===e)return t;return-1},st="[\\x20\\t\\r\\n\\f]",lt="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",ct="\\["+st+"*("+lt+")(?:"+st+"*([*^$|!~]?=)"+st+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+lt+"))|)"+st+"*\\]",ft=":("+lt+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ct+")*)|.*)\\)|)",dt=new RegExp("^"+st+"+|((?:^|[^\\\\])(?:\\\\.)*)"+st+"+$","g"),ht=new RegExp("^"+st+"*,"+st+"*"),mt=new RegExp("^"+st+"*([>+~]|"+st+")"+st+"*"),gt=new RegExp("="+st+"*([^\\]'\"]*?)"+st+"*\\]","g"),pt=new RegExp(ft),vt=new RegExp("^"+lt+"$"),bt={ID:new RegExp("^#("+lt+")"),CLASS:new RegExp("^\\.("+lt+")"),TAG:new RegExp("^("+lt+"|[*])"),ATTR:new RegExp("^"+ct),PSEUDO:new RegExp("^"+ft),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+st+"*(even|odd|(([+-]|)(\\d*)n|)"+st+"*(?:([+-]|)"+st+"*(\\d+)|))"+st+"*\\)|)","i"),bool:new RegExp("^(?:checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)$","i"),needsContext:new RegExp("^"+st+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+st+"*((?:-\\d)?\\d*)"+st+"*\\)|)(?=[^-]|$)","i")},yt=/^(?:input|select|textarea|button)$/i,Ct=/^h\d$/i,wt=/^[^{]+\{\s*\[native \w/,xt=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Nt=/[+~]/,Et=/'|\\/g,zt=new RegExp("\\\\([\\da-f]{1,6}"+st+"?|("+st+")|.)","ig"),St=function(e,t,n){var r="0x"+t-65536;return r!=r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)};try{it.apply(nt=at.call(Ke.childNodes),Ke.childNodes),nt[Ke.childNodes.length].nodeType}catch($N){it={apply:nt.length?function(e,t){ot.apply(e,at.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}var kt=function(e,t,n,r){var o,i,a,u,s,l,c,f,d,h;if((t?t.ownerDocument||t:Ke)!==Ie&&Ve(t),n=n||[],!e||"string"!=typeof e)return n;if(1!==(u=(t=t||Ie).nodeType)&&9!==u)return[];if(Ue&&!r){if(o=xt.exec(e))if(a=o[1]){if(9===u){if(!(i=t.getElementById(a))||!i.parentNode)return n;if(i.id===a)return n.push(i),n}else if(t.ownerDocument&&(i=t.ownerDocument.getElementById(a))&&$e(t,i)&&i.id===a)return n.push(i),n}else{if(o[2])return it.apply(n,t.getElementsByTagName(e)),n;if((a=o[3])&&Ae.getElementsByClassName)return it.apply(n,t.getElementsByClassName(a)),n}if(Ae.qsa&&(!je||!je.test(e))){if(f=c=We,d=t,h=9===u&&e,1===u&&"object"!==t.nodeName.toLowerCase()){for(l=Be(e),(c=t.getAttribute("id"))?f=c.replace(Et,"\\$&"):t.setAttribute("id",f),f="[id='"+f+"'] ",s=l.length;s--;)l[s]=f+Ht(l[s]);d=Nt.test(e)&&_t(t.parentNode)||t,h=l.join(",")}if(h)try{return it.apply(n,d.querySelectorAll(h)),n}catch(m){}finally{c||t.removeAttribute("id")}}}return Oe(e.replace(dt,"$1"),t,n,r)};function Tt(){var n=[];return function r(e,t){return n.push(e+" ")>Re.cacheLength&&delete r[n.shift()],r[e+" "]=t}}function At(e){return e[We]=!0,e}function Rt(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||1<<31)-(~e.sourceIndex||1<<31);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function Dt(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function Mt(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}function Bt(a){return At(function(i){return i=+i,At(function(e,t){for(var n,r=a([],e.length,i),o=r.length;o--;)e[n=r[o]]&&(e[n]=!(t[n]=e[n]))})})}function _t(e){return e&&typeof e.getElementsByTagName!==et&&e}for(Te in Ae=kt.support={},Me=kt.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},Ve=kt.setDocument=function(e){var t,s=e?e.ownerDocument||e:Ke,n=s.defaultView;return s!==Ie&&9===s.nodeType&&s.documentElement?(Fe=(Ie=s).documentElement,Ue=!Me(s),n&&n!==function r(e){try{return e.top}catch(t){}return null}(n)&&(n.addEventListener?n.addEventListener("unload",function(){Ve()},!1):n.attachEvent&&n.attachEvent("onunload",function(){Ve()})),Ae.attributes=!0,Ae.getElementsByTagName=!0,Ae.getElementsByClassName=wt.test(s.getElementsByClassName),Ae.getById=!0,Re.find.ID=function(e,t){if(typeof t.getElementById!==et&&Ue){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},Re.filter.ID=function(e){var t=e.replace(zt,St);return function(e){return e.getAttribute("id")===t}},Re.find.TAG=Ae.getElementsByTagName?function(e,t){if(typeof t.getElementsByTagName!==et)return t.getElementsByTagName(e)}:function(e,t){var n,r=[],o=0,i=t.getElementsByTagName(e);if("*"!==e)return i;for(;n=i[o++];)1===n.nodeType&&r.push(n);return r},Re.find.CLASS=Ae.getElementsByClassName&&function(e,t){if(Ue)return t.getElementsByClassName(e)},qe=[],je=[],Ae.disconnectedMatch=!0,je=je.length&&new RegExp(je.join("|")),qe=qe.length&&new RegExp(qe.join("|")),t=wt.test(Fe.compareDocumentPosition),$e=t||wt.test(Fe.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},Ze=t?function(e,t){if(e===t)return Le=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!Ae.sortDetached&&t.compareDocumentPosition(e)===n?e===s||e.ownerDocument===Ke&&$e(Ke,e)?-1:t===s||t.ownerDocument===Ke&&$e(Ke,t)?1:Pe?ut.call(Pe,e)-ut.call(Pe,t):0:4&n?-1:1)}:function(e,t){if(e===t)return Le=!0,0;var n,r=0,o=e.parentNode,i=t.parentNode,a=[e],u=[t];if(!o||!i)return e===s?-1:t===s?1:o?-1:i?1:Pe?ut.call(Pe,e)-ut.call(Pe,t):0;if(o===i)return Rt(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)u.unshift(n);for(;a[r]===u[r];)r++;return r?Rt(a[r],u[r]):a[r]===Ke?-1:u[r]===Ke?1:0},s):Ie},kt.matches=function(e,t){return kt(e,null,null,t)},kt.matchesSelector=function(e,t){if((e.ownerDocument||e)!==Ie&&Ve(e),t=t.replace(gt,"='$1']"),Ae.matchesSelector&&Ue&&(!qe||!qe.test(t))&&(!je||!je.test(t)))try{var n=(void 0).call(e,t);if(n||Ae.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch($N){}return 0<kt(t,Ie,null,[e]).length},kt.contains=function(e,t){return(e.ownerDocument||e)!==Ie&&Ve(e),$e(e,t)},kt.attr=function(e,t){(e.ownerDocument||e)!==Ie&&Ve(e);var n=Re.attrHandle[t.toLowerCase()],r=n&&tt.call(Re.attrHandle,t.toLowerCase())?n(e,t,!Ue):undefined;return r!==undefined?r:Ae.attributes||!Ue?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},kt.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},kt.uniqueSort=function(e){var t,n=[],r=0,o=0;if(Le=!Ae.detectDuplicates,Pe=!Ae.sortStable&&e.slice(0),e.sort(Ze),Le){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)e.splice(n[r],1)}return Pe=null,e},De=kt.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=De(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[r++];)n+=De(t);return n},(Re=kt.selectors={cacheLength:50,createPseudo:At,match:bt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(zt,St),e[3]=(e[3]||e[4]||e[5]||"").replace(zt,St),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||kt.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&kt.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return bt.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&pt.test(n)&&(t=Be(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(zt,St).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=Ge[e+" "];return t||(t=new RegExp("(^|"+st+")"+e+"("+st+"|$)"))&&Ge(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==et&&e.getAttribute("class")||"")})},ATTR:function(n,r,o){return function(e){var t=kt.attr(e,n);return null==t?"!="===r:!r||(t+="","="===r?t===o:"!="===r?t!==o:"^="===r?o&&0===t.indexOf(o):"*="===r?o&&-1<t.indexOf(o):"$="===r?o&&t.slice(-o.length)===o:"~="===r?-1<(" "+t+" ").indexOf(o):"|="===r&&(t===o||t.slice(0,o.length+1)===o+"-"))}},CHILD:function(h,e,t,m,g){var p="nth"!==h.slice(0,3),v="last"!==h.slice(-4),b="of-type"===e;return 1===m&&0===g?function(e){return!!e.parentNode}:function(e,t,n){var r,o,i,a,u,s,l=p!==v?"nextSibling":"previousSibling",c=e.parentNode,f=b&&e.nodeName.toLowerCase(),d=!n&&!b;if(c){if(p){for(;l;){for(i=e;i=i[l];)if(b?i.nodeName.toLowerCase()===f:1===i.nodeType)return!1;s=l="only"===h&&!s&&"nextSibling"}return!0}if(s=[v?c.firstChild:c.lastChild],v&&d){for(u=(r=(o=c[We]||(c[We]={}))[h]||[])[0]===Xe&&r[1],a=r[0]===Xe&&r[2],i=u&&c.childNodes[u];i=++u&&i&&i[l]||(a=u=0)||s.pop();)if(1===i.nodeType&&++a&&i===e){o[h]=[Xe,u,a];break}}else if(d&&(r=(e[We]||(e[We]={}))[h])&&r[0]===Xe)a=r[1];else for(;(i=++u&&i&&i[l]||(a=u=0)||s.pop())&&((b?i.nodeName.toLowerCase()!==f:1!==i.nodeType)||!++a||(d&&((i[We]||(i[We]={}))[h]=[Xe,a]),i!==e)););return(a-=g)===m||a%m==0&&0<=a/m}}},PSEUDO:function(e,i){var t,a=Re.pseudos[e]||Re.setFilters[e.toLowerCase()]||kt.error("unsupported pseudo: "+e);return a[We]?a(i):1<a.length?(t=[e,e,"",i],Re.setFilters.hasOwnProperty(e.toLowerCase())?At(function(e,t){for(var n,r=a(e,i),o=r.length;o--;)e[n=ut.call(e,r[o])]=!(t[n]=r[o])}):function(e){return a(e,0,t)}):a}},pseudos:{not:At(function(e){var r=[],o=[],u=_e(e.replace(dt,"$1"));return u[We]?At(function(e,t,n,r){for(var o,i=u(e,null,r,[]),a=e.length;a--;)(o=i[a])&&(e[a]=!(t[a]=o))}):function(e,t,n){return r[0]=e,u(r,null,n,o),!o.pop()}}),has:At(function(t){return function(e){return 0<kt(t,e).length}}),contains:At(function(t){return t=t.replace(zt,St),function(e){return-1<(e.textContent||e.innerText||De(e)).indexOf(t)}}),lang:At(function(n){return vt.test(n||"")||kt.error("unsupported lang: "+n),n=n.replace(zt,St).toLowerCase(),function(e){var t;do{if(t=Ue?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=j.window.location&&j.window.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===Fe},focus:function(e){return e===Ie.activeElement&&(!Ie.hasFocus||Ie.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!Re.pseudos.empty(e)},header:function(e){return Ct.test(e.nodeName)},input:function(e){return yt.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:Bt(function(){return[0]}),last:Bt(function(e,t){return[t-1]}),eq:Bt(function(e,t,n){return[n<0?n+t:n]}),even:Bt(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:Bt(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:Bt(function(e,t,n){for(var r=n<0?n+t:n;0<=--r;)e.push(r);return e}),gt:Bt(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=Re.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})Re.pseudos[Te]=Dt(Te);for(Te in{submit:!0,reset:!0})Re.pseudos[Te]=Mt(Te);function Ot(){}function Ht(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function Pt(a,e,t){var u=e.dir,s=t&&"parentNode"===u,l=Ye++;return e.first?function(e,t,n){for(;e=e[u];)if(1===e.nodeType||s)return a(e,t,n)}:function(e,t,n){var r,o,i=[Xe,l];if(n){for(;e=e[u];)if((1===e.nodeType||s)&&a(e,t,n))return!0}else for(;e=e[u];)if(1===e.nodeType||s){if((r=(o=e[We]||(e[We]={}))[u])&&r[0]===Xe&&r[1]===l)return i[2]=r[2];if((o[u]=i)[2]=a(e,t,n))return!0}}}function Lt(o){return 1<o.length?function(e,t,n){for(var r=o.length;r--;)if(!o[r](e,t,n))return!1;return!0}:o[0]}function Vt(e,t,n,r,o){for(var i,a=[],u=0,s=e.length,l=null!=t;u<s;u++)(i=e[u])&&(n&&!n(i,r,o)||(a.push(i),l&&t.push(u)));return a}function It(m,g,p,v,b,e){return v&&!v[We]&&(v=It(v)),b&&!b[We]&&(b=It(b,e)),At(function(e,t,n,r){var o,i,a,u=[],s=[],l=t.length,c=e||function h(e,t,n){for(var r=0,o=t.length;r<o;r++)kt(e,t[r],n);return n}(g||"*",n.nodeType?[n]:n,[]),f=!m||!e&&g?c:Vt(c,u,m,n,r),d=p?b||(e?m:l||v)?[]:t:f;if(p&&p(f,d,n,r),v)for(o=Vt(d,s),v(o,[],n,r),i=o.length;i--;)(a=o[i])&&(d[s[i]]=!(f[s[i]]=a));if(e){if(b||m){if(b){for(o=[],i=d.length;i--;)(a=d[i])&&o.push(f[i]=a);b(null,d=[],o,r)}for(i=d.length;i--;)(a=d[i])&&-1<(o=b?ut.call(e,a):u[i])&&(e[o]=!(t[o]=a))}}else d=Vt(d===t?d.splice(l,d.length):d),b?b(null,t,d,r):it.apply(t,d)})}function Ft(e){for(var r,t,n,o=e.length,i=Re.relative[e[0].type],a=i||Re.relative[" "],u=i?1:0,s=Pt(function(e){return e===r},a,!0),l=Pt(function(e){return-1<ut.call(r,e)},a,!0),c=[function(e,t,n){return!i&&(n||t!==He)||((r=t).nodeType?s(e,t,n):l(e,t,n))}];u<o;u++)if(t=Re.relative[e[u].type])c=[Pt(Lt(c),t)];else{if((t=Re.filter[e[u].type].apply(null,e[u].matches))[We]){for(n=++u;n<o&&!Re.relative[e[n].type];n++);return It(1<u&&Lt(c),1<u&&Ht(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace(dt,"$1"),t,u<n&&Ft(e.slice(u,n)),n<o&&Ft(e=e.slice(n)),n<o&&Ht(e))}c.push(t)}return Lt(c)}Ot.prototype=Re.filters=Re.pseudos,Re.setFilters=new Ot,Be=kt.tokenize=function(e,t){var n,r,o,i,a,u,s,l=Je[e+" "];if(l)return t?0:l.slice(0);for(a=e,u=[],s=Re.preFilter;a;){for(i in n&&!(r=ht.exec(a))||(r&&(a=a.slice(r[0].length)||a),u.push(o=[])),n=!1,(r=mt.exec(a))&&(n=r.shift(),o.push({value:n,type:r[0].replace(dt," ")}),a=a.slice(n.length)),Re.filter)!(r=bt[i].exec(a))||s[i]&&!(r=s[i](r))||(n=r.shift(),o.push({value:n,type:i,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?kt.error(e):Je(e,u).slice(0)},_e=kt.compile=function(e,t){var n,r=[],o=[],i=Qe[e+" "];if(!i){for(t||(t=Be(e)),n=t.length;n--;)(i=Ft(t[n]))[We]?r.push(i):o.push(i);(i=Qe(e,function a(p,v){var b=0<v.length,y=0<p.length,e=function(e,t,n,r,o){var i,a,u,s=0,l="0",c=e&&[],f=[],d=He,h=e||y&&Re.find.TAG("*",o),m=Xe+=null==d?1:Math.random()||.1,g=h.length;for(o&&(He=t!==Ie&&t);l!==g&&null!=(i=h[l]);l++){if(y&&i){for(a=0;u=p[a++];)if(u(i,t,n)){r.push(i);break}o&&(Xe=m)}b&&((i=!u&&i)&&s--,e&&c.push(i))}if(s+=l,b&&l!==s){for(a=0;u=v[a++];)u(c,f,t,n);if(e){if(0<s)for(;l--;)c[l]||f[l]||(f[l]=rt.call(r));f=Vt(f)}it.apply(r,f),o&&!e&&0<f.length&&1<s+v.length&&kt.uniqueSort(r)}return o&&(Xe=m,He=d),c};return b?At(e):e}(o,r))).selector=e}return i},Oe=kt.select=function(e,t,n,r){var o,i,a,u,s,l="function"==typeof e&&e,c=!r&&Be(e=l.selector||e);if(n=n||[],1===c.length){if(2<(i=c[0]=c[0].slice(0)).length&&"ID"===(a=i[0]).type&&Ae.getById&&9===t.nodeType&&Ue&&Re.relative[i[1].type]){if(!(t=(Re.find.ID(a.matches[0].replace(zt,St),t)||[])[0]))return n;l&&(t=t.parentNode),e=e.slice(i.shift().value.length)}for(o=bt.needsContext.test(e)?0:i.length;o--&&(a=i[o],!Re.relative[u=a.type]);)if((s=Re.find[u])&&(r=s(a.matches[0].replace(zt,St),Nt.test(i[0].type)&&_t(t.parentNode)||t))){if(i.splice(o,1),!(e=r.length&&Ht(i)))return it.apply(n,r),n;break}}return(l||_e(e,c))(r,t,!Ue,n,Nt.test(e)&&_t(t.parentNode)||t),n},Ae.sortStable=We.split("").sort(Ze).join("")===We,Ae.detectDuplicates=!!Le,Ve(),Ae.sortDetached=!0;var Ut=Array.isArray,jt=function(e,t,n){var r,o;if(!e)return 0;if(n=n||e,e.length!==undefined){for(r=0,o=e.length;r<o;r++)if(!1===t.call(n,e[r],r,e))return 0}else for(r in e)if(e.hasOwnProperty(r)&&!1===t.call(n,e[r],r,e))return 0;return 1},qt=function(e,t,n){var r,o;for(r=0,o=e.length;r<o;r++)if(t.call(n,e[r],r,e))return r;return-1},$t={isArray:Ut,toArray:function(e){var t,n,r=e;if(!Ut(e))for(r=[],t=0,n=e.length;t<n;t++)r[t]=e[t];return r},each:jt,map:function(n,r){var o=[];return jt(n,function(e,t){o.push(r(e,t,n))}),o},filter:function(n,r){var o=[];return jt(n,function(e,t){r&&!r(e,t,n)||o.push(e)}),o},indexOf:function(e,t){var n,r;if(e)for(n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},reduce:function(e,t,n,r){var o=0;for(arguments.length<3&&(n=e[0]);o<e.length;o++)n=t.call(r,n,e[o],o);return n},findIndex:qt,find:function(e,t,n){var r=qt(e,t,n);return-1!==r?e[r]:undefined},last:function(e){return e[e.length-1]}},Wt=/^\s*|\s*$/g,Kt=function(e){return null===e||e===undefined?"":(""+e).replace(Wt,"")},Xt=function(e,t){return t?!("array"!==t||!$t.isArray(e))||typeof e===t:e!==undefined},Yt=function(e,n,r,o){o=o||this,e&&(r&&(e=e[r]),$t.each(e,function(e,t){if(!1===n.call(o,e,t,r))return!1;Yt(e,n,r,o)}))},Gt={trim:Kt,isArray:$t.isArray,is:Xt,toArray:$t.toArray,makeMap:function(e,t,n){var r;for(t=t||",","string"==typeof(e=e||[])&&(e=e.split(t)),n=n||{},r=e.length;r--;)n[e[r]]={};return n},each:$t.each,map:$t.map,grep:$t.filter,inArray:$t.indexOf,hasOwn:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},extend:function(e,t){for(var n,r,o,i=[],a=2;a<arguments.length;a++)i[a-2]=arguments[a];var u,s=arguments;for(n=1,r=s.length;n<r;n++)for(o in t=s[n])t.hasOwnProperty(o)&&(u=t[o])!==undefined&&(e[o]=u);return e},create:function(e,t,n){var r,o,i,a,u,s=this,l=0;if(e=/^((static) )?([\w.]+)(:([\w.]+))?/.exec(e),i=e[3].match(/(^|\.)(\w+)$/i)[2],!(o=s.createNS(e[3].replace(/\.\w+$/,""),n))[i]){if("static"===e[2])return o[i]=t,void(this.onCreate&&this.onCreate(e[2],e[3],o[i]));t[i]||(t[i]=function(){},l=1),o[i]=t[i],s.extend(o[i].prototype,t),e[5]&&(r=s.resolve(e[5]).prototype,a=e[5].match(/\.(\w+)$/i)[1],u=o[i],o[i]=l?function(){return r[a].apply(this,arguments)}:function(){return this.parent=r[a],u.apply(this,arguments)},o[i].prototype[i]=o[i],s.each(r,function(e,t){o[i].prototype[t]=r[t]}),s.each(t,function(e,t){r[t]?o[i].prototype[t]=function(){return this.parent=r[t],e.apply(this,arguments)}:t!==i&&(o[i].prototype[t]=e)})),s.each(t["static"],function(e,t){o[i][t]=e})}},walk:Yt,createNS:function(e,t){var n,r;for(t=t||j.window,e=e.split("."),n=0;n<e.length;n++)t[r=e[n]]||(t[r]={}),t=t[r];return t},resolve:function(e,t){var n,r;for(t=t||j.window,n=0,r=(e=e.split(".")).length;n<r&&(t=t[e[n]]);n++);return t},explode:function(e,t){return!e||Xt(e,"array")?e:$t.map(e.split(t||","),Kt)},_addCacheSuffix:function(e){var t=he.cacheSuffix;return t&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e}},Jt=j.document,Qt=Array.prototype.push,Zt=Array.prototype.slice,en=/^(?:[^#<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,tn=ke.Event,nn=Gt.makeMap("children,contents,next,prev"),rn=function(e){return void 0!==e},on=function(e){return"string"==typeof e},an=function(e,t){var n,r,o;for(o=(t=t||Jt).createElement("div"),n=t.createDocumentFragment(),o.innerHTML=e;r=o.firstChild;)n.appendChild(r);return n},un=function(e,t,n,r){var o;if(on(t))t=an(t,wn(e[0]));else if(t.length&&!t.nodeType){if(t=pn.makeArray(t),r)for(o=t.length-1;0<=o;o--)un(e,t[o],n,r);else for(o=0;o<t.length;o++)un(e,t[o],n,r);return e}if(t.nodeType)for(o=e.length;o--;)n.call(e[o],t);return e},sn=function(e,t){return e&&t&&-1!==(" "+e.className+" ").indexOf(" "+t+" ")},ln=function(e,t,n){var r,o;return t=pn(t)[0],e.each(function(){var e=this;n&&r===e.parentNode||(r=e.parentNode,o=t.cloneNode(!1),e.parentNode.insertBefore(o,e)),o.appendChild(e)}),e},cn=Gt.makeMap("fillOpacity fontWeight lineHeight opacity orphans widows zIndex zoom"," "),fn=Gt.makeMap("checked compact declare defer disabled ismap multiple nohref noshade nowrap readonly selected"," "),dn={"for":"htmlFor","class":"className",readonly:"readOnly"},hn={"float":"cssFloat"},mn={},gn={},pn=function(e,t){return new pn.fn.init(e,t)},vn=/^\s*|\s*$/g,bn=function(e){return null===e||e===undefined?"":(""+e).replace(vn,"")},yn=function(e,t){var n,r,o,i;if(e)if((n=e.length)===undefined){for(r in e)if(e.hasOwnProperty(r)&&(i=e[r],!1===t.call(i,r,i)))break}else for(o=0;o<n&&(i=e[o],!1!==t.call(i,o,i));o++);return e},Cn=function(e,n){var r=[];return yn(e,function(e,t){n(t,e)&&r.push(t)}),r},wn=function(e){return e?9===e.nodeType?e:e.ownerDocument:Jt};pn.fn=pn.prototype={constructor:pn,selector:"",context:null,length:0,init:function(e,t){var n,r,o=this;if(!e)return o;if(e.nodeType)return o.context=o[0]=e,o.length=1,o;if(t&&t.nodeType)o.context=t;else{if(t)return pn(e).attr(t);o.context=t=j.document}if(on(e)){if(!(n="<"===(o.selector=e).charAt(0)&&">"===e.charAt(e.length-1)&&3<=e.length?[null,e,null]:en.exec(e)))return pn(t).find(e);if(n[1])for(r=an(e,wn(t)).firstChild;r;)Qt.call(o,r),r=r.nextSibling;else{if(!(r=wn(t).getElementById(n[2])))return o;if(r.id!==n[2])return o.find(e);o.length=1,o[0]=r}}else this.add(e,!1);return o},toArray:function(){return Gt.toArray(this)},add:function(e,t){var n,r,o=this;if(on(e))return o.add(pn(e));if(!1!==t)for(n=pn.unique(o.toArray().concat(pn.makeArray(e))),o.length=n.length,r=0;r<n.length;r++)o[r]=n[r];else Qt.apply(o,pn.makeArray(e));return o},attr:function(t,n){var e,r=this;if("object"==typeof t)yn(t,function(e,t){r.attr(e,t)});else{if(!rn(n)){if(r[0]&&1===r[0].nodeType){if((e=mn[t])&&e.get)return e.get(r[0],t);if(fn[t])return r.prop(t)?t:undefined;null===(n=r[0].getAttribute(t,2))&&(n=undefined)}return n}this.each(function(){var e;if(1===this.nodeType){if((e=mn[t])&&e.set)return void e.set(this,n);null===n?this.removeAttribute(t,2):this.setAttribute(t,n,2)}})}return r},removeAttr:function(e){return this.attr(e,null)},prop:function(e,t){var n=this;if("object"==typeof(e=dn[e]||e))yn(e,function(e,t){n.prop(e,t)});else{if(!rn(t))return n[0]&&n[0].nodeType&&e in n[0]?n[0][e]:t;this.each(function(){1===this.nodeType&&(this[e]=t)})}return n},css:function(n,r){var e,o,i=this,t=function(e){return e.replace(/-(\D)/g,function(e,t){return t.toUpperCase()})},a=function(e){return e.replace(/[A-Z]/g,function(e){return"-"+e})};if("object"==typeof n)yn(n,function(e,t){i.css(e,t)});else if(rn(r))n=t(n),"number"!=typeof r||cn[n]||(r=r.toString()+"px"),i.each(function(){var e=this.style;if((o=gn[n])&&o.set)o.set(this,r);else{try{this.style[hn[n]||n]=r}catch(t){}null!==r&&""!==r||(e.removeProperty?e.removeProperty(a(n)):e.removeAttribute(n))}});else{if(e=i[0],(o=gn[n])&&o.get)return o.get(e);if(!e.ownerDocument.defaultView)return e.currentStyle?e.currentStyle[t(n)]:"";try{return e.ownerDocument.defaultView.getComputedStyle(e,null).getPropertyValue(a(n))}catch(u){return undefined}}return i},remove:function(){for(var e,t=this.length;t--;)e=this[t],tn.clean(e),e.parentNode&&e.parentNode.removeChild(e);return this},empty:function(){for(var e,t=this.length;t--;)for(e=this[t];e.firstChild;)e.removeChild(e.firstChild);return this},html:function(e){var t,n=this;if(rn(e)){t=n.length;try{for(;t--;)n[t].innerHTML=e}catch(r){pn(n[t]).empty().append(e)}return n}return n[0]?n[0].innerHTML:""},text:function(e){var t,n=this;if(rn(e)){for(t=n.length;t--;)"innerText"in n[t]?n[t].innerText=e:n[0].textContent=e;return n}return n[0]?n[0].innerText||n[0].textContent:""},append:function(){return un(this,arguments,function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.appendChild(e)})},prepend:function(){return un(this,arguments,function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.insertBefore(e,this.firstChild)},!0)},before:function(){return this[0]&&this[0].parentNode?un(this,arguments,function(e){this.parentNode.insertBefore(e,this)}):this},after:function(){return this[0]&&this[0].parentNode?un(this,arguments,function(e){this.parentNode.insertBefore(e,this.nextSibling)},!0):this},appendTo:function(e){return pn(e).append(this),this},prependTo:function(e){return pn(e).prepend(this),this},replaceWith:function(e){return this.before(e).remove()},wrap:function(e){return ln(this,e)},wrapAll:function(e){return ln(this,e,!0)},wrapInner:function(e){return this.each(function(){pn(this).contents().wrapAll(e)}),this},unwrap:function(){return this.parent().each(function(){pn(this).replaceWith(this.childNodes)})},clone:function(){var e=[];return this.each(function(){e.push(this.cloneNode(!0))}),pn(e)},addClass:function(e){return this.toggleClass(e,!0)},removeClass:function(e){return this.toggleClass(e,!1)},toggleClass:function(o,i){var e=this;return"string"!=typeof o||(-1!==o.indexOf(" ")?yn(o.split(" "),function(){e.toggleClass(this,i)}):e.each(function(e,t){var n,r;(r=sn(t,o))!==i&&(n=t.className,r?t.className=bn((" "+n+" ").replace(" "+o+" "," ")):t.className+=n?" "+o:o)})),e},hasClass:function(e){return sn(this[0],e)},each:function(e){return yn(this,e)},on:function(e,t){return this.each(function(){tn.bind(this,e,t)})},off:function(e,t){return this.each(function(){tn.unbind(this,e,t)})},trigger:function(e){return this.each(function(){"object"==typeof e?tn.fire(this,e.type,e):tn.fire(this,e)})},show:function(){return this.css("display","")},hide:function(){return this.css("display","none")},slice:function(){return new pn(Zt.apply(this,arguments))},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},find:function(e){var t,n,r=[];for(t=0,n=this.length;t<n;t++)pn.find(e,this[t],r);return pn(r)},filter:function(n){return pn("function"==typeof n?Cn(this.toArray(),function(e,t){return n(t,e)}):pn.filter(n,this.toArray()))},closest:function(n){var r=[];return n instanceof pn&&(n=n[0]),this.each(function(e,t){for(;t;){if("string"==typeof n&&pn(t).is(n)){r.push(t);break}if(t===n){r.push(t);break}t=t.parentNode}}),pn(r)},offset:function(e){var t,n,r,o,i=0,a=0;return e?this.css(e):((t=this[0])&&(r=(n=t.ownerDocument).documentElement,t.getBoundingClientRect&&(i=(o=t.getBoundingClientRect()).left+(r.scrollLeft||n.body.scrollLeft)-r.clientLeft,a=o.top+(r.scrollTop||n.body.scrollTop)-r.clientTop)),{left:i,top:a})},push:Qt,sort:[].sort,splice:[].splice},Gt.extend(pn,{extend:Gt.extend,makeArray:function(e){return(t=e)&&t===t.window||e.nodeType?[e]:Gt.toArray(e);var t},inArray:function(e,t){var n;if(t.indexOf)return t.indexOf(e);for(n=t.length;n--;)if(t[n]===e)return n;return-1},isArray:Gt.isArray,each:yn,trim:bn,grep:Cn,find:kt,expr:kt.selectors,unique:kt.uniqueSort,text:kt.getText,contains:kt.contains,filter:function(e,t,n){var r=t.length;for(n&&(e=":not("+e+")");r--;)1!==t[r].nodeType&&t.splice(r,1);return t=1===t.length?pn.find.matchesSelector(t[0],e)?[t[0]]:[]:pn.find.matches(e,t)}});var xn=function(e,t,n){var r=[],o=e[t];for("string"!=typeof n&&n instanceof pn&&(n=n[0]);o&&9!==o.nodeType;){if(n!==undefined){if(o===n)break;if("string"==typeof n&&pn(o).is(n))break}1===o.nodeType&&r.push(o),o=o[t]}return r},Nn=function(e,t,n,r){var o=[];for(r instanceof pn&&(r=r[0]);e;e=e[t])if(!n||e.nodeType===n){if(r!==undefined){if(e===r)break;if("string"==typeof r&&pn(e).is(r))break}o.push(e)}return o},En=function(e,t,n){for(e=e[t];e;e=e[t])if(e.nodeType===n)return e;return null};yn({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return xn(e,"parentNode")},next:function(e){return En(e,"nextSibling",1)},prev:function(e){return En(e,"previousSibling",1)},children:function(e){return Nn(e.firstChild,"nextSibling",1)},contents:function(e){return Gt.toArray(("iframe"===e.nodeName?e.contentDocument||e.contentWindow.document:e).childNodes)}},function(e,r){pn.fn[e]=function(t){var n=[];return this.each(function(){var e=r.call(n,this,t,n);e&&(pn.isArray(e)?n.push.apply(n,e):n.push(e))}),1<this.length&&(nn[e]||(n=pn.unique(n)),0===e.indexOf("parents")&&(n=n.reverse())),n=pn(n),t?n.filter(t):n}}),yn({parentsUntil:function(e,t){return xn(e,"parentNode",t)},nextUntil:function(e,t){return Nn(e,"nextSibling",1,t).slice(1)},prevUntil:function(e,t){return Nn(e,"previousSibling",1,t).slice(1)}},function(r,o){pn.fn[r]=function(t,e){var n=[];return this.each(function(){var e=o.call(n,this,t,n);e&&(pn.isArray(e)?n.push.apply(n,e):n.push(e))}),1<this.length&&(n=pn.unique(n),0!==r.indexOf("parents")&&"prevUntil"!==r||(n=n.reverse())),n=pn(n),e?n.filter(e):n}}),pn.fn.is=function(e){return!!e&&0<this.filter(e).length},pn.fn.init.prototype=pn.fn,pn.overrideDefaults=function(n){var r,o=function(e,t){return r=r||n(),0===arguments.length&&(e=r.element),t||(t=r.context),new o.fn.init(e,t)};return pn.extend(o,this),o};var zn=function(n,r,e){yn(e,function(e,t){n[e]=n[e]||{},n[e][r]=t})};he.ie&&he.ie<8&&(zn(mn,"get",{maxlength:function(e){var t=e.maxLength;return 2147483647===t?undefined:t},size:function(e){var t=e.size;return 20===t?undefined:t},"class":function(e){return e.className},style:function(e){var t=e.style.cssText;return 0===t.length?undefined:t}}),zn(mn,"set",{"class":function(e,t){e.className=t},style:function(e,t){e.style.cssText=t}})),he.ie&&he.ie<9&&(hn["float"]="styleFloat",zn(gn,"set",{opacity:function(e,t){var n=e.style;null===t||""===t?n.removeAttribute("filter"):(n.zoom=1,n.filter="alpha(opacity="+100*t+")")}})),pn.attrHooks=mn,pn.cssHooks=gn;var Sn,kn,Tn,An=function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}return undefined}(e,t);if(!n)return{major:0,minor:0};var r=function(e){return Number(t.replace(n,"$"+e))};return Dn(r(1),r(2))},Rn=function(){return Dn(0,0)},Dn=function(e,t){return{major:e,minor:t}},Mn={nu:Dn,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?Rn():An(e,n)},unknown:Rn},Bn="Firefox",_n=function(e,t){return function(){return t===e}},On=function(e){var t=e.current;return{current:t,version:e.version,isEdge:_n("Edge",t),isChrome:_n("Chrome",t),isIE:_n("IE",t),isOpera:_n("Opera",t),isFirefox:_n(Bn,t),isSafari:_n("Safari",t)}},Hn={unknown:function(){return On({current:undefined,version:Mn.unknown()})},nu:On,edge:$("Edge"),chrome:$("Chrome"),ie:$("IE"),opera:$("Opera"),firefox:$(Bn),safari:$("Safari")},Pn="Windows",Ln="Android",Vn="Solaris",In="FreeBSD",Fn=function(e,t){return function(){return t===e}},Un=function(e){var t=e.current;return{current:t,version:e.version,isWindows:Fn(Pn,t),isiOS:Fn("iOS",t),isAndroid:Fn(Ln,t),isOSX:Fn("OSX",t),isLinux:Fn("Linux",t),isSolaris:Fn(Vn,t),isFreeBSD:Fn(In,t)}},jn={unknown:function(){return Un({current:undefined,version:Mn.unknown()})},nu:Un,windows:$(Pn),ios:$("iOS"),android:$(Ln),linux:$("Linux"),osx:$("OSX"),solaris:$(Vn),freebsd:$(In)},qn=function(e,t){var n=String(t).toLowerCase();return F(e,function(e){return e.search(n)})},$n=function(e,n){return qn(e,n).map(function(e){var t=Mn.detect(e.versionRegexes,n);return{current:e.name,version:t}})},Wn=function(e,n){return qn(e,n).map(function(e){var t=Mn.detect(e.versionRegexes,n);return{current:e.name,version:t}})},Kn=function(e,t){return-1!==e.indexOf(t)},Xn=function(e){return e.replace(/^\s+|\s+$/g,"")},Yn=function(e){return e.replace(/\s+$/g,"")},Gn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Jn=function(t){return function(e){return Kn(e,t)}},Qn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return Kn(e,"edge/")&&Kn(e,"chrome")&&Kn(e,"safari")&&Kn(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Gn],search:function(e){return Kn(e,"chrome")&&!Kn(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return Kn(e,"msie")||Kn(e,"trident")}},{name:"Opera",versionRegexes:[Gn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Jn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Jn("firefox")},{name:"Safari",versionRegexes:[Gn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(Kn(e,"safari")||Kn(e,"mobile/"))&&Kn(e,"applewebkit")}}],Zn=[{name:"Windows",search:Jn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return Kn(e,"iphone")||Kn(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Jn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Jn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Jn("linux"),versionRegexes:[]},{name:"Solaris",search:Jn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Jn("freebsd"),versionRegexes:[]}],er={browsers:$(Qn),oses:$(Zn)},tr=function(e){var t,n,r,o,i,a,u,s,l,c,f,d=er.browsers(),h=er.oses(),m=$n(d,e).fold(Hn.unknown,Hn.nu),g=Wn(h,e).fold(jn.unknown,jn.nu);return{browser:m,os:g,deviceType:(n=m,r=e,o=(t=g).isiOS()&&!0===/ipad/i.test(r),i=t.isiOS()&&!o,a=t.isAndroid()&&3===t.version.major,u=t.isAndroid()&&4===t.version.major,s=o||a||u&&!0===/mobile/i.test(r),l=t.isiOS()||t.isAndroid(),c=l&&!s,f=n.isSafari()&&t.isiOS()&&!1===/safari/i.test(r),{isiPad:$(o),isiPhone:$(i),isTablet:$(s),isPhone:$(c),isTouch:$(l),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:$(f)})}},nr={detect:(Sn=function(){var e=j.navigator.userAgent;return tr(e)},Tn=!1,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Tn||(Tn=!0,kn=Sn.apply(null,e)),kn})},rr=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:$(e)}},or={fromHtml:function(e,t){var n=(t||j.document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw j.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return rr(n.childNodes[0])},fromTag:function(e,t){var n=(t||j.document).createElement(e);return rr(n)},fromText:function(e,t){var n=(t||j.document).createTextNode(e);return rr(n)},fromDom:rr,fromPoint:function(e,t,n){var r=e.dom();return T.from(r.elementFromPoint(t,n)).map(rr)}},ir=(j.Node.ATTRIBUTE_NODE,j.Node.CDATA_SECTION_NODE,j.Node.COMMENT_NODE,j.Node.DOCUMENT_NODE),ar=(j.Node.DOCUMENT_TYPE_NODE,j.Node.DOCUMENT_FRAGMENT_NODE,j.Node.ELEMENT_NODE),ur=j.Node.TEXT_NODE,sr=(j.Node.PROCESSING_INSTRUCTION_NODE,j.Node.ENTITY_REFERENCE_NODE,j.Node.ENTITY_NODE,j.Node.NOTATION_NODE,function(e){return e.dom().nodeName.toLowerCase()}),lr=function(t){return function(e){return e.dom().nodeType===t}},cr=lr(ar),fr=lr(ur),dr=Object.keys,hr=Object.hasOwnProperty,mr=function(e,t){for(var n=dr(e),r=0,o=n.length;r<o;r++){var i=n[r];t(e[i],i,e)}},gr=function(r,o){var i={};return mr(r,function(e,t){var n=o(e,t,r);i[n.k]=n.v}),i},pr=function(e,t){return vr(e,t)?T.some(e[t]):T.none()},vr=function(e,t){return hr.call(e,t)},br=function(e,t,n){if(!(A(n)||B(n)||O(n)))throw j.console.error("Invalid call to Attr.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},yr=function(e,t,n){br(e.dom(),t,n)},Cr=function(e,t){var n=e.dom();mr(t,function(e,t){br(n,t,e)})},wr=function(e,t){var n=e.dom().getAttribute(t);return null===n?undefined:n},xr=function(e,t){e.dom().removeAttribute(t)},Nr=function(e,t){var n,r,o=e.dom(),i=j.window.getComputedStyle(o).getPropertyValue(t),a=""!==i||(r=fr(n=e)?n.dom().parentNode:n.dom())!==undefined&&null!==r&&r.ownerDocument.body.contains(r)?i:Er(o,t);return null===a?undefined:a},Er=function(e,t){return e.style!==undefined?e.style.getPropertyValue(t):""},zr=function(e,t){var n=e.dom(),r=Er(n,t);return T.from(r).filter(function(e){return 0<e.length})},Sr=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(t.length!==n.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+n.length+" arguments");var r={};return L(t,function(e,t){r[e]=$(n[t])}),r}},kr=function(e,t){for(var n=[],r=function(e){return n.push(e),t(e)},o=t(e);(o=o.bind(r)).isSome(););return n},Tr=function(){return ae.getOrDie("Node")},Ar=function(e,t,n){return 0!=(e.compareDocumentPosition(t)&n)},Rr=function(e,t){return Ar(e,t,Tr().DOCUMENT_POSITION_CONTAINED_BY)},Dr=ar,Mr=ir,Br=function(e,t){var n=e.dom();if(n.nodeType!==Dr)return!1;if(n.matches!==undefined)return n.matches(t);if(n.msMatchesSelector!==undefined)return n.msMatchesSelector(t);if(n.webkitMatchesSelector!==undefined)return n.webkitMatchesSelector(t);if(n.mozMatchesSelector!==undefined)return n.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},_r=function(e){return e.nodeType!==Dr&&e.nodeType!==Mr||0===e.childElementCount},Or=function(e,t){return e.dom()===t.dom()},Hr=nr.detect().browser.isIE()?function(e,t){return Rr(e.dom(),t.dom())}:function(e,t){var n=e.dom(),r=t.dom();return n!==r&&n.contains(r)},Pr=function(e){return or.fromDom(e.dom().ownerDocument)},Lr=function(e){var t=e.dom();return T.from(t.parentNode).map(or.fromDom)},Vr=function(e){var t=e.dom();return T.from(t.previousSibling).map(or.fromDom)},Ir=function(e){var t=e.dom();return T.from(t.nextSibling).map(or.fromDom)},Fr=function(e){return t=kr(e,Vr),(n=Z.call(t,0)).reverse(),n;var t,n},Ur=function(e){return kr(e,Ir)},jr=function(e){var t=e.dom();return K(t.childNodes,or.fromDom)},qr=function(e,t){var n=e.dom().childNodes;return T.from(n[t]).map(or.fromDom)},$r=function(e){return qr(e,0)},Wr=function(e){return qr(e,e.dom().childNodes.length-1)},Kr=(Sr("element","offset"),nr.detect().browser),Xr=function(e){return F(e,cr)},Yr={getPos:function(e,t,n){var r,o,i,a=0,u=0,s=e.ownerDocument;if(n=n||e,t){if(n===e&&t.getBoundingClientRect&&"static"===Nr(or.fromDom(e),"position"))return{x:a=(o=t.getBoundingClientRect()).left+(s.documentElement.scrollLeft||e.scrollLeft)-s.documentElement.clientLeft,y:u=o.top+(s.documentElement.scrollTop||e.scrollTop)-s.documentElement.clientTop};for(r=t;r&&r!==n&&r.nodeType;)a+=r.offsetLeft||0,u+=r.offsetTop||0,r=r.offsetParent;for(r=t.parentNode;r&&r!==n&&r.nodeType;)a-=r.scrollLeft||0,u-=r.scrollTop||0,r=r.parentNode;u+=(i=or.fromDom(t),Kr.isFirefox()&&"table"===sr(i)?Xr(jr(i)).filter(function(e){return"caption"===sr(e)}).bind(function(o){return Xr(Ur(o)).map(function(e){var t=e.dom().offsetTop,n=o.dom().offsetTop,r=o.dom().offsetHeight;return t<=n?-r:0})}).getOr(0):0)}return{x:a,y:u}}},Gr=function(e){var n=T.none(),t=[],r=function(e){o()?a(e):t.push(e)},o=function(){return n.isSome()},i=function(e){L(e,a)},a=function(t){n.each(function(e){setTimeout(function(){t(e)},0)})};return e(function(e){n=T.some(e),i(t),t=[]}),{get:r,map:function(n){return Gr(function(t){r(function(e){t(n(e))})})},isReady:o}},Jr={nu:Gr,pure:function(t){return Gr(function(e){e(t)})}},Qr=function(t){var e=function(e){var r;t((r=e,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=this;setTimeout(function(){r.apply(n,e)},0)}))},n=function(){return Jr.nu(e)};return{map:function(r){return Qr(function(n){e(function(e){var t=r(e);n(t)})})},bind:function(n){return Qr(function(t){e(function(e){n(e).get(t)})})},anonBind:function(n){return Qr(function(t){e(function(e){n.get(t)})})},toLazy:n,toCached:function(){var t=null;return Qr(function(e){null===t&&(t=n()),t.get(e)})},get:e}},Zr={nu:Qr,pure:function(t){return Qr(function(e){e(t)})}},eo=function(a,e){return e(function(r){var o=[],i=0;0===a.length?r([]):L(a,function(e,t){var n;e.get((n=t,function(e){o[n]=e,++i>=a.length&&r(o)}))})})},to=function(e){return eo(e,Zr.nu)},no=function(n){return{is:function(e){return n===e},isValue:w,isError:C,getOr:$(n),getOrThunk:$(n),getOrDie:$(n),or:function(e){return no(n)},orThunk:function(e){return no(n)},fold:function(e,t){return t(n)},map:function(e){return no(e(n))},mapError:function(e){return no(n)},each:function(e){e(n)},bind:function(e){return e(n)},exists:function(e){return e(n)},forall:function(e){return e(n)},toOption:function(){return T.some(n)}}},ro=function(n){return{is:C,isValue:C,isError:w,getOr:W,getOrThunk:function(e){return e()},getOrDie:function(){return e=String(n),function(){throw new Error(e)}();var e},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,t){return e(n)},map:function(e){return ro(n)},mapError:function(e){return ro(e(n))},each:o,bind:function(e){return ro(n)},exists:C,forall:w,toOption:T.none}},oo={value:no,error:ro};function io(e,u){var t=e,n=function(e,t,n,r){var o,i;if(e){if(!r&&e[t])return e[t];if(e!==u){if(o=e[n])return o;for(i=e.parentNode;i&&i!==u;i=i.parentNode)if(o=i[n])return o}}};this.current=function(){return t},this.next=function(e){return t=n(t,"firstChild","nextSibling",e)},this.prev=function(e){return t=n(t,"lastChild","previousSibling",e)},this.prev2=function(e){return t=function(e,t,n,r){var o,i,a;if(e){if(o=e[n],u&&o===u)return;if(o){if(!r)for(a=o[t];a;a=a[t])if(!a[t])return a;return o}if((i=e.parentNode)&&i!==u)return i}}(t,"lastChild","previousSibling",e)}}var ao,uo,so,lo=function(t){var n;return function(e){return(n=n||function(e,t){for(var n={},r=0,o=e.length;r<o;r++){var i=e[r];n[String(i)]=t(i,r)}return n}(t,$(!0))).hasOwnProperty(sr(e))}},co=lo(["h1","h2","h3","h4","h5","h6"]),fo=lo(["article","aside","details","div","dt","figcaption","footer","form","fieldset","header","hgroup","html","main","nav","section","summary","body","p","dl","multicol","dd","figure","address","center","blockquote","h1","h2","h3","h4","h5","h6","listing","xmp","pre","plaintext","menu","dir","ul","ol","li","hr","table","tbody","thead","tfoot","th","tr","td","caption"]),ho=function(e){return cr(e)&&!fo(e)},mo=function(e){return cr(e)&&"br"===sr(e)},go=lo(["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"]),po=lo(["ul","ol","dl"]),vo=lo(["li","dd","dt"]),bo=lo(["area","base","basefont","br","col","frame","hr","img","input","isindex","link","meta","param","embed","source","wbr","track"]),yo=lo(["thead","tbody","tfoot"]),Co=lo(["td","th"]),wo=lo(["pre","script","textarea","style"]),xo=function(t){return function(e){return!!e&&e.nodeType===t}},No=xo(1),Eo=function(e){var r=e.toLowerCase().split(" ");return function(e){var t,n;if(e&&e.nodeType)for(n=e.nodeName.toLowerCase(),t=0;t<r.length;t++)if(n===r[t])return!0;return!1}},zo=function(t){return function(e){if(No(e)){if(e.contentEditable===t)return!0;if(e.getAttribute("data-mce-contenteditable")===t)return!0}return!1}},So=xo(3),ko=xo(8),To=xo(9),Ao=xo(11),Ro=Eo("br"),Do=zo("true"),Mo=zo("false"),Bo={isText:So,isElement:No,isComment:ko,isDocument:To,isDocumentFragment:Ao,isBr:Ro,isContentEditableTrue:Do,isContentEditableFalse:Mo,matchNodeNames:Eo,hasPropValue:function(t,n){return function(e){return No(e)&&e[t]===n}},hasAttribute:function(t,e){return function(e){return No(e)&&e.hasAttribute(t)}},hasAttributeValue:function(t,n){return function(e){return No(e)&&e.getAttribute(t)===n}},matchStyleValues:function(r,e){var o=e.toLowerCase().split(" ");return function(e){var t;if(No(e))for(t=0;t<o.length;t++){var n=e.ownerDocument.defaultView.getComputedStyle(e,null);if((n?n.getPropertyValue(r):null)===o[t])return!0}return!1}},isBogus:function(e){return No(e)&&e.hasAttribute("data-mce-bogus")},isBogusAll:function(e){return No(e)&&"all"===e.getAttribute("data-mce-bogus")},isTable:function(e){return No(e)&&"TABLE"===e.tagName}},_o=function(e){return e&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type")},Oo=function(e,t){var n,r=t.childNodes;if(!Bo.isElement(t)||!_o(t)){for(n=r.length-1;0<=n;n--)Oo(e,r[n]);if(!1===Bo.isDocument(t)){if(Bo.isText(t)&&0<t.nodeValue.length){var o=Gt.trim(t.nodeValue).length;if(e.isBlock(t.parentNode)||0<o)return;if(0===o&&(a=(i=t).previousSibling&&"SPAN"===i.previousSibling.nodeName,u=i.nextSibling&&"SPAN"===i.nextSibling.nodeName,a&&u))return}else if(Bo.isElement(t)&&(1===(r=t.childNodes).length&&_o(r[0])&&t.parentNode.insertBefore(r[0],t),r.length||bo(or.fromDom(t))))return;e.remove(t)}var i,a,u;return t}},Ho={trimNode:Oo},Po=Gt.makeMap,Lo=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Vo=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Io=/[<>&\"\']/g,Fo=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,Uo={128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"};uo={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},so={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"};var jo=function(e,t){var n,r,o,i={};if(e){for(e=e.split(","),t=t||10,n=0;n<e.length;n+=2)r=String.fromCharCode(parseInt(e[n],t)),uo[r]||(o="&"+e[n+1]+";",i[r]=o,i[o]=r);return i}};ao=jo("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32);var qo=function(e,t){return e.replace(t?Lo:Vo,function(e){return uo[e]||e})},$o=function(e,t){return e.replace(t?Lo:Vo,function(e){return 1<e.length?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":uo[e]||"&#"+e.charCodeAt(0)+";"})},Wo=function(e,t,n){return n=n||ao,e.replace(t?Lo:Vo,function(e){return uo[e]||n[e]||e})},Ko={encodeRaw:qo,encodeAllRaw:function(e){return(""+e).replace(Io,function(e){return uo[e]||e})},encodeNumeric:$o,encodeNamed:Wo,getEncodeFunc:function(e,t){var n=jo(t)||ao,r=Po(e.replace(/\+/g,","));return r.named&&r.numeric?function(e,t){return e.replace(t?Lo:Vo,function(e){return uo[e]!==undefined?uo[e]:n[e]!==undefined?n[e]:1<e.length?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":"&#"+e.charCodeAt(0)+";"})}:r.named?t?function(e,t){return Wo(e,t,n)}:Wo:r.numeric?$o:qo},decode:function(e){return e.replace(Fo,function(e,t){return t?65535<(t="x"===t.charAt(0).toLowerCase()?parseInt(t.substr(1),16):parseInt(t,10))?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):Uo[t]||String.fromCharCode(t):so[e]||ao[e]||(n=e,(r=or.fromTag("div").dom()).innerHTML=n,r.textContent||r.innerText||n);var n,r})}},Xo={},Yo={},Go=Gt.makeMap,Jo=Gt.each,Qo=Gt.extend,Zo=Gt.explode,ei=Gt.inArray,ti=function(e,t){return(e=Gt.trim(e))?e.split(t||" "):[]},ni=function(e){var u,t,n,r,o,i,s={},a=function(e,t,n){var r,o,i,a=function(e,t){var n,r,o={};for(n=0,r=e.length;n<r;n++)o[e[n]]=t||{};return o};for(t=t||"","string"==typeof(n=n||[])&&(n=ti(n)),r=(e=ti(e)).length;r--;)i={attributes:a(o=ti([u,t].join(" "))),attributesOrder:o,children:a(n,Yo)},s[e[r]]=i},l=function(e,t){var n,r,o,i;for(n=(e=ti(e)).length,t=ti(t);n--;)for(r=s[e[n]],o=0,i=t.length;o<i;o++)r.attributes[t[o]]={},r.attributesOrder.push(t[o])};return Xo[e]?Xo[e]:(u="id accesskey class dir lang style tabindex title role",t="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",n="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==e&&(u+=" contenteditable contextmenu draggable dropzone hidden spellcheck translate",t+=" article aside details dialog figure main header footer hgroup section nav",n+=" audio canvas command datalist mark meter output picture progress time wbr video ruby bdi keygen"),"html5-strict"!==e&&(u+=" xml:lang",n=[n,i="acronym applet basefont big font strike tt"].join(" "),Jo(ti(i),function(e){a(e,"",n)}),t=[t,o="center dir isindex noframes"].join(" "),r=[t,n].join(" "),Jo(ti(o),function(e){a(e,"",r)})),r=r||[t,n].join(" "),a("html","manifest","head body"),a("head","","base command link meta noscript script style title"),a("title hr noscript br"),a("base","href target"),a("link","href rel media hreflang type sizes hreflang"),a("meta","name http-equiv content charset"),a("style","media type scoped"),a("script","src async defer type charset"),a("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",r),a("address dt dd div caption","",r),a("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",n),a("blockquote","cite",r),a("ol","reversed start type","li"),a("ul","","li"),a("li","value",r),a("dl","","dt dd"),a("a","href target rel media hreflang type",n),a("q","cite",n),a("ins del","cite datetime",r),a("img","src sizes srcset alt usemap ismap width height"),a("iframe","src name width height",r),a("embed","src type width height"),a("object","data type typemustmatch name usemap form width height",[r,"param"].join(" ")),a("param","name value"),a("map","name",[r,"area"].join(" ")),a("area","alt coords shape href target rel media hreflang type"),a("table","border","caption colgroup thead tfoot tbody tr"+("html4"===e?" col":"")),a("colgroup","span","col"),a("col","span"),a("tbody thead tfoot","","tr"),a("tr","","td th"),a("td","colspan rowspan headers",r),a("th","colspan rowspan headers scope abbr",r),a("form","accept-charset action autocomplete enctype method name novalidate target",r),a("fieldset","disabled form name",[r,"legend"].join(" ")),a("label","form for",n),a("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),a("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===e?r:n),a("select","disabled form multiple name required size","option optgroup"),a("optgroup","disabled label","option"),a("option","disabled label selected value"),a("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),a("menu","type label",[r,"li"].join(" ")),a("noscript","",r),"html4"!==e&&(a("wbr"),a("ruby","",[n,"rt rp"].join(" ")),a("figcaption","",r),a("mark rt rp summary bdi","",n),a("canvas","width height",r),a("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[r,"track source"].join(" ")),a("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[r,"track source"].join(" ")),a("picture","","img source"),a("source","src srcset type media sizes"),a("track","kind src srclang label default"),a("datalist","",[n,"option"].join(" ")),a("article section nav aside main header footer","",r),a("hgroup","","h1 h2 h3 h4 h5 h6"),a("figure","",[r,"figcaption"].join(" ")),a("time","datetime",n),a("dialog","open",r),a("command","type label icon disabled checked radiogroup command"),a("output","for form name",n),a("progress","value max",n),a("meter","value min max low high optimum",n),a("details","open",[r,"summary"].join(" ")),a("keygen","autofocus challenge disabled form keytype name")),"html5-strict"!==e&&(l("script","language xml:space"),l("style","xml:space"),l("object","declare classid code codebase codetype archive standby align border hspace vspace"),l("embed","align name hspace vspace"),l("param","valuetype type"),l("a","charset name rev shape coords"),l("br","clear"),l("applet","codebase archive code object alt name width height align hspace vspace"),l("img","name longdesc align border hspace vspace"),l("iframe","longdesc frameborder marginwidth marginheight scrolling align"),l("font basefont","size color face"),l("input","usemap align"),l("select","onchange"),l("textarea"),l("h1 h2 h3 h4 h5 h6 div p legend caption","align"),l("ul","type compact"),l("li","type"),l("ol dl menu dir","compact"),l("pre","width xml:space"),l("hr","align noshade size width"),l("isindex","prompt"),l("table","summary width frame rules cellspacing cellpadding align bgcolor"),l("col","width align char charoff valign"),l("colgroup","width align char charoff valign"),l("thead","align char charoff valign"),l("tr","align char charoff valign bgcolor"),l("th","axis align char charoff valign nowrap bgcolor width height"),l("form","accept"),l("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),l("tfoot","align char charoff valign"),l("tbody","align char charoff valign"),l("area","nohref"),l("body","background bgcolor text link vlink alink")),"html4"!==e&&(l("input button select textarea","autofocus"),l("input textarea","placeholder"),l("a","download"),l("link script img","crossorigin"),l("iframe","sandbox seamless allowfullscreen")),Jo(ti("a form meter progress dfn"),function(e){s[e]&&delete s[e].children[e]}),delete s.caption.children.table,delete s.script,Xo[e]=s)},ri=function(e,n){var r;return e&&(r={},"string"==typeof e&&(e={"*":e}),Jo(e,function(e,t){r[t]=r[t.toUpperCase()]="map"===n?Go(e,/[, ]/):Zo(e,/[, ]/)})),r};function oi(i){var e,t,n,r,o,a,u,s,l,c,f,d,h,N={},m={},E=[],g={},p={},v=function(e,t,n){var r=i[e];return r?r=Go(r,/[, ]/,Go(r.toUpperCase(),/[, ]/)):(r=Xo[e])||(r=Go(t," ",Go(t.toUpperCase()," ")),r=Qo(r,n),Xo[e]=r),r};n=ni((i=i||{}).schema),!1===i.verify_html&&(i.valid_elements="*[*]"),e=ri(i.valid_styles),t=ri(i.invalid_styles,"map"),s=ri(i.valid_classes,"map"),r=v("whitespace_elements","pre script noscript style textarea video audio iframe object code"),o=v("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),a=v("short_ended_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),u=v("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls"),c=v("non_empty_elements","td th iframe video audio object script pre code",a),f=v("move_caret_before_on_enter_elements","table",c),d=v("text_block_elements","h1 h2 h3 h4 h5 h6 p div address pre form blockquote center dir fieldset header footer article section hgroup aside main nav figure"),l=v("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption details summary",d),h=v("text_inline_elements","span strong b em i font strike u var cite dfn code mark q sup sub samp"),Jo((i.special||"script noscript noframes noembed title style textarea xmp").split(" "),function(e){p[e]=new RegExp("</"+e+"[^>]*>","gi")});var z=function(e){return new RegExp("^"+e.replace(/([?+*])/g,".$1")+"$")},b=function(e){var t,n,r,o,i,a,u,s,l,c,f,d,h,m,g,p,v,b,y,C=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)\])?$/,w=/^([!\-])?(\w+[\\:]:\w+|[^=:<]+)?(?:([=:<])(.*))?$/,x=/[*?+]/;if(e)for(e=ti(e,","),N["@"]&&(p=N["@"].attributes,v=N["@"].attributesOrder),t=0,n=e.length;t<n;t++)if(i=C.exec(e[t])){if(m=i[1],l=i[2],g=i[3],s=i[5],a={attributes:d={},attributesOrder:h=[]},"#"===m&&(a.paddEmpty=!0),"-"===m&&(a.removeEmpty=!0),"!"===i[4]&&(a.removeEmptyAttrs=!0),p){for(b in p)d[b]=p[b];h.push.apply(h,v)}if(s)for(r=0,o=(s=ti(s,"|")).length;r<o;r++)if(i=w.exec(s[r])){if(u={},f=i[1],c=i[2].replace(/[\\:]:/g,":"),m=i[3],y=i[4],"!"===f&&(a.attributesRequired=a.attributesRequired||[],a.attributesRequired.push(c),u.required=!0),"-"===f){delete d[c],h.splice(ei(h,c),1);continue}m&&("="===m&&(a.attributesDefault=a.attributesDefault||[],a.attributesDefault.push({name:c,value:y}),u.defaultValue=y),":"===m&&(a.attributesForced=a.attributesForced||[],a.attributesForced.push({name:c,value:y}),u.forcedValue=y),"<"===m&&(u.validValues=Go(y,"?"))),x.test(c)?(a.attributePatterns=a.attributePatterns||[],u.pattern=z(c),a.attributePatterns.push(u)):(d[c]||h.push(c),d[c]=u)}p||"@"!==l||(p=d,v=h),g&&(a.outputName=l,N[g]=a),x.test(l)?(a.pattern=z(l),E.push(a)):N[l]=a}},y=function(e){N={},E=[],b(e),Jo(n,function(e,t){m[t]=e.children})},C=function(e){var a=/^(~)?(.+)$/;e&&(Xo.text_block_elements=Xo.block_elements=null,Jo(ti(e,","),function(e){var t=a.exec(e),n="~"===t[1],r=n?"span":"div",o=t[2];if(m[o]=m[r],g[o]=r,n||(l[o.toUpperCase()]={},l[o]={}),!N[o]){var i=N[r];delete(i=Qo({},i)).removeEmptyAttrs,delete i.removeEmpty,N[o]=i}Jo(m,function(e,t){e[r]&&(m[t]=e=Qo({},m[t]),e[o]=e[r])})}))},w=function(e){var o=/^([+\-]?)(\w+)\[([^\]]+)\]$/;Xo[i.schema]=null,e&&Jo(ti(e,","),function(e){var t,n,r=o.exec(e);r&&(n=r[1],t=n?m[r[2]]:m[r[2]]={"#comment":{}},t=m[r[2]],Jo(ti(r[3],"|"),function(e){"-"===n?delete t[e]:t[e]={}}))})},x=function(e){var t,n=N[e];if(n)return n;for(t=E.length;t--;)if((n=E[t]).pattern.test(e))return n};return i.valid_elements?y(i.valid_elements):(Jo(n,function(e,t){N[t]={attributes:e.attributes,attributesOrder:e.attributesOrder},m[t]=e.children}),"html5"!==i.schema&&Jo(ti("strong/b em/i"),function(e){e=ti(e,"/"),N[e[1]].outputName=e[0]}),Jo(ti("ol ul sub sup blockquote span font a table tbody tr strong em b i"),function(e){N[e]&&(N[e].removeEmpty=!0)}),Jo(ti("p h1 h2 h3 h4 h5 h6 th td pre div address caption li"),function(e){N[e].paddEmpty=!0}),Jo(ti("span"),function(e){N[e].removeEmptyAttrs=!0})),C(i.custom_elements),w(i.valid_children),b(i.extended_valid_elements),w("+ol[ul|ol],+ul[ul|ol]"),Jo({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},function(e,t){N[t]&&(N[t].parentsRequired=ti(e))}),i.invalid_elements&&Jo(Zo(i.invalid_elements),function(e){N[e]&&delete N[e]}),x("span")||b("span[!data-mce-type|*]"),{children:m,elements:N,getValidStyles:function(){return e},getValidClasses:function(){return s},getBlockElements:function(){return l},getInvalidStyles:function(){return t},getShortEndedElements:function(){return a},getTextBlockElements:function(){return d},getTextInlineElements:function(){return h},getBoolAttrs:function(){return u},getElementRule:x,getSelfClosingElements:function(){return o},getNonEmptyElements:function(){return c},getMoveCaretBeforeOnEnterElements:function(){return f},getWhiteSpaceElements:function(){return r},getSpecialElements:function(){return p},isValidChild:function(e,t){var n=m[e.toLowerCase()];return!(!n||!n[t.toLowerCase()])},isValid:function(e,t){var n,r,o=x(e);if(o){if(!t)return!0;if(o.attributes[t])return!0;if(n=o.attributePatterns)for(r=n.length;r--;)if(n[r].pattern.test(e))return!0}return!1},getCustomElements:function(){return g},addValidElements:b,setValidElements:y,addCustomElements:C,addValidChildren:w}}var ii=function(e,t,n,r){var o=function(e){return 1<(e=parseInt(e,10).toString(16)).length?e:"0"+e};return"#"+o(t)+o(n)+o(r)};function ai(y,e){var C,t,l,c,w=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,x=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,N=/\s*([^:]+):\s*([^;]+);?/g,E=/\s+$/,z={},S="\ufeff";for(y=y||{},e&&(l=e.getValidStyles(),c=e.getInvalidStyles()),t=("\\\" \\' \\; \\: ; : "+S).split(" "),C=0;C<t.length;C++)z[t[C]]=S+C,z[S+C]=t[C];return{toHex:function(e){return e.replace(w,ii)},parse:function(e){var t,n,r,o,i,a,u,s,l={},c=y.url_converter,f=y.url_converter_scope||this,d=function(e,t,n){var r,o,i,a;if((r=l[e+"-top"+t])&&(o=l[e+"-right"+t])&&(i=l[e+"-bottom"+t])&&(a=l[e+"-left"+t])){var u=[r,o,i,a];for(C=u.length-1;C--&&u[C]===u[C+1];);-1<C&&n||(l[e+t]=-1===C?u[0]:u.join(" "),delete l[e+"-top"+t],delete l[e+"-right"+t],delete l[e+"-bottom"+t],delete l[e+"-left"+t])}},h=function(e){var t,n=l[e];if(n){for(t=(n=n.split(" ")).length;t--;)if(n[t]!==n[0])return!1;return l[e]=n[0],!0}},m=function(e){return o=!0,z[e]},g=function(e,t){return o&&(e=e.replace(/\uFEFF[0-9]/g,function(e){return z[e]})),t||(e=e.replace(/\\([\'\";:])/g,"$1")),e},p=function(e){return String.fromCharCode(parseInt(e.slice(1),16))},v=function(e){return e.replace(/\\[0-9a-f]+/gi,p)},b=function(e,t,n,r,o,i){if(o=o||i)return"'"+(o=g(o)).replace(/\'/g,"\\'")+"'";if(t=g(t||n||r),!y.allow_script_urls){var a=t.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(a))return"";if(!y.allow_svg_data_urls&&/^data:image\/svg/i.test(a))return""}return c&&(t=c.call(f,t,"style")),"url('"+t.replace(/\'/g,"\\'")+"')"};if(e){for(e=(e=e.replace(/[\u0000-\u001F]/g,"")).replace(/\\[\"\';:\uFEFF]/g,m).replace(/\"[^\"]+\"|\'[^\']+\'/g,function(e){return e.replace(/[;:]/g,m)});t=N.exec(e);)if(N.lastIndex=t.index+t[0].length,n=t[1].replace(E,"").toLowerCase(),r=t[2].replace(E,""),n&&r){if(n=v(n),r=v(r),-1!==n.indexOf(S)||-1!==n.indexOf('"'))continue;if(!y.allow_script_urls&&("behavior"===n||/expression\s*\(|\/\*|\*\//.test(r)))continue;"font-weight"===n&&"700"===r?r="bold":"color"!==n&&"background-color"!==n||(r=r.toLowerCase()),r=(r=r.replace(w,ii)).replace(x,b),l[n]=o?g(r,!0):r}d("border","",!0),d("border","-width"),d("border","-color"),d("border","-style"),d("padding",""),d("margin",""),i="border",u="border-style",s="border-color",h(a="border-width")&&h(u)&&h(s)&&(l[i]=l[a]+" "+l[u]+" "+l[s],delete l[a],delete l[u],delete l[s]),"medium none"===l.border&&delete l.border,"none"===l["border-image"]&&delete l["border-image"]}return l},serialize:function(i,e){var t,n,r,o,a,u="",s=function(e){var t,n,r,o;if(t=l[e])for(n=0,r=t.length;n<r;n++)e=t[n],(o=i[e])&&(u+=(0<u.length?" ":"")+e+": "+o+";")};if(e&&l)s("*"),s(e);else for(t in i)!(n=i[t])||c&&(r=t,o=e,a=void 0,(a=c["*"])&&a[r]||(a=c[o])&&a[r])||(u+=(0<u.length?" ":"")+t+": "+n+";");return u}}}var ui,si=Gt.each,li=Gt.grep,ci=he.ie,fi=/^([a-z0-9],?)+$/i,di=/^[ \t\r\n]*$/,hi=function(n,r,o){var e={},i=r.keep_values,t={set:function(e,t,n){r.url_converter&&(t=r.url_converter.call(r.url_converter_scope||o(),t,n,e[0])),e.attr("data-mce-"+n,t).attr(n,t)},get:function(e,t){return e.attr("data-mce-"+t)||e.attr(t)}};return e={style:{set:function(e,t){null===t||"object"!=typeof t?(i&&e.attr("data-mce-style",t),e.attr("style",t)):e.css(t)},get:function(e){var t=e.attr("data-mce-style")||e.attr("style");return t=n.serialize(n.parse(t),e[0].nodeName)}}},i&&(e.href=e.src=t),e},mi=function(e,t){var n=t.attr("style"),r=e.serialize(e.parse(n),t[0].nodeName);r||(r=null),t.attr("data-mce-style",r)},gi=function(e,t){var n,r,o=0;if(e)for(n=e.nodeType,e=e.previousSibling;e;e=e.previousSibling)r=e.nodeType,(!t||3!==r||r!==n&&e.nodeValue.length)&&(o++,n=r);return o};function pi(a,u){var s,l=this;void 0===u&&(u={});var r={},i=j.window,o={},t=0,e=function U(m,g){void 0===g&&(g={});var p,v=0,b={};p=g.maxLoadTime||5e3;var y=function(e){m.getElementsByTagName("head")[0].appendChild(e)},n=function(e,t,n){var o,r,i,a,u=function(e){a.status=e,a.passed=[],a.failed=[],o&&(o.onload=null,o.onerror=null,o=null)},s=function(){for(var e=a.passed,t=e.length;t--;)e[t]();u(2)},l=function(){for(var e=a.failed,t=e.length;t--;)e[t]();u(3)},c=function(e,t){e()||((new Date).getTime()-i<p?be.setTimeout(t):l())},f=function(){c(function(){for(var e,t,n=m.styleSheets,r=n.length;r--;)if((t=(e=n[r]).ownerNode?e.ownerNode:e.owningElement)&&t.id===o.id)return s(),!0},f)},d=function(){c(function(){try{var e=r.sheet.cssRules;return s(),!!e}catch(t){}},d)};if(e=Gt._addCacheSuffix(e),b[e]?a=b[e]:(a={passed:[],failed:[]},b[e]=a),t&&a.passed.push(t),n&&a.failed.push(n),1!==a.status)if(2!==a.status)if(3!==a.status){if(a.status=1,(o=m.createElement("link")).rel="stylesheet",o.type="text/css",o.id="u"+v++,o.async=!1,o.defer=!1,i=(new Date).getTime(),g.contentCssCors&&(o.crossOrigin="anonymous"),"onload"in o&&!((h=j.navigator.userAgent.match(/WebKit\/(\d*)/))&&parseInt(h[1],10)<536))o.onload=f,o.onerror=l;else{if(0<j.navigator.userAgent.indexOf("Firefox"))return(r=m.createElement("style")).textContent='@import "'+e+'"',d(),void y(r);f()}var h;y(o),o.href=e}else l();else s()},t=function(t){return Zr.nu(function(e){n(t,q(e,$(oo.value(t))),q(e,$(oo.error(t))))})},o=function(e){return e.fold(W,W)};return{load:n,loadAll:function(e,n,r){to(K(e,t)).get(function(e){var t=X(e,function(e){return e.isValue()});0<t.fail.length?r(t.fail.map(o)):n(t.pass.map(o))})}}}(a,{contentCssCors:u.contentCssCors}),c=[],f=u.schema?u.schema:oi({}),d=ai({url_converter:u.url_converter,url_converter_scope:u.url_converter_scope},u.schema),h=u.ownEvents?new ke(u.proxy):ke.Event,n=f.getBlockElements(),m=pn.overrideDefaults(function(){return{context:a,element:F.getRoot()}}),g=function(e){if(e&&a&&"string"==typeof e){var t=a.getElementById(e);return t&&t.id!==e?a.getElementsByName(e)[1]:t}return e},p=function(e){return"string"==typeof e&&(e=g(e)),m(e)},v=function(e,t,n){var r,o,i=p(e);return i.length&&(o=(r=s[t])&&r.get?r.get(i,t):i.attr(t)),void 0===o&&(o=n||""),o},b=function(e){var t=g(e);return t?t.attributes:[]},y=function(e,t,n){var r,o;""===n&&(n=null);var i=p(e);r=i.attr(t),i.length&&((o=s[t])&&o.set?o.set(i,n,t):i.attr(t,n),r!==n&&u.onSetAttrib&&u.onSetAttrib({attrElm:i,attrName:t,attrValue:n}))},C=function(){return u.root_element||a.body},w=function(e,t){return Yr.getPos(a.body,g(e),t)},x=function(e,t,n){var r=p(e);return n?r.css(t):("float"===(t=t.replace(/-(\D)/g,function(e,t){return t.toUpperCase()}))&&(t=he.ie&&he.ie<12?"styleFloat":"cssFloat"),r[0]&&r[0].style?r[0].style[t]:undefined)},N=function(e){var t,n;return e=g(e),t=x(e,"width"),n=x(e,"height"),-1===t.indexOf("px")&&(t=0),-1===n.indexOf("px")&&(n=0),{w:parseInt(t,10)||e.offsetWidth||e.clientWidth,h:parseInt(n,10)||e.offsetHeight||e.clientHeight}},E=function(e,t){var n;if(!e)return!1;if(!Array.isArray(e)){if("*"===t)return 1===e.nodeType;if(fi.test(t)){var r=t.toLowerCase().split(/,/),o=e.nodeName.toLowerCase();for(n=r.length-1;0<=n;n--)if(r[n]===o)return!0;return!1}if(e.nodeType&&1!==e.nodeType)return!1}var i=Array.isArray(e)?e:[e];return 0<kt(t,i[0].ownerDocument||i[0],null,i).length},z=function(e,t,n,r){var o,i=[],a=g(e);for(r=r===undefined,n=n||("BODY"!==C().nodeName?C().parentNode:null),Gt.is(t,"string")&&(t="*"===(o=t)?function(e){return 1===e.nodeType}:function(e){return E(e,o)});a&&a!==n&&a.nodeType&&9!==a.nodeType;){if(!t||"function"==typeof t&&t(a)){if(!r)return[a];i.push(a)}a=a.parentNode}return r?i:null},S=function(e,t,n){var r=t;if(e)for("string"==typeof t&&(r=function(e){return E(e,t)}),e=e[n];e;e=e[n])if("function"==typeof r&&r(e))return e;return null},k=function(e,n,r){var o,t="string"==typeof e?g(e):e;if(!t)return!1;if(Gt.isArray(t)&&(t.length||0===t.length))return o=[],si(t,function(e,t){e&&("string"==typeof e&&(e=g(e)),o.push(n.call(r,e,t)))}),o;var i=r||l;return n.call(i,t)},T=function(e,t){p(e).each(function(e,n){si(t,function(e,t){y(n,t,e)})})},A=function(e,r){var t=p(e);ci?t.each(function(e,t){if(!1!==t.canHaveHTML){for(;t.firstChild;)t.removeChild(t.firstChild);try{t.innerHTML="<br>"+r,t.removeChild(t.firstChild)}catch(n){pn("<div></div>").html("<br>"+r).contents().slice(1).appendTo(t)}return r}}):t.html(r)},R=function(e,n,r,o,i){return k(e,function(e){var t="string"==typeof n?a.createElement(n):n;return T(t,r),o&&("string"!=typeof o&&o.nodeType?t.appendChild(o):"string"==typeof o&&A(t,o)),i?t:e.appendChild(t)})},D=function(e,t,n){return R(a.createElement(e),e,t,n,!0)},M=Ko.decode,B=Ko.encodeAllRaw,_=function(e,t){var n=p(e);return t?n.each(function(){for(var e;e=this.firstChild;)3===e.nodeType&&0===e.data.length?this.removeChild(e):this.parentNode.insertBefore(e,this)}).remove():n.remove(),1<n.length?n.toArray():n[0]},O=function(e,t,n){p(e).toggleClass(t,n).each(function(){""===this.className&&pn(this).attr("class",null)})},H=function(t,e,n){return k(e,function(e){return Gt.is(e,"array")&&(t=t.cloneNode(!0)),n&&si(li(e.childNodes),function(e){t.appendChild(e)}),e.parentNode.replaceChild(t,e)})},P=function(){return a.createRange()},L=function(e,t,n,r){if(Gt.isArray(e)){for(var o=e.length;o--;)e[o]=L(e[o],t,n,r);return e}return!u.collect||e!==a&&e!==i||c.push([e,t,n,r]),h.bind(e,t,n,r||F)},V=function(e,t,n){var r;if(Gt.isArray(e)){for(r=e.length;r--;)e[r]=V(e[r],t,n);return e}if(c&&(e===a||e===i))for(r=c.length;r--;){var o=c[r];e!==o[0]||t&&t!==o[1]||n&&n!==o[2]||h.unbind(o[0],o[1],o[2])}return h.unbind(e,t,n)},I=function(e){if(e&&Bo.isElement(e)){var t=e.getAttribute("data-mce-contenteditable");return t&&"inherit"!==t?t:"inherit"!==e.contentEditable?e.contentEditable:null}return null},F={doc:a,settings:u,win:i,files:o,stdMode:!0,boxModel:!0,styleSheetLoader:e,boundEvents:c,styles:d,schema:f,events:h,isBlock:function(e){if("string"==typeof e)return!!n[e];if(e){var t=e.nodeType;if(t)return!(1!==t||!n[e.nodeName])}return!1},$:m,$$:p,root:null,clone:function(t,e){if(!ci||1!==t.nodeType||e)return t.cloneNode(e);if(e)return null;var n=a.createElement(t.nodeName);return si(b(t),function(e){y(n,e.nodeName,v(t,e.nodeName))}),n},getRoot:C,getViewPort:function(e){var t=e||i,n=t.document,r=n.documentElement;return{x:t.pageXOffset||r.scrollLeft,y:t.pageYOffset||r.scrollTop,w:t.innerWidth||r.clientWidth,h:t.innerHeight||r.clientHeight}},getRect:function(e){var t,n;return e=g(e),t=w(e),n=N(e),{x:t.x,y:t.y,w:n.w,h:n.h}},getSize:N,getParent:function(e,t,n){var r=z(e,t,n,!1);return r&&0<r.length?r[0]:null},getParents:z,get:g,getNext:function(e,t){return S(e,t,"nextSibling")},getPrev:function(e,t){return S(e,t,"previousSibling")},select:function(e,t){return kt(e,g(t)||u.root_element||a,[])},is:E,add:R,create:D,createHTML:function(e,t,n){var r,o="";for(r in o+="<"+e,t)t.hasOwnProperty(r)&&null!==t[r]&&"undefined"!=typeof t[r]&&(o+=" "+r+'="'+B(t[r])+'"');return void 0!==n?o+">"+n+"</"+e+">":o+" />"},createFragment:function(e){var t,n=a.createElement("div"),r=a.createDocumentFragment();for(e&&(n.innerHTML=e);t=n.firstChild;)r.appendChild(t);return r},remove:_,setStyle:function(e,t,n){var r=p(e).css(t,n);u.update_styles&&mi(d,r)},getStyle:x,setStyles:function(e,t){var n=p(e).css(t);u.update_styles&&mi(d,n)},removeAllAttribs:function(e){return k(e,function(e){var t,n=e.attributes;for(t=n.length-1;0<=t;t--)e.removeAttributeNode(n.item(t))})},setAttrib:y,setAttribs:T,getAttrib:v,getPos:w,parseStyle:function(e){return d.parse(e)},serializeStyle:function(e,t){return d.serialize(e,t)},addStyle:function(e){var t,n;if(F!==pi.DOM&&a===j.document){if(r[e])return;r[e]=!0}(n=a.getElementById("mceDefaultStyles"))||((n=a.createElement("style")).id="mceDefaultStyles",n.type="text/css",(t=a.getElementsByTagName("head")[0]).firstChild?t.insertBefore(n,t.firstChild):t.appendChild(n)),n.styleSheet?n.styleSheet.cssText+=e:n.appendChild(a.createTextNode(e))},loadCSS:function(e){var n;F===pi.DOM||a!==j.document?(e||(e=""),n=a.getElementsByTagName("head")[0],si(e.split(","),function(e){var t;e=Gt._addCacheSuffix(e),o[e]||(o[e]=!0,t=D("link",{rel:"stylesheet",href:e}),n.appendChild(t))})):pi.DOM.loadCSS(e)},addClass:function(e,t){p(e).addClass(t)},removeClass:function(e,t){O(e,t,!1)},hasClass:function(e,t){return p(e).hasClass(t)},toggleClass:O,show:function(e){p(e).show()},hide:function(e){p(e).hide()},isHidden:function(e){return"none"===p(e).css("display")},uniqueId:function(e){return(e||"mce_")+t++},setHTML:A,getOuterHTML:function(e){var t="string"==typeof e?g(e):e;return Bo.isElement(t)?t.outerHTML:pn("<div></div>").append(pn(t).clone()).html()},setOuterHTML:function(e,t){p(e).each(function(){try{if("outerHTML"in this)return void(this.outerHTML=t)}catch(e){}_(pn(this).html(t),!0)})},decode:M,encode:B,insertAfter:function(e,t){var r=g(t);return k(e,function(e){var t,n;return t=r.parentNode,(n=r.nextSibling)?t.insertBefore(e,n):t.appendChild(e),e})},replace:H,rename:function(t,e){var n;return t.nodeName!==e.toUpperCase()&&(n=D(e),si(b(t),function(e){y(n,e.nodeName,v(t,e.nodeName))}),H(n,t,!0)),n||t},findCommonAncestor:function(e,t){for(var n,r=e;r;){for(n=t;n&&r!==n;)n=n.parentNode;if(r===n)break;r=r.parentNode}return!r&&e.ownerDocument?e.ownerDocument.documentElement:r},toHex:function(e){return d.toHex(Gt.trim(e))},run:k,getAttribs:b,isEmpty:function(e,t){var n,r,o,i,a,u,s=0;if(e=e.firstChild){a=new io(e,e.parentNode),t=t||(f?f.getNonEmptyElements():null),i=f?f.getWhiteSpaceElements():{};do{if(o=e.nodeType,Bo.isElement(e)){var l=e.getAttribute("data-mce-bogus");if(l){e=a.next("all"===l);continue}if(u=e.nodeName.toLowerCase(),t&&t[u]){if("br"!==u)return!1;s++,e=a.next();continue}for(n=(r=b(e)).length;n--;)if("name"===(u=r[n].nodeName)||"data-mce-bookmark"===u)return!1}if(8===o)return!1;if(3===o&&!di.test(e.nodeValue))return!1;if(3===o&&e.parentNode&&i[e.parentNode.nodeName]&&di.test(e.nodeValue))return!1;e=a.next()}while(e)}return s<=1},createRng:P,nodeIndex:gi,split:function(e,t,n){var r,o,i,a=P();if(e&&t)return a.setStart(e.parentNode,gi(e)),a.setEnd(t.parentNode,gi(t)),r=a.extractContents(),(a=P()).setStart(t.parentNode,gi(t)+1),a.setEnd(e.parentNode,gi(e)+1),o=a.extractContents(),(i=e.parentNode).insertBefore(Ho.trimNode(F,r),e),n?i.insertBefore(n,e):i.insertBefore(t,e),i.insertBefore(Ho.trimNode(F,o),e),_(e),n||t},bind:L,unbind:V,fire:function(e,t,n){return h.fire(e,t,n)},getContentEditable:I,getContentEditableParent:function(e){for(var t=C(),n=null;e&&e!==t&&null===(n=I(e));e=e.parentNode);return n},destroy:function(){if(c)for(var e=c.length;e--;){var t=c[e];h.unbind(t[0],t[1],t[2])}kt.setDocument&&kt.setDocument()},isChildOf:function(e,t){for(;e;){if(t===e)return!0;e=e.parentNode}return!1},dumpRng:function(e){return"startContainer: "+e.startContainer.nodeName+", startOffset: "+e.startOffset+", endContainer: "+e.endContainer.nodeName+", endOffset: "+e.endOffset}};return s=hi(d,u,function(){return F}),F}(ui=pi||(pi={})).DOM=ui(j.document),ui.nodeIndex=gi;var vi=pi,bi=vi.DOM,yi=Gt.each,Ci=Gt.grep,wi=function(e){return"function"==typeof e},xi=function(){var u={},o=[],s={},l=[],c=0,f=function(e,t,n){var r,o,i=bi;o=i.uniqueId(),(r=j.document.createElement("script")).id=o,r.type="text/javascript",r.src=Gt._addCacheSuffix(e),r.onload=function(){i.remove(o),r&&(r.onreadystatechange=r.onload=r=null),t()},r.onerror=function(){wi(n)?n():"undefined"!=typeof console&&console.log&&console.log("Failed to load script: "+e)},(j.document.getElementsByTagName("head")[0]||j.document.body).appendChild(r)};this.loadScript=function(e,t,n){f(e,t,n)},this.isDone=function(e){return 2===u[e]},this.markDone=function(e){u[e]=2},this.add=this.load=function(e,t,n,r){u[e]===undefined&&(o.push(e),u[e]=0),t&&(s[e]||(s[e]=[]),s[e].push({success:t,failure:r,scope:n||this}))},this.remove=function(e){delete u[e],delete s[e]},this.loadQueue=function(e,t,n){this.loadScripts(o,e,t,n)},this.loadScripts=function(n,e,t,r){var o,i=[],a=function(t,e){yi(s[e],function(e){wi(e[t])&&e[t].call(e.scope)}),s[e]=undefined};l.push({success:e,failure:r,scope:t||this}),(o=function(){var e=Ci(n);if(n.length=0,yi(e,function(e){2!==u[e]?3!==u[e]?1!==u[e]&&(u[e]=1,c++,f(e,function(){u[e]=2,c--,a("success",e),o()},function(){u[e]=3,c--,i.push(e),a("failure",e),o()})):a("failure",e):a("success",e)}),!c){var t=l.slice(0);l.length=0,yi(t,function(e){0===i.length?wi(e.success)&&e.success.call(e.scope):wi(e.failure)&&e.failure.call(e.scope,i)})}})()}};xi.ScriptLoader=new xi;var Ni,Ei=function(e){var t=e,n=function(){return t};return{get:n,set:function(e){t=e},clone:function(){return Ei(n())}}},zi={},Si=Ei("en"),ki={setCode:function(e){e&&Si.set(e)},getCode:function(){return Si.get()},add:function(e,t){var n=zi[e];for(var r in n||(zi[e]=n={}),t)n[r.toLowerCase()]=t[r]},translate:function(e){var t,n,r=zi[Si.get()]||{},o=function(e){return _(e)?Object.prototype.toString.call(e):i(e)?"":""+e},i=function(e){return""===e||null===e||e===undefined},a=function(e){var t=o(e),n=t.toLowerCase();return vr(r,n)?o(r[n]):t},u=function(e){return e.replace(/{context:\w+}$/,"")},s=function(e){return e};if(i(e))return s("");if(R(t=e)&&vr(t,"raw"))return s(o(e.raw));if(D(n=e)&&1<n.length){var l=e.slice(1);return s(u(a(e[0]).replace(/\{([0-9]+)\}/g,function(e,t){return vr(l,t)?o(l[t]):e})))}return s(u(a(e)))},isRtl:function(){return pr(zi,Si.get()).bind(function(e){return pr(e,"_dir")}).exists(function(e){return"rtl"===e})},hasCode:function(e){return vr(zi,e)}},Ti=Gt.each;function Ai(){var r=this,o=[],a={},u={},i=[],s=function(e){var t;return u[e]&&(t=u[e].dependencies),t||[]},l=function(e,t){return"object"==typeof t?t:"string"==typeof e?{prefix:"",resource:t,suffix:""}:{prefix:e.prefix,resource:t,suffix:e.suffix}},c=function(e,n,t,r){var o=s(e);Ti(o,function(e){var t=l(n,e);f(t.resource,t,undefined,undefined)}),t&&(r?t.call(r):t.call(xi))},f=function(e,t,n,r,o){if(!a[e]){var i="string"==typeof t?t:t.prefix+t.resource+t.suffix;0!==i.indexOf("/")&&-1===i.indexOf("://")&&(i=Ai.baseURL+"/"+i),a[e]=i.substring(0,i.lastIndexOf("/")),u[e]?c(e,t,n,r):xi.ScriptLoader.add(i,function(){return c(e,t,n,r)},r,o)}};return{items:o,urls:a,lookup:u,_listeners:i,get:function(e){return u[e]?u[e].instance:undefined},dependencies:s,requireLangPack:function(e,t){var n=ki.getCode();if(n&&!1!==Ai.languageLoad){if(t)if(-1!==(t=","+t+",").indexOf(","+n.substr(0,2)+","))n=n.substr(0,2);else if(-1===t.indexOf(","+n+","))return;xi.ScriptLoader.add(a[e]+"/langs/"+n+".js")}},add:function(t,e,n){o.push(e),u[t]={instance:e,dependencies:n};var r=X(i,function(e){return e.name===t});return i=r.fail,Ti(r.pass,function(e){e.callback()}),e},remove:function(e){delete a[e],delete u[e]},createUrl:l,addComponents:function(e,t){var n=r.urls[e];Ti(t,function(e){xi.ScriptLoader.add(n+"/"+e)})},load:f,waitFor:function(e,t){u.hasOwnProperty(e)?t():i.push({name:e,callback:t})}}}(Ni=Ai||(Ai={})).PluginManager=Ni(),Ni.ThemeManager=Ni();var Ri=function(t,n){Lr(t).each(function(e){e.dom().insertBefore(n.dom(),t.dom())})},Di=function(e,t){Ir(e).fold(function(){Lr(e).each(function(e){Bi(e,t)})},function(e){Ri(e,t)})},Mi=function(t,n){$r(t).fold(function(){Bi(t,n)},function(e){t.dom().insertBefore(n.dom(),e.dom())})},Bi=function(e,t){e.dom().appendChild(t.dom())},_i=function(t,e){L(e,function(e){Bi(t,e)})},Oi=function(e){e.dom().textContent="",L(jr(e),function(e){Hi(e)})},Hi=function(e){var t=e.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},Pi=function(e){var t,n=jr(e);0<n.length&&(t=e,L(n,function(e){Ri(t,e)})),Hi(e)},Li=function(n,r){var o=null;return{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];null===o&&(o=setTimeout(function(){n.apply(null,e),o=null},r))}}},Vi=function(e,t){var n=wr(e,t);return n===undefined||""===n?[]:n.split(" ")},Ii=function(e){return e.dom().classList!==undefined},Fi=function(e,t){return o=t,i=Vi(n=e,r="class").concat([o]),yr(n,r,i.join(" ")),!0;var n,r,o,i},Ui=function(e,t){return o=t,0<(i=V(Vi(n=e,r="class"),function(e){return e!==o})).length?yr(n,r,i.join(" ")):xr(n,r),!1;var n,r,o,i},ji=function(e,t){Ii(e)?e.dom().classList.add(t):Fi(e,t)},qi=function(e){0===(Ii(e)?e.dom().classList:Vi(e,"class")).length&&xr(e,"class")},$i=function(e,t){return Ii(e)&&e.dom().classList.contains(t)},Wi=function(e,t){var n=[];return L(jr(e),function(e){t(e)&&(n=n.concat([e])),n=n.concat(Wi(e,t))}),n},Ki=function(e,t){return n=t,o=(r=e)===undefined?j.document:r.dom(),_r(o)?[]:K(o.querySelectorAll(n),or.fromDom);var n,r,o};function Xi(e,t,n,r,o){return e(n,r)?T.some(n):_(o)&&o(n)?T.none():t(n,r,o)}var Yi,Gi=function(e,t,n){for(var r=e.dom(),o=_(n)?n:$(!1);r.parentNode;){r=r.parentNode;var i=or.fromDom(r);if(t(i))return T.some(i);if(o(i))break}return T.none()},Ji=function(e,t,n){return Xi(function(e){return t(e)},Gi,e,t,n)},Qi=function(e,t,n){return Gi(e,function(e){return Br(e,t)},n)},Zi=function(e,t){return n=t,o=(r=e)===undefined?j.document:r.dom(),_r(o)?T.none():T.from(o.querySelector(n)).map(or.fromDom);var n,r,o},ea=function(e,t,n){return Xi(Br,Qi,e,t,n)},ta=$("mce-annotation"),na=$("data-mce-annotation"),ra=$("data-mce-annotation-uid"),oa=function(r,e){var t=r.selection.getRng(),n=or.fromDom(t.startContainer),o=or.fromDom(r.getBody()),i=e.fold(function(){return"."+ta()},function(e){return"["+na()+'="'+e+'"]'}),a=qr(n,t.startOffset).getOr(n),u=ea(a,i,function(e){return Or(e,o)}),s=function(e,t){return n=t,(r=e.dom())&&r.hasAttribute&&r.hasAttribute(n)?T.some(wr(e,t)):T.none();var n,r};return u.bind(function(e){return s(e,""+ra()).bind(function(n){return s(e,""+na()).map(function(e){var t=ia(r,n);return{uid:n,name:e,elements:t}})})})},ia=function(e,t){var n=or.fromDom(e.getBody());return Ki(n,"["+ra()+'="'+t+'"]')},aa=function(i,e){var n,r,o,a=Ei({}),l=function(e,t){u(e,function(e){return t(e),e})},u=function(e,t){var n=a.get(),r=t(n.hasOwnProperty(e)?n[e]:{listeners:[],previous:Ei(T.none())});n[e]=r,a.set(n)},t=(n=function(){var e,t,n,r=a.get(),o=(e=dr(r),(n=Z.call(e,0)).sort(t),n);L(o,function(e){u(e,function(u){var s=u.previous.get();return oa(i,T.some(e)).fold(function(){var t;s.isSome()&&(l(t=e,function(e){L(e.listeners,function(e){return e(!1,t)})}),u.previous.set(T.none()))},function(e){var t,n,r,o=e.uid,i=e.name,a=e.elements;s.is(o)||(n=o,r=a,l(t=i,function(e){L(e.listeners,function(e){return e(!0,t,{uid:n,nodes:K(r,function(e){return e.dom()})})})}),u.previous.set(T.some(o)))}),{previous:u.previous,listeners:u.listeners}})})},r=30,o=null,{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];null!==o&&clearTimeout(o),o=setTimeout(function(){n.apply(null,e),o=null},r)}});return i.on("remove",function(){t.cancel()}),i.on("nodeChange",function(){t.throttle()}),{addListener:function(e,t){u(e,function(e){return{previous:e.previous,listeners:e.listeners.concat([t])}})}}},ua=function(e,n){e.on("init",function(){e.serializer.addNodeFilter("span",function(e){L(e,function(t){var e;(e=t,T.from(e.attributes.map[na()]).bind(n.lookup)).each(function(e){!1===e.persistent&&t.unwrap()})})})})},sa=function(){return(sa=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},la=0,ca=function(e,t){return or.fromDom(e.dom().cloneNode(t))},fa=function(e){return ca(e,!1)},da=function(e){return ca(e,!0)},ha=function(e,t){var n,r,o=Pr(e).dom(),i=or.fromDom(o.createDocumentFragment()),a=(n=t,(r=(o||j.document).createElement("div")).innerHTML=n,jr(or.fromDom(r)));_i(i,a),Oi(e),Bi(e,i)},ma="\ufeff",ga=function(e){return e===ma},pa=ma,va=function(e){return e.replace(new RegExp(ma,"g"),"")},ba=Bo.isElement,ya=Bo.isText,Ca=function(e){return ya(e)&&(e=e.parentNode),ba(e)&&e.hasAttribute("data-mce-caret")},wa=function(e){return ya(e)&&ga(e.data)},xa=function(e){return Ca(e)||wa(e)},Na=function(e){return e.firstChild!==e.lastChild||!Bo.isBr(e.firstChild)},Ea=function(e){var t=e.container();return!(!e||!Bo.isText(t))&&(t.data.charAt(e.offset())===pa||e.isAtStart()&&wa(t.previousSibling))},za=function(e){var t=e.container();return!(!e||!Bo.isText(t))&&(t.data.charAt(e.offset()-1)===pa||e.isAtEnd()&&wa(t.nextSibling))},Sa=function(e,t,n){var r,o,i;return(r=t.ownerDocument.createElement(e)).setAttribute("data-mce-caret",n?"before":"after"),r.setAttribute("data-mce-bogus","all"),r.appendChild(((i=j.document.createElement("br")).setAttribute("data-mce-bogus","1"),i)),o=t.parentNode,n?o.insertBefore(r,t):t.nextSibling?o.insertBefore(r,t.nextSibling):o.appendChild(r),r},ka=function(e){return ya(e)&&e.data[0]===pa},Ta=function(e){return ya(e)&&e.data[e.data.length-1]===pa},Aa=function(e){return e&&e.hasAttribute("data-mce-caret")?(t=e.getElementsByTagName("br"),n=t[t.length-1],Bo.isBogus(n)&&n.parentNode.removeChild(n),e.removeAttribute("data-mce-caret"),e.removeAttribute("data-mce-bogus"),e.removeAttribute("style"),e.removeAttribute("_moz_abspos"),e):null;var t,n},Ra=Bo.isContentEditableTrue,Da=Bo.isContentEditableFalse,Ma=Bo.isBr,Ba=Bo.isText,_a=Bo.matchNodeNames("script style textarea"),Oa=Bo.matchNodeNames("img input textarea hr iframe video audio object"),Ha=Bo.matchNodeNames("table"),Pa=xa,La=function(e){return!Pa(e)&&(Ba(e)?!_a(e.parentNode):Oa(e)||Ma(e)||Ha(e)||Va(e))},Va=function(e){return!1===(t=e,Bo.isElement(t)&&"true"===t.getAttribute("unselectable"))&&Da(e);var t},Ia=function(e,t){return La(e)&&function(e,t){for(e=e.parentNode;e&&e!==t;e=e.parentNode){if(Va(e))return!1;if(Ra(e))return!0}return!0}(e,t)},Fa=Math.round,Ua=function(e){return e?{left:Fa(e.left),top:Fa(e.top),bottom:Fa(e.bottom),right:Fa(e.right),width:Fa(e.width),height:Fa(e.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0}},ja=function(e,t){return(e=Ua(e)).right=(t||(e.left=e.left+e.width),e.left),e.width=0,e},qa=function(e,t,n){return 0<=e&&e<=Math.min(t.height,n.height)/2},$a=function(e,t){return e.bottom-e.height/2<t.top||!(e.top>t.bottom)&&qa(t.top-e.bottom,e,t)},Wa=function(e,t){return e.top>t.bottom||!(e.bottom<t.top)&&qa(t.bottom-e.top,e,t)},Ka=function(e){var t=e.startContainer,n=e.startOffset;return t.hasChildNodes()&&e.endOffset===n+1?t.childNodes[n]:null},Xa=function(e,t){return 1===e.nodeType&&e.hasChildNodes()&&(t>=e.childNodes.length&&(t=e.childNodes.length-1),e=e.childNodes[t]),e},Ya=new RegExp("[\u0300-\u036f\u0483-\u0487\u0488-\u0489\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7-\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u08e3-\u0902\u093a\u093c\u0941-\u0948\u094d\u0951-\u0957\u0962-\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2-\u09e3\u0a01-\u0a02\u0a3c\u0a41-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a70-\u0a71\u0a75\u0a81-\u0a82\u0abc\u0ac1-\u0ac5\u0ac7-\u0ac8\u0acd\u0ae2-\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62-\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c00\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c62-\u0c63\u0c81\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc-\u0ccd\u0cd5-\u0cd6\u0ce2-\u0ce3\u0d01\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62-\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb-\u0ebc\u0ec8-\u0ecd\u0f18-\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86-\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039-\u103a\u103d-\u103e\u1058-\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085-\u1086\u108d\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752-\u1753\u1772-\u1773\u17b4-\u17b5\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927-\u1928\u1932\u1939-\u193b\u1a17-\u1a18\u1a1b\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1ab0-\u1abd\u1abe\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80-\u1b81\u1ba2-\u1ba5\u1ba8-\u1ba9\u1bab-\u1bad\u1be6\u1be8-\u1be9\u1bed\u1bef-\u1bf1\u1c2c-\u1c33\u1c36-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1cf4\u1cf8-\u1cf9\u1dc0-\u1df5\u1dfc-\u1dff\u200c-\u200d\u20d0-\u20dc\u20dd-\u20e0\u20e1\u20e2-\u20e4\u20e5-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302d\u302e-\u302f\u3099-\u309a\ua66f\ua670-\ua672\ua674-\ua67d\ua69e-\ua69f\ua6f0-\ua6f1\ua802\ua806\ua80b\ua825-\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\ua9e5\uaa29-\uaa2e\uaa31-\uaa32\uaa35-\uaa36\uaa43\uaa4c\uaa7c\uaab0\uaab2-\uaab4\uaab7-\uaab8\uaabe-\uaabf\uaac1\uaaec-\uaaed\uaaf6\uabe5\uabe8\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe2f\uff9e-\uff9f]"),Ga=function(e){return"string"==typeof e&&768<=e.charCodeAt(0)&&Ya.test(e)},Ja=function(e,t){for(var n=[],r=0;r<e.length;r++){var o=e[r];if(!o.isSome())return T.none();n.push(o.getOrDie())}return T.some(t.apply(null,n))},Qa=[].slice,Za=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Qa.call(arguments);return function(e){for(var t=0;t<n.length;t++)if(!n[t](e))return!1;return!0}},eu=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Qa.call(arguments);return function(e){for(var t=0;t<n.length;t++)if(n[t](e))return!0;return!1}},tu=Bo.isElement,nu=La,ru=Bo.matchStyleValues("display","block table"),ou=Bo.matchStyleValues("float","left right"),iu=Za(tu,nu,y(ou)),au=y(Bo.matchStyleValues("white-space","pre pre-line pre-wrap")),uu=Bo.isText,su=Bo.isBr,lu=vi.nodeIndex,cu=Xa,fu=function(e){return"createRange"in e?e.createRange():vi.DOM.createRng()},du=function(e){return e&&/[\r\n\t ]/.test(e)},hu=function(e){return!!e.setStart&&!!e.setEnd},mu=function(e){var t,n=e.startContainer,r=e.startOffset;return!!(du(e.toString())&&au(n.parentNode)&&Bo.isText(n)&&(t=n.data,du(t[r-1])||du(t[r+1])))},gu=function(e){return 0===e.left&&0===e.right&&0===e.top&&0===e.bottom},pu=function(e){var t,n,r,o,i,a,u,s;return t=0<(n=e.getClientRects()).length?Ua(n[0]):Ua(e.getBoundingClientRect()),!hu(e)&&su(e)&&gu(t)?(i=(r=e).ownerDocument,a=fu(i),u=i.createTextNode("\xa0"),(s=r.parentNode).insertBefore(u,r),a.setStart(u,0),a.setEnd(u,1),o=Ua(a.getBoundingClientRect()),s.removeChild(u),o):gu(t)&&hu(e)?function(e){var t=e.startContainer,n=e.endContainer,r=e.startOffset,o=e.endOffset;if(t===n&&Bo.isText(n)&&0===r&&1===o){var i=e.cloneRange();return i.setEndAfter(n),pu(i)}return null}(e):t},vu=function(e,t){var n=ja(e,t);return n.width=1,n.right=n.left+1,n},bu=function(e){var t,n,r=[],o=function(e){var t,n;0!==e.height&&(0<r.length&&(t=e,n=r[r.length-1],t.left===n.left&&t.top===n.top&&t.bottom===n.bottom&&t.right===n.right)||r.push(e))},i=function(e,t){var n=fu(e.ownerDocument);if(t<e.data.length){if(Ga(e.data[t]))return r;if(Ga(e.data[t-1])&&(n.setStart(e,t),n.setEnd(e,t+1),!mu(n)))return o(vu(pu(n),!1)),r}0<t&&(n.setStart(e,t-1),n.setEnd(e,t),mu(n)||o(vu(pu(n),!1))),t<e.data.length&&(n.setStart(e,t),n.setEnd(e,t+1),mu(n)||o(vu(pu(n),!0)))};if(uu(e.container()))return i(e.container(),e.offset()),r;if(tu(e.container()))if(e.isAtEnd())n=cu(e.container(),e.offset()),uu(n)&&i(n,n.data.length),iu(n)&&!su(n)&&o(vu(pu(n),!1));else{if(n=cu(e.container(),e.offset()),uu(n)&&i(n,0),iu(n)&&e.isAtEnd())return o(vu(pu(n),!1)),r;t=cu(e.container(),e.offset()-1),iu(t)&&!su(t)&&(ru(t)||ru(n)||!iu(n))&&o(vu(pu(t),!1)),iu(n)&&o(vu(pu(n),!0))}return r};function yu(t,n,e){var r=function(){return e||(e=bu(yu(t,n))),e};return{container:$(t),offset:$(n),toRange:function(){var e;return(e=fu(t.ownerDocument)).setStart(t,n),e.setEnd(t,n),e},getClientRects:r,isVisible:function(){return 0<r().length},isAtStart:function(){return uu(t),0===n},isAtEnd:function(){return uu(t)?n>=t.data.length:n>=t.childNodes.length},isEqual:function(e){return e&&t===e.container()&&n===e.offset()},getNode:function(e){return cu(t,e?n-1:n)}}}(Yi=yu||(yu={})).fromRangeStart=function(e){return Yi(e.startContainer,e.startOffset)},Yi.fromRangeEnd=function(e){return Yi(e.endContainer,e.endOffset)},Yi.after=function(e){return Yi(e.parentNode,lu(e)+1)},Yi.before=function(e){return Yi(e.parentNode,lu(e))},Yi.isAbove=function(e,t){return Ja([te(t.getClientRects()),ne(e.getClientRects())],$a).getOr(!1)},Yi.isBelow=function(e,t){return Ja([ne(t.getClientRects()),te(e.getClientRects())],Wa).getOr(!1)},Yi.isAtStart=function(e){return!!e&&e.isAtStart()},Yi.isAtEnd=function(e){return!!e&&e.isAtEnd()},Yi.isTextPosition=function(e){return!!e&&Bo.isText(e.container())},Yi.isElementPosition=function(e){return!1===Yi.isTextPosition(e)};var Cu,wu,xu=yu,Nu=Bo.isText,Eu=Bo.isBogus,zu=vi.nodeIndex,Su=function(e){var t=e.parentNode;return Eu(t)?Su(t):t},ku=function(e){return e?$t.reduce(e.childNodes,function(e,t){return Eu(t)&&"BR"!==t.nodeName?e=e.concat(ku(t)):e.push(t),e},[]):[]},Tu=function(t){return function(e){return t===e}},Au=function(e){var t,r,n,o;return(Nu(e)?"text()":e.nodeName.toLowerCase())+"["+(r=ku(Su(t=e)),n=$t.findIndex(r,Tu(t),t),r=r.slice(0,n+1),o=$t.reduce(r,function(e,t,n){return Nu(t)&&Nu(r[n-1])&&e++,e},0),r=$t.filter(r,Bo.matchNodeNames(t.nodeName)),(n=$t.findIndex(r,Tu(t),t))-o)+"]"},Ru=function(e,t){var n,r,o,i,a,u=[];return n=t.container(),r=t.offset(),Nu(n)?o=function(e,t){for(;(e=e.previousSibling)&&Nu(e);)t+=e.data.length;return t}(n,r):(r>=(i=n.childNodes).length?(o="after",r=i.length-1):o="before",n=i[r]),u.push(Au(n)),a=function(e,t,n){var r=[];for(t=t.parentNode;!(t===e||n&&n(t));t=t.parentNode)r.push(t);return r}(e,n),a=$t.filter(a,y(Bo.isBogus)),(u=u.concat($t.map(a,function(e){return Au(e)}))).reverse().join("/")+","+o},Du=function(e,t){var n,r,o;return t?(t=(n=t.split(","))[0].split("/"),o=1<n.length?n[1]:"before",(r=$t.reduce(t,function(e,t){return(t=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(t))?("text()"===t[1]&&(t[1]="#text"),n=e,r=t[1],o=parseInt(t[2],10),i=ku(n),i=$t.filter(i,function(e,t){return!Nu(e)||!Nu(i[t-1])}),(i=$t.filter(i,Bo.matchNodeNames(r)))[o]):null;var n,r,o,i},e))?Nu(r)?function(e,t){for(var n,r=e,o=0;Nu(r);){if(n=r.data.length,o<=t&&t<=o+n){e=r,t-=o;break}if(!Nu(r.nextSibling)){e=r,t=n;break}o+=n,r=r.nextSibling}return Nu(e)&&t>e.data.length&&(t=e.data.length),xu(e,t)}(r,parseInt(o,10)):(o="after"===o?zu(r)+1:zu(r),xu(r.parentNode,o)):null):null},Mu=function(e,t){Bo.isText(t)&&0===t.data.length&&e.remove(t)},Bu=function(e,t,n){var r,o,i,a,u,s,l;Bo.isDocumentFragment(n)?(i=e,a=t,u=n,s=T.from(u.firstChild),l=T.from(u.lastChild),a.insertNode(u),s.each(function(e){return Mu(i,e.previousSibling)}),l.each(function(e){return Mu(i,e.nextSibling)})):(r=e,o=n,t.insertNode(o),Mu(r,o.previousSibling),Mu(r,o.nextSibling))},_u=Bo.isContentEditableFalse,Ou=function(e,t,n,r,o){var i,a=r[o?"startContainer":"endContainer"],u=r[o?"startOffset":"endOffset"],s=[],l=0,c=e.getRoot();for(Bo.isText(a)?s.push(n?function(e,t,n){var r,o;for(o=e(t.data.slice(0,n)).length,r=t.previousSibling;r&&Bo.isText(r);r=r.previousSibling)o+=e(r.data).length;return o}(t,a,u):u):(u>=(i=a.childNodes).length&&i.length&&(l=1,u=Math.max(0,i.length-1)),s.push(e.nodeIndex(i[u],n)+l));a&&a!==c;a=a.parentNode)s.push(e.nodeIndex(a,n));return s},Hu=function(e,t,n){var r=0;return Gt.each(e.select(t),function(e){if("all"!==e.getAttribute("data-mce-bogus"))return e!==n&&void r++}),r},Pu=function(e,t){var n,r,o,i=t?"start":"end";n=e[i+"Container"],r=e[i+"Offset"],Bo.isElement(n)&&"TR"===n.nodeName&&(n=(o=n.childNodes)[Math.min(t?r:r-1,o.length-1)])&&(r=t?0:n.childNodes.length,e["set"+(t?"Start":"End")](n,r))},Lu=function(e){return Pu(e,!0),Pu(e,!1),e},Vu=function(e,t){var n;if(Bo.isElement(e)&&(e=Xa(e,t),_u(e)))return e;if(xa(e)){if(Bo.isText(e)&&Ca(e)&&(e=e.parentNode),n=e.previousSibling,_u(n))return n;if(n=e.nextSibling,_u(n))return n}},Iu=function(e,t,n){var r=n.getNode(),o=r?r.nodeName:null,i=n.getRng();if(_u(r)||"IMG"===o)return{name:o,index:Hu(n.dom,o,r)};var a,u,s,l,c,f,d,h=Vu((a=i).startContainer,a.startOffset)||Vu(a.endContainer,a.endOffset);return h?{name:o=h.tagName,index:Hu(n.dom,o,h)}:(u=e,l=t,c=i,f=(s=n).dom,(d={}).start=Ou(f,u,l,c,!0),s.isCollapsed()||(d.end=Ou(f,u,l,c,!1)),d)},Fu=function(e,t,n){var r={"data-mce-type":"bookmark",id:t,style:"overflow:hidden;line-height:0px"};return n?e.create("span",r,"&#xFEFF;"):e.create("span",r)},Uu=function(e,t){var n=e.dom,r=e.getRng(),o=n.uniqueId(),i=e.isCollapsed(),a=e.getNode(),u=a.nodeName;if("IMG"===u)return{name:u,index:Hu(n,u,a)};var s=Lu(r.cloneRange());if(!i){s.collapse(!1);var l=Fu(n,o+"_end",t);Bu(n,s,l)}(r=Lu(r)).collapse(!0);var c=Fu(n,o+"_start",t);return Bu(n,r,c),e.moveToBookmark({id:o,keep:1}),{id:o}},ju={getBookmark:function(e,t,n){return 2===t?Iu(va,n,e):3===t?(o=(r=e).getRng(),{start:Ru(r.dom.getRoot(),xu.fromRangeStart(o)),end:Ru(r.dom.getRoot(),xu.fromRangeEnd(o))}):t?{rng:e.getRng()}:Uu(e,!1);var r,o},getUndoBookmark:d(Iu,W,!0),getPersistentBookmark:Uu},qu="_mce_caret",$u=function(e){return Bo.isElement(e)&&e.id===qu},Wu=function(e,t){for(;t&&t!==e;){if(t.id===qu)return t;t=t.parentNode}return null},Ku=Bo.isElement,Xu=Bo.isText,Yu=function(e){var t=e.parentNode;t&&t.removeChild(e)},Gu=function(e,t){0===t.length?Yu(e):e.nodeValue=t},Ju=function(e){var t=va(e);return{count:e.length-t.length,text:t}},Qu=function(e,t){return ts(e),t},Zu=function(e,t){var n,r,o,i=t.container(),a=(n=re(i.childNodes),r=e,o=H(n,r),-1===o?T.none():T.some(o)).map(function(e){return e<t.offset()?xu(i,t.offset()-1):t}).getOr(t);return ts(e),a},es=function(e,t){return Xu(e)&&t.container()===e?(r=t,o=Ju((n=e).data.substr(0,r.offset())),i=Ju(n.data.substr(r.offset())),0<(a=o.text+i.text).length?(Gu(n,a),xu(n,r.offset()-o.count)):r):Qu(e,t);var n,r,o,i,a},ts=function(e){if(Ku(e)&&xa(e)&&(Na(e)?e.removeAttribute("data-mce-caret"):Yu(e)),Xu(e)){var t=va(function(e){try{return e.nodeValue}catch(t){return""}}(e));Gu(e,t)}},ns={removeAndReposition:function(e,t){return xu.isTextPosition(t)?es(e,t):(n=e,(r=t).container()===n.parentNode?Zu(n,r):Qu(n,r));var n,r},remove:ts},rs=nr.detect().browser,os=Bo.isContentEditableFalse,is=function(e,t,n){var r,o,i,a,u,s=ja(t.getBoundingClientRect(),n);return i="BODY"===e.tagName?(r=e.ownerDocument.documentElement,o=e.scrollLeft||r.scrollLeft,e.scrollTop||r.scrollTop):(u=e.getBoundingClientRect(),o=e.scrollLeft-u.left,e.scrollTop-u.top),s.left+=o,s.right+=o,s.top+=i,s.bottom+=i,s.width=1,0<(a=t.offsetWidth-t.clientWidth)&&(n&&(a*=-1),s.left+=a,s.right+=a),s},as=function(a,u,e){var t,s,l=Ei(T.none()),c=function(){!function(e){var t,n,r,o,i;for(t=pn("*[contentEditable=false]",e),o=0;o<t.length;o++)r=(n=t[o]).previousSibling,Ta(r)&&(1===(i=r.data).length?r.parentNode.removeChild(r):r.deleteData(i.length-1,1)),r=n.nextSibling,ka(r)&&(1===(i=r.data).length?r.parentNode.removeChild(r):r.deleteData(0,1))}(a),s&&(ns.remove(s),s=null),l.get().each(function(e){pn(e.caret).remove(),l.set(T.none())}),clearInterval(t)},f=function(){t=be.setInterval(function(){e()?pn("div.mce-visual-caret",a).toggleClass("mce-visual-caret-hidden"):pn("div.mce-visual-caret",a).addClass("mce-visual-caret-hidden")},500)};return{show:function(t,e){var n,r,o;if(c(),o=e,Bo.isElement(o)&&/^(TD|TH)$/i.test(o.tagName))return null;if(!u(e))return s=function(e,t){var n,r,o;if(r=e.ownerDocument.createTextNode(pa),o=e.parentNode,t){if(n=e.previousSibling,ya(n)){if(xa(n))return n;if(Ta(n))return n.splitText(n.data.length-1)}o.insertBefore(r,e)}else{if(n=e.nextSibling,ya(n)){if(xa(n))return n;if(ka(n))return n.splitText(1),n}e.nextSibling?o.insertBefore(r,e.nextSibling):o.appendChild(r)}return r}(e,t),r=e.ownerDocument.createRange(),os(s.nextSibling)?(r.setStart(s,0),r.setEnd(s,0)):(r.setStart(s,1),r.setEnd(s,1)),r;s=Sa("p",e,t),n=is(a,e,t),pn(s).css("top",n.top);var i=pn('<div class="mce-visual-caret" data-mce-bogus="all"></div>').css(n).appendTo(a)[0];return l.set(T.some({caret:i,element:e,before:t})),l.get().each(function(e){t&&pn(e.caret).addClass("mce-visual-caret-before")}),f(),(r=e.ownerDocument.createRange()).setStart(s,0),r.setEnd(s,0),r},hide:c,getCss:function(){return".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}"},reposition:function(){l.get().each(function(e){var t=is(a,e.element,e.before);pn(e.caret).css(t)})},destroy:function(){return be.clearInterval(t)}}},us=function(){return rs.isIE()||rs.isEdge()||rs.isFirefox()},ss=function(e){return os(e)||Bo.isTable(e)&&us()},ls=Bo.isContentEditableFalse,cs=Bo.matchStyleValues("display","block table table-cell table-caption list-item"),fs=xa,ds=Ca,hs=Bo.isElement,ms=La,gs=function(e){return 0<e},ps=function(e){return e<0},vs=function(e,t){for(var n;n=e(t);)if(!ds(n))return n;return null},bs=function(e,t,n,r,o){var i=new io(e,r);if(ps(t)){if((ls(e)||ds(e))&&n(e=vs(i.prev,!0)))return e;for(;e=vs(i.prev,o);)if(n(e))return e}if(gs(t)){if((ls(e)||ds(e))&&n(e=vs(i.next,!0)))return e;for(;e=vs(i.next,o);)if(n(e))return e}return null},ys=function(e,t){for(;e&&e!==t;){if(cs(e))return e;e=e.parentNode}return null},Cs=function(e,t,n){return ys(e.container(),n)===ys(t.container(),n)},ws=function(e,t){var n,r;return t?(n=t.container(),r=t.offset(),hs(n)?n.childNodes[r+e]:null):null},xs=function(e,t){var n=t.ownerDocument.createRange();return e?(n.setStartBefore(t),n.setEndBefore(t)):(n.setStartAfter(t),n.setEndAfter(t)),n},Ns=function(e,t,n){var r,o,i,a;for(o=e?"previousSibling":"nextSibling";n&&n!==t;){if(r=n[o],fs(r)&&(r=r[o]),ls(r)){if(a=n,ys(r,i=t)===ys(a,i))return r;break}if(ms(r))break;n=n.parentNode}return null},Es=d(xs,!0),zs=d(xs,!1),Ss=function(e,t,n){var r,o,i,a,u=d(Ns,!0,t),s=d(Ns,!1,t);if(o=n.startContainer,i=n.startOffset,Ca(o)){if(hs(o)||(o=o.parentNode),"before"===(a=o.getAttribute("data-mce-caret"))&&(r=o.nextSibling,ss(r)))return Es(r);if("after"===a&&(r=o.previousSibling,ss(r)))return zs(r)}if(!n.collapsed)return n;if(Bo.isText(o)){if(fs(o)){if(1===e){if(r=s(o))return Es(r);if(r=u(o))return zs(r)}if(-1===e){if(r=u(o))return zs(r);if(r=s(o))return Es(r)}return n}if(Ta(o)&&i>=o.data.length-1)return 1===e&&(r=s(o))?Es(r):n;if(ka(o)&&i<=1)return-1===e&&(r=u(o))?zs(r):n;if(i===o.data.length)return(r=s(o))?Es(r):n;if(0===i)return(r=u(o))?zs(r):n}return n},ks=function(e,t){return T.from(ws(e?0:-1,t)).filter(ls)},Ts=function(e,t,n){var r=Ss(e,t,n);return-1===e?yu.fromRangeStart(r):yu.fromRangeEnd(r)},As=function(e){return T.from(e.getNode()).map(or.fromDom)},Rs=function(e,t){for(;t=e(t);)if(t.isVisible())return t;return t},Ds=function(e,t){var n=Cs(e,t);return!(n||!Bo.isBr(e.getNode()))||n};(wu=Cu||(Cu={}))[wu.Backwards=-1]="Backwards",wu[wu.Forwards=1]="Forwards";var Ms=Bo.isContentEditableFalse,Bs=Bo.isText,_s=Bo.isElement,Os=Bo.isBr,Hs=La,Ps=function(e){return Oa(e)||!!Va(t=e)&&!0!==I(re(t.getElementsByTagName("*")),function(e,t){return e||Ra(t)},!1);var t},Ls=Ia,Vs=function(e,t){return e.hasChildNodes()&&t<e.childNodes.length?e.childNodes[t]:null},Is=function(e,t){if(gs(e)){if(Hs(t.previousSibling)&&!Bs(t.previousSibling))return xu.before(t);if(Bs(t))return xu(t,0)}if(ps(e)){if(Hs(t.nextSibling)&&!Bs(t.nextSibling))return xu.after(t);if(Bs(t))return xu(t,t.data.length)}return ps(e)?Os(t)?xu.before(t):xu.after(t):xu.before(t)},Fs=function(e,t,n){var r,o,i,a,u;if(!_s(n)||!t)return null;if(t.isEqual(xu.after(n))&&n.lastChild){if(u=xu.after(n.lastChild),ps(e)&&Hs(n.lastChild)&&_s(n.lastChild))return Os(n.lastChild)?xu.before(n.lastChild):u}else u=t;var s,l,c,f=u.container(),d=u.offset();if(Bs(f)){if(ps(e)&&0<d)return xu(f,--d);if(gs(e)&&d<f.length)return xu(f,++d);r=f}else{if(ps(e)&&0<d&&(o=Vs(f,d-1),Hs(o)))return!Ps(o)&&(i=bs(o,e,Ls,o))?Bs(i)?xu(i,i.data.length):xu.after(i):Bs(o)?xu(o,o.data.length):xu.before(o);if(gs(e)&&d<f.childNodes.length&&(o=Vs(f,d),Hs(o)))return Os(o)?(s=n,(c=(l=o).nextSibling)&&Hs(c)?Bs(c)?xu(c,0):xu.before(c):Fs(Cu.Forwards,xu.after(l),s)):!Ps(o)&&(i=bs(o,e,Ls,o))?Bs(i)?xu(i,0):xu.before(i):Bs(o)?xu(o,0):xu.after(o);r=o||u.getNode()}return(gs(e)&&u.isAtEnd()||ps(e)&&u.isAtStart())&&(r=bs(r,e,$(!0),n,!0),Ls(r,n))?Is(e,r):(o=bs(r,e,Ls,n),!(a=$t.last(V(function(e,t){for(var n=[];e&&e!==t;)n.push(e),e=e.parentNode;return n}(f,n),Ms)))||o&&a.contains(o)?o?Is(e,o):null:u=gs(e)?xu.after(a):xu.before(a))},Us=function(t){return{next:function(e){return Fs(Cu.Forwards,e,t)},prev:function(e){return Fs(Cu.Backwards,e,t)}}},js=function(e){return xu.isTextPosition(e)?0===e.offset():La(e.getNode())},qs=function(e){if(xu.isTextPosition(e)){var t=e.container();return e.offset()===t.data.length}return La(e.getNode(!0))},$s=function(e,t){return!xu.isTextPosition(e)&&!xu.isTextPosition(t)&&e.getNode()===t.getNode(!0)},Ws=function(e,t,n){return e?!$s(t,n)&&(r=t,!(!xu.isTextPosition(r)&&Bo.isBr(r.getNode())))&&qs(t)&&js(n):!$s(n,t)&&js(t)&&qs(n);var r},Ks=function(e,t,n){var r=Us(t);return T.from(e?r.next(n):r.prev(n))},Xs=function(t,n,r){return Ks(t,n,r).bind(function(e){return Cs(r,e,n)&&Ws(t,r,e)?Ks(t,n,e):T.some(e)})},Ys=function(t,n,e,r){return Xs(t,n,e).bind(function(e){return r(e)?Ys(t,n,e,r):T.some(e)})},Gs=function(e,t){var n,r,o,i,a,u=e?t.firstChild:t.lastChild;return Bo.isText(u)?T.some(xu(u,e?0:u.data.length)):u?La(u)?T.some(e?xu.before(u):(a=u,Bo.isBr(a)?xu.before(a):xu.after(a))):(r=t,o=u,i=(n=e)?xu.before(o):xu.after(o),Ks(n,r,i)):T.none()},Js=d(Ks,!0),Qs=d(Ks,!1),Zs={fromPosition:Ks,nextPosition:Js,prevPosition:Qs,navigate:Xs,navigateIgnore:Ys,positionIn:Gs,firstPositionIn:d(Gs,!0),lastPositionIn:d(Gs,!1)},el=function(e,t){return!e.isBlock(t)||t.innerHTML||he.ie||(t.innerHTML='<br data-mce-bogus="1" />'),t},tl=function(e,t){return Zs.lastPositionIn(e).fold(function(){return!1},function(e){return t.setStart(e.container(),e.offset()),t.setEnd(e.container(),e.offset()),!0})},nl=function(e,t,n){return!(!1!==t.hasChildNodes()||!Wu(e,t)||(o=n,i=(r=t).ownerDocument.createTextNode(pa),r.appendChild(i),o.setStart(i,0),o.setEnd(i,0),0));var r,o,i},rl=function(e,t,n,r){var o,i,a,u,s=n[t?"start":"end"],l=e.getRoot();if(s){for(a=s[0],i=l,o=s.length-1;1<=o;o--){if(u=i.childNodes,nl(l,i,r))return!0;if(s[o]>u.length-1)return!!nl(l,i,r)||tl(i,r);i=u[s[o]]}3===i.nodeType&&(a=Math.min(s[0],i.nodeValue.length)),1===i.nodeType&&(a=Math.min(s[0],i.childNodes.length)),t?r.setStart(i,a):r.setEnd(i,a)}return!0},ol=function(e){return Bo.isText(e)&&0<e.data.length},il=function(e,t,n){var r,o,i,a,u,s,l=e.get(n.id+"_"+t),c=n.keep;if(l){if(r=l.parentNode,o="start"===t?c?l.hasChildNodes()?(r=l.firstChild,1):ol(l.nextSibling)?(r=l.nextSibling,0):ol(l.previousSibling)?(r=l.previousSibling,l.previousSibling.data.length):(r=l.parentNode,e.nodeIndex(l)+1):e.nodeIndex(l):c?l.hasChildNodes()?(r=l.firstChild,1):ol(l.previousSibling)?(r=l.previousSibling,l.previousSibling.data.length):(r=l.parentNode,e.nodeIndex(l)):e.nodeIndex(l),u=r,s=o,!c){for(a=l.previousSibling,i=l.nextSibling,Gt.each(Gt.grep(l.childNodes),function(e){Bo.isText(e)&&(e.nodeValue=e.nodeValue.replace(/\uFEFF/g,""))});l=e.get(n.id+"_"+t);)e.remove(l,!0);a&&i&&a.nodeType===i.nodeType&&Bo.isText(a)&&!he.opera&&(o=a.nodeValue.length,a.appendData(i.nodeValue),e.remove(i),u=a,s=o)}return T.some(xu(u,s))}return T.none()},al=function(e,t){var n,r,o,i,a,u,s,l,c,f,d,h,m,g,p,v,b=e.dom;if(t){if(v=t,Gt.isArray(v.start))return g=t,p=(m=b).createRng(),rl(m,!0,g,p)&&rl(m,!1,g,p)?T.some(p):T.none();if("string"==typeof t.start)return T.some((f=t,d=(c=b).createRng(),h=Du(c.getRoot(),f.start),d.setStart(h.container(),h.offset()),h=Du(c.getRoot(),f.end),d.setEnd(h.container(),h.offset()),d));if(t.hasOwnProperty("id"))return s=il(o=b,"start",i=t),l=il(o,"end",i),Ja([s,(a=l,u=s,a.isSome()?a:u)],function(e,t){var n=o.createRng();return n.setStart(el(o,e.container()),e.offset()),n.setEnd(el(o,t.container()),t.offset()),n});if(t.hasOwnProperty("name"))return n=b,r=t,T.from(n.select(r.name)[r.index]).map(function(e){var t=n.createRng();return t.selectNode(e),t});if(t.hasOwnProperty("rng"))return T.some(t.rng)}return T.none()},ul=function(e,t,n){return ju.getBookmark(e,t,n)},sl=function(t,e){al(t,e).each(function(e){t.setRng(e)})},ll=function(e){return Bo.isElement(e)&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type")},cl=function(e){return e&&/^(IMG)$/.test(e.nodeName)},fl=function(e){return e&&3===e.nodeType&&/^([\t \r\n]+|)$/.test(e.nodeValue)},dl=function(e,t,n){return"color"!==n&&"backgroundColor"!==n||(t=e.toHex(t)),"fontWeight"===n&&700===t&&(t="bold"),"fontFamily"===n&&(t=t.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),""+t},hl={isInlineBlock:cl,moveStart:function(e,t,n){var r,o,i,a=n.startOffset,u=n.startContainer;if((n.startContainer!==n.endContainer||!cl(n.startContainer.childNodes[n.startOffset]))&&1===u.nodeType)for(a<(i=u.childNodes).length?r=new io(u=i[a],e.getParent(u,e.isBlock)):(r=new io(u=i[i.length-1],e.getParent(u,e.isBlock))).next(!0),o=r.current();o;o=r.next())if(3===o.nodeType&&!fl(o))return n.setStart(o,0),void t.setRng(n)},getNonWhiteSpaceSibling:function(e,t,n){if(e)for(t=t?"nextSibling":"previousSibling",e=n?e:e[t];e;e=e[t])if(1===e.nodeType||!fl(e))return e},isTextBlock:function(e,t){return t.nodeType&&(t=t.nodeName),!!e.schema.getTextBlockElements()[t.toLowerCase()]},isValid:function(e,t,n){return e.schema.isValidChild(t,n)},isWhiteSpaceNode:fl,replaceVars:function(e,n){return"string"!=typeof e?e=e(n):n&&(e=e.replace(/%(\w+)/g,function(e,t){return n[t]||e})),e},isEq:function(e,t){return t=t||"",e=""+((e=e||"").nodeName||e),t=""+(t.nodeName||t),e.toLowerCase()===t.toLowerCase()},normalizeStyleValue:dl,getStyle:function(e,t,n){return dl(e,e.getStyle(t,n),n)},getTextDecoration:function(t,e){var n;return t.getParent(e,function(e){return(n=t.getStyle(e,"text-decoration"))&&"none"!==n}),n},getParents:function(e,t,n){return e.getParents(t,n,e.getRoot())}},ml=ll,gl=hl.getParents,pl=hl.isWhiteSpaceNode,vl=hl.isTextBlock,bl=function(e,t){for(void 0===t&&(t=3===e.nodeType?e.length:e.childNodes.length);e&&e.hasChildNodes();)(e=e.childNodes[t])&&(t=3===e.nodeType?e.length:e.childNodes.length);return{node:e,offset:t}},yl=function(e,t){for(var n=t;n;){if(1===n.nodeType&&e.getContentEditable(n))return"false"===e.getContentEditable(n)?n:t;n=n.parentNode}return t},Cl=function(e,t,n,r){var o,i,a=n.nodeValue;return void 0===r&&(r=e?a.length:0),e?(o=a.lastIndexOf(" ",r),-1!==(o=(i=a.lastIndexOf("\xa0",r))<o?o:i)&&!t&&(o<r||!e)&&o<=a.length&&o++):(o=a.indexOf(" ",r),i=a.indexOf("\xa0",r),o=-1!==o&&(-1===i||o<i)?o:i),o},wl=function(e,t,n,r,o,i){var a,u,s,l;if(3===n.nodeType){if(-1!==(s=Cl(o,i,n,r)))return{container:n,offset:s};l=n}for(a=new io(n,e.getParent(n,e.isBlock)||t);u=a[o?"prev":"next"]();)if(3!==u.nodeType||ml(u.parentNode)){if(e.isBlock(u)||hl.isEq(u,"BR"))break}else if(-1!==(s=Cl(o,i,l=u)))return{container:u,offset:s};if(l)return{container:l,offset:r=o?0:l.length}},xl=function(e,t,n,r,o){var i,a,u,s;for(3===r.nodeType&&0===r.nodeValue.length&&r[o]&&(r=r[o]),i=gl(e,r),a=0;a<i.length;a++)for(u=0;u<t.length;u++)if(!("collapsed"in(s=t[u])&&s.collapsed!==n.collapsed)&&e.is(i[a],s.selector))return i[a];return r},Nl=function(t,e,n,r){var o,i=t.dom,a=i.getRoot();if(e[0].wrapper||(o=i.getParent(n,e[0].block,a)),!o){var u=i.getParent(n,"LI,TD,TH");o=i.getParent(3===n.nodeType?n.parentNode:n,function(e){return e!==a&&vl(t,e)},u)}if(o&&e[0].wrapper&&(o=gl(i,o,"ul,ol").reverse()[0]||o),!o)for(o=n;o[r]&&!i.isBlock(o[r])&&(o=o[r],!hl.isEq(o,"br")););return o||n},El=function(e,t,n,r,o,i,a){var u,s,l,c,f,d;if(u=s=a?n:o,c=a?"previousSibling":"nextSibling",f=e.getRoot(),3===u.nodeType&&!pl(u)&&(a?0<r:i<u.nodeValue.length))return u;for(;;){if(!t[0].block_expand&&e.isBlock(s))return s;for(l=s[c];l;l=l[c])if(!ml(l)&&!pl(l)&&("BR"!==(d=l).nodeName||!d.getAttribute("data-mce-bogus")||d.nextSibling))return s;if(s===f||s.parentNode===f){u=s;break}s=s.parentNode}return u},zl=function(e,t,n,r){var o,i=t.startContainer,a=t.startOffset,u=t.endContainer,s=t.endOffset,l=e.dom;return 1===i.nodeType&&i.hasChildNodes()&&3===(i=Xa(i,a)).nodeType&&(a=0),1===u.nodeType&&u.hasChildNodes()&&3===(u=Xa(u,t.collapsed?s:s-1)).nodeType&&(s=u.nodeValue.length),i=yl(l,i),u=yl(l,u),(ml(i.parentNode)||ml(i))&&(i=ml(i)?i:i.parentNode,3===(i=t.collapsed?i.previousSibling||i:i.nextSibling||i).nodeType&&(a=t.collapsed?i.length:0)),(ml(u.parentNode)||ml(u))&&(u=ml(u)?u:u.parentNode,3===(u=t.collapsed?u.nextSibling||u:u.previousSibling||u).nodeType&&(s=t.collapsed?0:u.length)),t.collapsed&&((o=wl(l,e.getBody(),i,a,!0,r))&&(i=o.container,a=o.offset),(o=wl(l,e.getBody(),u,s,!1,r))&&(u=o.container,s=o.offset)),n[0].inline&&(u=r?u:function(e,t){var n=bl(e,t);if(n.node){for(;n.node&&0===n.offset&&n.node.previousSibling;)n=bl(n.node.previousSibling);n.node&&0<n.offset&&3===n.node.nodeType&&" "===n.node.nodeValue.charAt(n.offset-1)&&1<n.offset&&(e=n.node).splitText(n.offset-1)}return e}(u,s)),(n[0].inline||n[0].block_expand)&&(n[0].inline&&3===i.nodeType&&0!==a||(i=El(l,n,i,a,u,s,!0)),n[0].inline&&3===u.nodeType&&s!==u.nodeValue.length||(u=El(l,n,i,a,u,s,!1))),n[0].selector&&!1!==n[0].expand&&!n[0].inline&&(i=xl(l,n,t,i,"previousSibling"),u=xl(l,n,t,u,"nextSibling")),(n[0].block||n[0].selector)&&(i=Nl(e,n,i,"previousSibling"),u=Nl(e,n,u,"nextSibling"),n[0].block&&(l.isBlock(i)||(i=El(l,n,i,a,u,s,!0)),l.isBlock(u)||(u=El(l,n,i,a,u,s,!1)))),1===i.nodeType&&(a=l.nodeIndex(i),i=i.parentNode),1===u.nodeType&&(s=l.nodeIndex(u)+1,u=u.parentNode),{startContainer:i,startOffset:a,endContainer:u,endOffset:s}},Sl=Gt.each,kl=function(e,t,o){var n,r,i,a,u,s,l,c=t.startContainer,f=t.startOffset,d=t.endContainer,h=t.endOffset;if(0<(l=e.select("td[data-mce-selected],th[data-mce-selected]")).length)Sl(l,function(e){o([e])});else{var m,g,p,v=function(e){var t;return 3===(t=e[0]).nodeType&&t===c&&f>=t.nodeValue.length&&e.splice(0,1),t=e[e.length-1],0===h&&0<e.length&&t===d&&3===t.nodeType&&e.splice(e.length-1,1),e},b=function(e,t,n){for(var r=[];e&&e!==n;e=e[t])r.push(e);return r},y=function(e,t){do{if(e.parentNode===t)return e;e=e.parentNode}while(e)},C=function(e,t,n){var r=n?"nextSibling":"previousSibling";for(u=(a=e).parentNode;a&&a!==t;a=u)u=a.parentNode,(s=b(a===e?a:a[r],r)).length&&(n||s.reverse(),o(v(s)))};if(1===c.nodeType&&c.hasChildNodes()&&(c=c.childNodes[f]),1===d.nodeType&&d.hasChildNodes()&&(g=h,p=(m=d).childNodes,--g>p.length-1?g=p.length-1:g<0&&(g=0),d=p[g]||m),c===d)return o(v([c]));for(n=e.findCommonAncestor(c,d),a=c;a;a=a.parentNode){if(a===d)return C(c,n,!0);if(a===n)break}for(a=d;a;a=a.parentNode){if(a===c)return C(d,n);if(a===n)break}r=y(c,n)||c,i=y(d,n)||d,C(c,r,!0),(s=b(r===c?r:r.nextSibling,"nextSibling",i===d?i.nextSibling:i)).length&&o(v(s)),C(d,i)}},Tl=function WN(n,r){var t=function(e){return n(e)?T.from(e.dom().nodeValue):T.none()},e=nr.detect().browser,o=e.isIE()&&10===e.version.major?function(e){try{return t(e)}catch($N){return T.none()}}:t;return{get:function(e){if(!n(e))throw new Error("Can only get "+r+" value of a "+r+" node");return o(e).getOr("")},getOption:o,set:function(e,t){if(!n(e))throw new Error("Can only set raw "+r+" value of a "+r+" node");e.dom().nodeValue=t}}}(fr,"text"),Al=function(e){return Tl.get(e)},Rl=function(r,o,i,a){return Lr(o).fold(function(){return"skipping"},function(e){return"br"===a||fr(n=o)&&"\ufeff"===Al(n)?"valid":cr(t=o)&&$i(t,ta())?"existing":$u(o)?"caret":hl.isValid(r,i,a)&&hl.isValid(r,sr(e),i)?"valid":"invalid-child";var t,n})},Dl=function(e,t,n,r){var o,i,a=t.uid,u=void 0===a?(o="mce-annotation",i=(new Date).getTime(),o+"_"+Math.floor(1e9*Math.random())+ ++la+String(i)):a,s=function p(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&(n[r[o]]=e[r[o]])}return n}(t,["uid"]),l=or.fromTag("span",e);ji(l,ta()),yr(l,""+ra(),u),yr(l,""+na(),n);var c,f=r(u,s),d=f.attributes,h=void 0===d?{}:d,m=f.classes,g=void 0===m?[]:m;return Cr(l,h),c=l,L(g,function(e){ji(c,e)}),l},Ml=function(i,e,t,n,r){var a=[],u=Dl(i.getDoc(),r,t,n),s=Ei(T.none()),l=function(){s.set(T.none())},c=function(e){L(e,o)},o=function(e){var t,n;switch(Rl(i,e,"span",sr(e))){case"invalid-child":l();var r=jr(e);c(r),l();break;case"valid":var o=s.get().getOrThunk(function(){var e=fa(u);return a.push(e),s.set(T.some(e)),e});Ri(t=e,n=o),Bi(n,t)}};return kl(i.dom,e,function(e){var t;l(),t=K(e,or.fromDom),c(t)}),a},Bl=function(s,l,c,f){s.undoManager.transact(function(){var e,t,n,r,o=s.selection.getRng();if(o.collapsed&&(r=zl(e=s,t=o,[{inline:!0}],3===(n=t).startContainer.nodeType&&n.startContainer.nodeValue.length>=n.startOffset&&"\xa0"===n.startContainer.nodeValue[n.startOffset]),t.setStart(r.startContainer,r.startOffset),t.setEnd(r.endContainer,r.endOffset),e.selection.setRng(t)),s.selection.getRng().collapsed){var i=Dl(s.getDoc(),f,l,c.decorate);ha(i,"\xa0"),s.selection.getRng().insertNode(i.dom()),s.selection.select(i.dom())}else{var a=ju.getPersistentBookmark(s.selection,!1),u=s.selection.getRng();Ml(s,u,l,c.decorate,f),s.selection.moveToBookmark(a)}})};function _l(s){var n,r=(n={},{register:function(e,t){n[e]={name:e,settings:t}},lookup:function(e){return n.hasOwnProperty(e)?T.from(n[e]).map(function(e){return e.settings}):T.none()}});ua(s,r);var o=aa(s);return{register:function(e,t){r.register(e,t)},annotate:function(t,n){r.lookup(t).each(function(e){Bl(s,t,e,n)})},annotationChanged:function(e,t){o.addListener(e,t)},remove:function(e){oa(s,T.some(e)).each(function(e){var t=e.elements;L(t,Pi)})},getAll:function(e){var t,n,r,o,i,a,u=(t=s,n=e,r=or.fromDom(t.getBody()),o=Ki(r,"["+na()+'="'+n+'"]'),i={},L(o,function(e){var t=wr(e,ra()),n=i.hasOwnProperty(t)?i[t]:[];i[t]=n.concat([e])}),i);return a=function(e){return K(e,function(e){return e.dom()})},gr(u,function(e,t,n){return{k:t,v:a(e,t,n)}})}}}var Ol,Hl=Object.prototype.hasOwnProperty,Pl=(Ol=function(e,t){return t},function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];if(0===e.length)throw new Error("Can't merge zero objects");for(var n={},r=0;r<e.length;r++){var o=e[r];for(var i in o)Hl.call(o,i)&&(n[i]=Ol(n[i],o[i]))}return n}),Ll=/^[ \t\r\n]*$/,Vl={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},Il=function(e,t,n){var r,o,i=n?"lastChild":"firstChild",a=n?"prev":"next";if(e[i])return e[i];if(e!==t){if(r=e[a])return r;for(o=e.parent;o&&o!==t;o=o.parent)if(r=o[a])return r}},Fl=function(){function a(e,t){this.name=e,1===(this.type=t)&&(this.attributes=[],this.attributes.map={})}return a.create=function(e,t){var n,r;if(n=new a(e,Vl[e]||1),t)for(r in t)n.attr(r,t[r]);return n},a.prototype.replace=function(e){return e.parent&&e.remove(),this.insert(e,this),this.remove(),this},a.prototype.attr=function(e,t){var n,r;if("string"!=typeof e){for(r in e)this.attr(r,e[r]);return this}if(n=this.attributes){if(t===undefined)return n.map[e];if(null===t){if(e in n.map)for(delete n.map[e],r=n.length;r--;)if(n[r].name===e)return n=n.splice(r,1),this;return this}if(e in n.map){for(r=n.length;r--;)if(n[r].name===e){n[r].value=t;break}}else n.push({name:e,value:t});return n.map[e]=t,this}},a.prototype.clone=function(){var e,t,n,r,o,i=new a(this.name,this.type);if(n=this.attributes){for((o=[]).map={},e=0,t=n.length;e<t;e++)"id"!==(r=n[e]).name&&(o[o.length]={name:r.name,value:r.value},o.map[r.name]=r.value);i.attributes=o}return i.value=this.value,i.shortEnded=this.shortEnded,i},a.prototype.wrap=function(e){return this.parent.insert(e,this),e.append(this),this},a.prototype.unwrap=function(){var e,t;for(e=this.firstChild;e;)t=e.next,this.insert(e,this,!0),e=t;this.remove()},a.prototype.remove=function(){var e=this.parent,t=this.next,n=this.prev;return e&&(e.firstChild===this?(e.firstChild=t)&&(t.prev=null):n.next=t,e.lastChild===this?(e.lastChild=n)&&(n.next=null):t.prev=n,this.parent=this.next=this.prev=null),this},a.prototype.append=function(e){var t;return e.parent&&e.remove(),t=this.lastChild,this.lastChild=t?((t.next=e).prev=t,e):this.firstChild=e,e.parent=this,e},a.prototype.insert=function(e,t,n){var r;return e.parent&&e.remove(),r=t.parent||this,n?(t===r.firstChild?r.firstChild=e:t.prev.next=e,e.prev=t.prev,(e.next=t).prev=e):(t===r.lastChild?r.lastChild=e:t.next.prev=e,e.next=t.next,(e.prev=t).next=e),e.parent=r,e},a.prototype.getAll=function(e){var t,n=[];for(t=this.firstChild;t;t=Il(t,this))t.name===e&&n.push(t);return n},a.prototype.empty=function(){var e,t,n;if(this.firstChild){for(e=[],n=this.firstChild;n;n=Il(n,this))e.push(n);for(t=e.length;t--;)(n=e[t]).parent=n.firstChild=n.lastChild=n.next=n.prev=null}return this.firstChild=this.lastChild=null,this},a.prototype.isEmpty=function(e,t,n){var r,o,i=this.firstChild;if(t=t||{},i)do{if(1===i.type){if(i.attributes.map["data-mce-bogus"])continue;if(e[i.name])return!1;for(r=i.attributes.length;r--;)if("name"===(o=i.attributes[r].name)||0===o.indexOf("data-mce-bookmark"))return!1}if(8===i.type)return!1;if(3===i.type&&!Ll.test(i.value))return!1;if(3===i.type&&i.parent&&t[i.parent.name]&&Ll.test(i.value))return!1;if(n&&n(i))return!1}while(i=Il(i,this));return!0},a.prototype.walk=function(e){return Il(this,null,e)},a}(),Ul=function(e,t,n){var r,o,i,a,u=1;for(a=e.getShortEndedElements(),(i=/<([!?\/])?([A-Za-z0-9\-_\:\.]+)((?:\s+[^"\'>]+(?:(?:"[^"]*")|(?:\'[^\']*\')|[^>]*))*|\/|\s+)>/g).lastIndex=r=n;o=i.exec(t);){if(r=i.lastIndex,"/"===o[1])u--;else if(!o[1]){if(o[2]in a)continue;u++}if(0===u)break}return r},jl=function(e,t){var n=e.exec(t);if(n){var r=n[1],o=n[2];return"string"==typeof r&&"data-mce-bogus"===r.toLowerCase()?o:null}return null};function ql(V,I){void 0===I&&(I=oi());var e=function(){};!1!==(V=V||{}).fix_self_closing&&(V.fix_self_closing=!0);var F=V.comment?V.comment:e,U=V.cdata?V.cdata:e,j=V.text?V.text:e,q=V.start?V.start:e,$=V.end?V.end:e,W=V.pi?V.pi:e,K=V.doctype?V.doctype:e;return{parse:function(e){var t,n,r,d,o,i,a,h,u,s,m,l,g,c,f,p,v,b,y,C,w,x,N,E,z,S,k,T,A,R=0,D=[],M=0,B=Ko.decode,_=Gt.makeMap("src,href,data,background,formaction,poster,xlink:href"),O=/((java|vb)script|mhtml):/i,H=function(e){var t,n;for(t=D.length;t--&&D[t].name!==e;);if(0<=t){for(n=D.length-1;t<=n;n--)(e=D[n]).valid&&$(e.name);D.length=t}},P=function(e,t,n,r,o){var i,a,u,s,l;if(n=(t=t.toLowerCase())in m?t:B(n||r||o||""),g&&!h&&0==(0===(u=t).indexOf("data-")||0===u.indexOf("aria-"))){if(!(i=b[t])&&y){for(a=y.length;a--&&!(i=y[a]).pattern.test(t););-1===a&&(i=null)}if(!i)return;if(i.validValues&&!(n in i.validValues))return}if(_[t]&&!V.allow_script_urls){var c=n.replace(/[\s\u0000-\u001F]+/g,"");try{c=decodeURIComponent(c)}catch(f){c=unescape(c)}if(O.test(c))return;if(l=c,!(s=V).allow_html_data_urls&&(/^data:image\//i.test(l)?!1===s.allow_svg_data_urls&&/^data:image\/svg\+xml/i.test(l):/^data:/i.test(l)))return}h&&(t in _||0===t.indexOf("on"))||(d.map[t]=n,d.push({name:t,value:n}))};for(z=new RegExp("<(?:(?:!--([\\w\\W]*?)--\x3e)|(?:!\\[CDATA\\[([\\w\\W]*?)\\]\\]>)|(?:!DOCTYPE([\\w\\W]*?)>)|(?:\\?([^\\s\\/<>]+) ?([\\w\\W]*?)[?/]>)|(?:\\/([A-Za-z][A-Za-z0-9\\-_\\:\\.]*)>)|(?:([A-Za-z][A-Za-z0-9\\-_\\:\\.]*)((?:\\s+[^\"'>]+(?:(?:\"[^\"]*\")|(?:'[^']*')|[^>]*))*|\\/|\\s+)>))","g"),S=/([\w:\-]+)(?:\s*=\s*(?:(?:\"((?:[^\"])*)\")|(?:\'((?:[^\'])*)\')|([^>\s]+)))?/g,s=I.getShortEndedElements(),E=V.self_closing_elements||I.getSelfClosingElements(),m=I.getBoolAttrs(),g=V.validate,u=V.remove_internals,A=V.fix_self_closing,k=I.getSpecialElements(),N=e+">";t=z.exec(N);){if(R<t.index&&j(B(e.substr(R,t.index-R))),n=t[6])":"===(n=n.toLowerCase()).charAt(0)&&(n=n.substr(1)),H(n);else if(n=t[7]){if(t.index+t[0].length>e.length){j(B(e.substr(t.index))),R=t.index+t[0].length;continue}":"===(n=n.toLowerCase()).charAt(0)&&(n=n.substr(1)),l=n in s,A&&E[n]&&0<D.length&&D[D.length-1].name===n&&H(n);var L=jl(S,t[8]);if(null!==L){if("all"===L){R=Ul(I,e,z.lastIndex),z.lastIndex=R;continue}f=!1}if(!g||(c=I.getElementRule(n))){if(f=!0,g&&(b=c.attributes,y=c.attributePatterns),(v=t[8])?((h=-1!==v.indexOf("data-mce-type"))&&u&&(f=!1),(d=[]).map={},v.replace(S,P)):(d=[]).map={},g&&!h){if(C=c.attributesRequired,w=c.attributesDefault,x=c.attributesForced,c.removeEmptyAttrs&&!d.length&&(f=!1),x)for(o=x.length;o--;)a=(p=x[o]).name,"{$uid}"===(T=p.value)&&(T="mce_"+M++),d.map[a]=T,d.push({name:a,value:T});if(w)for(o=w.length;o--;)(a=(p=w[o]).name)in d.map||("{$uid}"===(T=p.value)&&(T="mce_"+M++),d.map[a]=T,d.push({name:a,value:T}));if(C){for(o=C.length;o--&&!(C[o]in d.map););-1===o&&(f=!1)}if(p=d.map["data-mce-bogus"]){if("all"===p){R=Ul(I,e,z.lastIndex),z.lastIndex=R;continue}f=!1}}f&&q(n,d,l)}else f=!1;if(r=k[n]){r.lastIndex=R=t.index+t[0].length,R=(t=r.exec(e))?(f&&(i=e.substr(R,t.index-R)),t.index+t[0].length):(i=e.substr(R),e.length),f&&(0<i.length&&j(i,!0),$(n)),z.lastIndex=R;continue}l||(v&&v.indexOf("/")===v.length-1?f&&$(n):D.push({name:n,valid:f}))}else(n=t[1])?(">"===n.charAt(0)&&(n=" "+n),V.allow_conditional_comments||"[if"!==n.substr(0,3).toLowerCase()||(n=" "+n),F(n)):(n=t[2])?U(n.replace(/<!--|-->/g,"")):(n=t[3])?K(n):(n=t[4])&&W(n,t[5]);R=t.index+t[0].length}for(R<e.length&&j(B(e.substr(R))),o=D.length-1;0<=o;o--)(n=D[o]).valid&&$(n.name)}}}(ql||(ql={})).findEndTag=Ul;var $l=ql,Wl=function(e,t){var n,r,o,i,a,u,s,l,c=t,f=/<(\w+) [^>]*data-mce-bogus="all"[^>]*>/g,d=e.schema;for(u=e.getTempAttrs(),s=c,l=new RegExp(["\\s?("+u.join("|")+')="[^"]+"'].join("|"),"gi"),c=s.replace(l,""),a=d.getShortEndedElements();i=f.exec(c);)r=f.lastIndex,o=i[0].length,n=a[i[1]]?r:$l.findEndTag(d,c,r),c=c.substring(0,r-o)+c.substring(n),f.lastIndex=r-o;return va(c)},Kl={trimExternal:Wl,trimInternal:Wl},Xl=function(e,t,n){var r=e.getParam(t,n);if(-1===r.indexOf("="))return r;var o=e.getParam(t,"","hash");return o.hasOwnProperty(e.id)?o[e.id]:n},Yl=function(e){return e.getParam("iframe_attrs",{})},Gl=function(e){return e.getParam("doctype","<!DOCTYPE html>")},Jl=function(e){return e.getParam("document_base_url","")},Ql=function(e){return Xl(e,"body_id","tinymce")},Zl=function(e){return Xl(e,"body_class","")},ec=function(e){return e.getParam("content_security_policy","")},tc=function(e){return e.getParam("br_in_pre",!0)},nc=function(e){if(e.getParam("force_p_newlines",!1))return"p";var t=e.getParam("forced_root_block","p");return!1===t?"":!0===t?"p":t},rc=function(e){return e.getParam("forced_root_block_attrs",{})},oc=function(e){return e.getParam("br_newline_selector",".mce-toc h2,figcaption,caption")},ic=function(e){return e.getParam("no_newline_selector","")},ac=function(e){return e.getParam("keep_styles",!0)},uc=function(e){return e.getParam("end_container_on_empty_block",!1)},sc=function(e){return Gt.explode(e.getParam("font_size_style_values",""))},lc=function(e){return Gt.explode(e.getParam("font_size_classes",""))},cc=function(e){return e.getParam("images_dataimg_filter",$(!0),"function")},fc=function(e){return e.getParam("automatic_uploads",!0,"boolean")},dc=function(e){return e.getParam("images_reuse_filename",!1,"boolean")},hc=function(e){return e.getParam("images_replace_blob_uris",!0,"boolean")},mc=function(e){return e.getParam("images_upload_url","","string")},gc=function(e){return e.getParam("images_upload_base_path","","string")},pc=function(e){return e.getParam("images_upload_credentials",!1,"boolean")},vc=function(e){return e.getParam("images_upload_handler",null,"function")},bc=function(e){return e.getParam("content_css_cors",!1,"boolean")},yc=function(e){return e.getParam("language","en","string")},Cc=function(e){return e.getParam("language_url","","string")},wc=function(e){return e.getParam("indent_use_margin",!1)},xc=function(e){return e.getParam("indentation","40px","string")},Nc=function(e){var t=e.settings.content_css;return A(t)?K(t.split(","),Xn):D(t)?t:!1===t||e.inline?[]:["default"]},Ec=function(e,t,n){var r,o,i,a,u;if(t.format=t.format?t.format:"html",t.get=!0,t.getInner=!0,t.no_events||e.fire("BeforeGetContent",t),"raw"===t.format)r=Gt.trim(Kl.trimExternal(e.serializer,n.innerHTML));else if("text"===t.format)r=va(n.innerText||n.textContent);else{if("tree"===t.format)return e.serializer.serialize(n,t);i=(o=e).serializer.serialize(n,t),a=nc(o),u=new RegExp("^(<"+a+"[^>]*>(&nbsp;|&#160;|\\s|\xa0|<br \\/>|)<\\/"+a+">[\r\n]*|<br \\/>[\r\n]*)$"),r=i.replace(u,"")}return"text"===t.format||wo(or.fromDom(n))?t.content=r:t.content=Gt.trim(r),t.no_events||e.fire("GetContent",t),t.content},zc=Gt.makeMap;function Sc(e){var u,s,l,c,f,d=[];return u=(e=e||{}).indent,s=zc(e.indent_before||""),l=zc(e.indent_after||""),c=Ko.getEncodeFunc(e.entity_encoding||"raw",e.entities),f="html"===e.element_format,{start:function(e,t,n){var r,o,i,a;if(u&&s[e]&&0<d.length&&0<(a=d[d.length-1]).length&&"\n"!==a&&d.push("\n"),d.push("<",e),t)for(r=0,o=t.length;r<o;r++)i=t[r],d.push(" ",i.name,'="',c(i.value,!0),'"');d[d.length]=!n||f?">":" />",n&&u&&l[e]&&0<d.length&&0<(a=d[d.length-1]).length&&"\n"!==a&&d.push("\n")},end:function(e){var t;d.push("</",e,">"),u&&l[e]&&0<d.length&&0<(t=d[d.length-1]).length&&"\n"!==t&&d.push("\n")},text:function(e,t){0<e.length&&(d[d.length]=t?e:c(e))},cdata:function(e){d.push("<![CDATA[",e,"]]>")},comment:function(e){d.push("\x3c!--",e,"--\x3e")},pi:function(e,t){t?d.push("<?",e," ",c(t),"?>"):d.push("<?",e,"?>"),u&&d.push("\n")},doctype:function(e){d.push("<!DOCTYPE",e,">",u?"\n":"")},reset:function(){d.length=0},getContent:function(){return d.join("").replace(/\n$/,"")}}}function kc(t,m){void 0===m&&(m=oi());var g=Sc(t);return(t=t||{}).validate=!("validate"in t)||t.validate,{serialize:function(e){var f,d;d=t.validate,f={3:function(e){g.text(e.value,e.raw)},8:function(e){g.comment(e.value)},7:function(e){g.pi(e.name,e.value)},10:function(e){g.doctype(e.value)},4:function(e){g.cdata(e.value)},11:function(e){if(e=e.firstChild)for(;h(e),e=e.next;);}},g.reset();var h=function(e){var t,n,r,o,i,a,u,s,l,c=f[e.type];if(c)c(e);else{if(t=e.name,n=e.shortEnded,r=e.attributes,d&&r&&1<r.length&&((a=[]).map={},l=m.getElementRule(e.name))){for(u=0,s=l.attributesOrder.length;u<s;u++)(o=l.attributesOrder[u])in r.map&&(i=r.map[o],a.map[o]=i,a.push({name:o,value:i}));for(u=0,s=r.length;u<s;u++)(o=r[u].name)in a.map||(i=r.map[o],a.map[o]=i,a.push({name:o,value:i}));r=a}if(g.start(e.name,r,n),!n){if(e=e.firstChild)for(;h(e),e=e.next;);g.end(t)}}};return 1!==e.type||t.inner?f[11](e):h(e),g.getContent()}}}var Tc=function(e,t){t(e),e.firstChild&&Tc(e.firstChild,t),e.next&&Tc(e.next,t)},Ac=function(e,t,n){var r=function(e,n,t){var r={},o={},i=[];for(var a in t.firstChild&&Tc(t.firstChild,function(t){L(e,function(e){e.name===t.name&&(r[e.name]?r[e.name].nodes.push(t):r[e.name]={filter:e,nodes:[t]})}),L(n,function(e){"string"==typeof t.attr(e.name)&&(o[e.name]?o[e.name].nodes.push(t):o[e.name]={filter:e,nodes:[t]})})}),r)r.hasOwnProperty(a)&&i.push(r[a]);for(var a in o)o.hasOwnProperty(a)&&i.push(o[a]);return i}(e,t,n);L(r,function(t){L(t.filter.callbacks,function(e){e(t.nodes,t.filter.name,{})})})},Rc=function(e){var t=Pr(e).dom();return e.dom()===t.activeElement},Dc=function(t){return(e=Pr(t),n=e!==undefined?e.dom():j.document,T.from(n.activeElement).map(or.fromDom)).filter(function(e){return t.dom().contains(e.dom())});var e,n},Mc=function(a){if(!D(a))throw new Error("cases must be an array");if(0===a.length)throw new Error("there must be at least one case");var u=[],n={};return L(a,function(e,r){var t=dr(e);if(1!==t.length)throw new Error("one and only one name per case");var o=t[0],i=e[o];if(n[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!D(i))throw new Error("case arguments must be an array");u.push(o),n[o]=function(){var e=arguments.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+e);for(var n=new Array(e),t=0;t<n.length;t++)n[t]=arguments[t];return{fold:function(){if(arguments.length!==a.length)throw new Error("Wrong number of arguments to fold. Expected "+a.length+", got "+arguments.length);return arguments[r].apply(null,n)},match:function(e){var t=dr(e);if(u.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+u.join(",")+"\nActual: "+t.join(","));if(!Q(u,function(e){return P(t,e)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+u.join(", "));return e[o].apply(null,n)},log:function(e){console.log(e,{constructors:u,constructor:o,params:n})}}}}),n},Bc=(Mc([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Mc([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}])),_c=Sr("start","soffset","finish","foffset"),Oc=(Bc.domRange,Bc.relative,Bc.exact,nr.detect().browser),Hc=function(e,t){var n=fr(t)?Al(t).length:jr(t).length+1;return n<e?n:e<0?0:e},Pc=function(e){return _c(e.start(),Hc(e.soffset(),e.start()),e.finish(),Hc(e.foffset(),e.finish()))},Lc=function(e,t){return Hr(e,t)||Or(e,t)},Vc=function(t){return function(e){return Lc(t,e.start())&&Lc(t,e.finish())}},Ic=function(e){return!0===e.inline||Oc.isIE()},Fc=function(e){return _c(or.fromDom(e.startContainer),e.startOffset,or.fromDom(e.endContainer),e.endOffset)},Uc=function(e){var t=e.getSelection();return(t&&0!==t.rangeCount?T.from(t.getRangeAt(0)):T.none()).map(Fc)},jc=function(e){var t,n=(t=e.dom().ownerDocument.defaultView,or.fromDom(t));return Uc(n.dom()).filter(Vc(e))},qc=function(e,t){return T.from(t).filter(Vc(e)).map(Pc)},$c=function(e){var t=j.document.createRange();try{return t.setStart(e.start().dom(),e.soffset()),t.setEnd(e.finish().dom(),e.foffset()),T.some(t)}catch(n){return T.none()}},Wc=function(e){return(e.bookmark?e.bookmark:T.none()).bind(d(qc,or.fromDom(e.getBody()))).bind($c)},Kc=function(e){var t=Ic(e)?jc(or.fromDom(e.getBody())):T.none();e.bookmark=t.isSome()?t:e.bookmark},Xc=function(t){Wc(t).each(function(e){t.selection.setRng(e)})},Yc=Wc,Gc=function(t,e){return(n=e,n.collapsed?T.from(Xa(n.startContainer,n.startOffset)).map(or.fromDom):T.none()).bind(function(e){return yo(e)?T.some(e):!1===Hr(t,e)?T.some(t):T.none()});var n},Jc=function(t,e){Gc(or.fromDom(t.getBody()),e).bind(function(e){return Zs.firstPositionIn(e.dom())}).fold(function(){t.selection.normalize()},function(e){return t.selection.setRng(e.toRange())})},Qc=function(e){if(e.setActive)try{e.setActive()}catch(t){e.focus()}else e.focus()},Zc=function(e){var t,n=e.getBody();return n&&(t=or.fromDom(n),Rc(t)||Dc(t).isSome())},ef=function(e){return e.inline?Zc(e):(t=e).iframeElement&&Rc(or.fromDom(t.iframeElement));var t},tf=function(e){return e.editorManager.setActive(e)},nf=function(e,t){e.removed||(t?tf(e):function(t){var e=t.selection,n=t.getBody(),r=e.getRng();t.quirks.refreshContentEditable();var o,i,a=(o=t,i=e.getNode(),o.dom.getParent(i,function(e){return"true"===o.dom.getContentEditable(e)}));if(t.$.contains(n,a))return Qc(a),Jc(t,r),tf(t);t.bookmark!==undefined&&!1===ef(t)&&Yc(t).each(function(e){t.selection.setRng(e),r=e}),t.inline||(he.opera||Qc(n),t.getWin().focus()),(he.gecko||t.inline)&&(Qc(n),Jc(t,r)),tf(t)}(e))},rf=ef,of=function(e){return e instanceof Fl},af=function(e,t){var r;e.dom.setHTML(e.getBody(),t),rf(r=e)&&Zs.firstPositionIn(r.getBody()).each(function(e){var t=e.getNode(),n=Bo.isTable(t)?Zs.firstPositionIn(t).getOr(e):e;r.selection.setRng(n.toRange())})},uf=function(u,s,l){return void 0===l&&(l={}),l.format=l.format?l.format:"html",l.set=!0,l.content=of(s)?"":s,of(s)||l.no_events||(u.fire("BeforeSetContent",l),s=l.content),T.from(u.getBody()).fold($(s),function(e){return of(s)?function(e,t,n,r){Ac(e.parser.getNodeFilters(),e.parser.getAttributeFilters(),n);var o=kc({validate:e.validate},e.schema).serialize(n);return r.content=wo(or.fromDom(t))?o:Gt.trim(o),af(e,r.content),r.no_events||e.fire("SetContent",r),n}(u,e,s,l):(t=u,n=e,o=l,0===(r=s).length||/^\s+$/.test(r)?(a='<br data-mce-bogus="1">',"TABLE"===n.nodeName?r="<tr><td>"+a+"</td></tr>":/^(UL|OL)$/.test(n.nodeName)&&(r="<li>"+a+"</li>"),(i=nc(t))&&t.schema.isValidChild(n.nodeName.toLowerCase(),i.toLowerCase())?(r=a,r=t.dom.createHTML(i,t.settings.forced_root_block_attrs,r)):r||(r='<br data-mce-bogus="1">'),af(t,r),t.fire("SetContent",o)):("raw"!==o.format&&(r=kc({validate:t.validate},t.schema).serialize(t.parser.parse(r,{isRootContent:!0,insert:!0}))),o.content=wo(or.fromDom(n))?r:Gt.trim(r),af(t,o.content),o.no_events||t.fire("SetContent",o)),o.content);var t,n,r,o,i,a})},sf=function(e,t){return e.fire("PreProcess",t)},lf=function(e,t){return e.fire("PostProcess",t)},cf=function(e){return e.fire("remove")},ff=function(e){return e.fire("detach")},df=function(e,t){return e.fire("SwitchMode",{mode:t})},hf=function(e,t,n,r){e.fire("ObjectResizeStart",{target:t,width:n,height:r})},mf=function(e,t,n,r){e.fire("ObjectResized",{target:t,width:n,height:r})},gf=vi.DOM,pf=function(e){return T.from(e).each(function(e){return e.destroy()})},vf=function(e){if(!e.removed){var t=e._selectionOverrides,n=e.editorUpload,r=e.getBody(),o=e.getElement();r&&e.save({is_removing:!0}),e.removed=!0,e.unbindAllNativeEvents(),e.hasHiddenInput&&o&&gf.remove(o.nextSibling),!e.inline&&r&&(i=e,gf.setStyle(i.id,"display",i.orgDisplay)),cf(e),e.editorManager.remove(e),ff(e),gf.remove(e.getContainer()),pf(t),pf(n),e.destroy()}var i},bf=function(e,t){var n,r,o,i=e.selection,a=e.dom;e.destroyed||(t||e.removed?(t||(e.editorManager.off("beforeunload",e._beforeUnload),e.theme&&e.theme.destroy&&e.theme.destroy(),pf(i),pf(a)),(r=(n=e).formElement)&&(r._mceOldSubmit&&(r.submit=r._mceOldSubmit,r._mceOldSubmit=null),gf.unbind(r,"submit reset",n.formEventDelegate)),(o=e).contentAreaContainer=o.formElement=o.container=o.editorContainer=null,o.bodyElement=o.contentDocument=o.contentWindow=null,o.iframeElement=o.targetElm=null,o.selection&&(o.selection=o.selection.win=o.selection.dom=o.selection.dom.doc=null),e.destroyed=!0):e.remove())},yf=Sr("sections","settings"),Cf=nr.detect(),wf=Cf.deviceType.isTouch(),xf=Cf.deviceType.isPhone(),Nf=["lists","autolink","autosave"],Ef=xf?{theme:"mobile"}:{},zf=function(e){var t=D(e)?e.join(" "):e,n=K(A(t)?t.split(" "):[],Xn);return V(n,function(e){return 0<e.length})},Sf=function(n,e){var r,o,i,t=(r=function(e,t){return P(n,t)},o={},i={},mr(e,function(e,t){(r(e,t)?o:i)[t]=e}),{t:o,f:i});return yf(t.t,t.f)},kf=function(e,t,n,r){var o,i,a=zf(n.forced_plugins),u=zf(r.plugins),s=e&&(o="mobile",t.sections().hasOwnProperty(o))?V(u,d(P,Nf)):u,l=(i=s,[].concat(zf(a)).concat(zf(i)));return Gt.extend(r,{plugins:l.join(" ")})},Tf=function(e,t,n,r){var o,i,a,u,s,l,c,f,d,h=Sf(["mobile"],r),m=Gt.extend(t,n,h.settings(),(f=e,d=h.settings().inline,f&&!d?(u="mobile",s=Ef,l=h.sections(),c=l.hasOwnProperty(u)?l[u]:{},Gt.extend({},s,c)):{}),{validate:!0,external_plugins:(o=n,i=h.settings(),a=i.external_plugins?i.external_plugins:{},o&&o.external_plugins?Gt.extend({},o.external_plugins,a):a)});return kf(e,h,n,m)},Af=function(e,t,n){return T.from(t.settings[n]).filter(e)},Rf=d(Af,A),Df=function(e,t,n,r){var o,i,a,u=t in e.settings?e.settings[t]:n;return"hash"===r?(a={},"string"==typeof(i=u)?L(0<i.indexOf("=")?i.split(/[;,](?![^=;,]*(?:[;,]|$))/):i.split(","),function(e){var t=e.split("=");1<t.length?a[Gt.trim(t[0])]=Gt.trim(t[1]):a[Gt.trim(t[0])]=Gt.trim(t)}):a=i,a):"string"===r?Af(A,e,t).getOr(n):"number"===r?Af(O,e,t).getOr(n):"boolean"===r?Af(B,e,t).getOr(n):"object"===r?Af(R,e,t).getOr(n):"array"===r?Af(D,e,t).getOr(n):"string[]"===r?Af((o=A,function(e){return D(e)&&Q(e,o)}),e,t).getOr(n):"function"===r?Af(_,e,t).getOr(n):u},Mf=function(e,t){return t.dom()[e]},Bf=function(e,t){return parseInt(Nr(t,e),10)},_f=d(Mf,"clientWidth"),Of=d(Mf,"clientHeight"),Hf=d(Bf,"margin-top"),Pf=d(Bf,"margin-left"),Lf=function(e,t,n){var r,o,i,a,u,s,l,c,f,d,h,m=or.fromDom(e.getBody()),g=e.inline?m:(r=m,or.fromDom(r.dom().ownerDocument.documentElement)),p=(o=e.inline,a=t,u=n,s=(i=g).dom().getBoundingClientRect(),{x:a-(o?s.left+i.dom().clientLeft+Pf(i):0),y:u-(o?s.top+i.dom().clientTop+Hf(i):0)});return c=p.x,f=p.y,d=_f(l=g),h=Of(l),0<=c&&0<=f&&c<=d&&f<=h},Vf=function(e){var t,n=e.inline?e.getBody():e.getContentAreaContainer();return(t=n,T.from(t).map(or.fromDom)).map(function(e){return Hr(Pr(e),e)}).getOr(!1)};function If(n){var t,o=[],i=function(){var e=n.theme;return e&&e.getNotificationManagerImpl?e.getNotificationManagerImpl():function t(){var e=function(){throw new Error("Theme did not provide a NotificationManager implementation.")};return{open:e,close:e,reposition:e,getArgs:e}}()},a=function(){0<o.length&&i().reposition(o)},u=function(t){U(o,function(e){return e===t}).each(function(e){o.splice(e,1)})},r=function(r){if(!n.removed&&Vf(n))return F(o,function(e){return t=i().getArgs(e),n=r,!(t.type!==n.type||t.text!==n.text||t.progressBar||t.timeout||n.progressBar||n.timeout);var t,n}).getOrThunk(function(){n.editorManager.setActive(n);var e,t=i().open(r,function(){u(t),a()});return e=t,o.push(e),a(),t})};return(t=n).on("SkinLoaded",function(){var e=t.settings.service_message;e&&r({text:e,type:"warning",timeout:0})}),t.on("ResizeEditor ResizeWindow NodeChange",function(){be.requestAnimationFrame(a)}),t.on("remove",function(){L(o.slice(),function(e){i().close(e)})}),{open:r,close:function(){T.from(o[0]).each(function(e){i().close(e),u(e),a()})},getNotifications:function(){return o}}}function Ff(r){var n=[],o=function(){var e=r.theme;return e&&e.getWindowManagerImpl?e.getWindowManagerImpl():function t(){var e=function(){throw new Error("Theme did not provide a WindowManager implementation.")};return{open:e,alert:e,confirm:e,close:e,getParams:e,setParams:e}}()},i=function(e,t){return function(){return t?t.apply(e,arguments):undefined}},a=function(e){var t;n.push(e),t=e,r.fire("OpenWindow",{dialog:t})},u=function(t){var e;e=t,r.fire("CloseWindow",{dialog:e}),0===(n=V(n,function(e){return e!==t})).length&&r.focus()};return r.on("remove",function(){L(n,function(e){o().close(e)})}),{open:function(e,t){r.editorManager.setActive(r),Kc(r);var n=o().open(e,t,u);return a(n),n},alert:function(e,t,n){o().alert(e,i(n||this,t))},confirm:function(e,t,n){o().confirm(e,i(n||this,t))},close:function(){T.from(n[n.length-1]).each(function(e){o().close(e),u(e)})}}}var Uf,jf=Ai.PluginManager,qf=function(e,t){var n=function(e,t){for(var n in jf.urls)if(jf.urls[n]+"/plugin"+t+".js"===e)return n;return null}(t,e.suffix);return n?ki.translate(["Failed to load plugin: {0} from url {1}",n,t]):ki.translate(["Failed to load plugin url: {0}",t])},$f=function(e,t){e.notificationManager.open({type:"error",text:t})},Wf=function(e,t){e._skinLoaded?$f(e,t):e.on("SkinLoaded",function(){$f(e,t)})},Kf=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=j.window.console;r&&(r.error?r.error.apply(r,arguments):r.log.apply(r,arguments))},Xf={pluginLoadError:function(e,t){Wf(e,qf(e,t))},pluginInitError:function(e,t,n){var r=ki.translate(["Failed to initialize plugin: {0}",t]);Kf(r,n),Wf(e,r)},uploadError:function(e,t){Wf(e,ki.translate(["Failed to upload image: {0}",t]))},displayError:Wf,initError:Kf},Yf=(Uf={},{add:function(e,t){Uf[e]=t},get:function(e){return Uf[e]?Uf[e]:{icons:{}}}}),Gf=Ai.PluginManager,Jf=Ai.ThemeManager;function Qf(){return new(ae.getOrDie("XMLHttpRequest"))}function Zf(u,s){var r={},n=function(e,r,o,t){var i,n;(i=Qf()).open("POST",s.url),i.withCredentials=s.credentials,i.upload.onprogress=function(e){t(e.loaded/e.total*100)},i.onerror=function(){o("Image upload failed due to a XHR Transport error. Code: "+i.status)},i.onload=function(){var e,t,n;i.status<200||300<=i.status?o("HTTP Error: "+i.status):(e=JSON.parse(i.responseText))&&"string"==typeof e.location?r((t=s.basePath,n=e.location,t?t.replace(/\/$/,"")+"/"+n.replace(/^\//,""):n)):o("Invalid JSON: "+i.responseText)},(n=new j.FormData).append("file",e.blob(),e.filename()),i.send(n)},l=function(e,t){return{url:t,blobInfo:e,status:!0}},c=function(e,t){return{url:"",blobInfo:e,status:!1,error:t}},f=function(e,t){Gt.each(r[e],function(e){e(t)}),delete r[e]},o=function(e,n){return e=Gt.grep(e,function(e){return!u.isUploaded(e.blobUri())}),me.all(Gt.map(e,function(e){return u.isPending(e.blobUri())?(t=e.blobUri(),new me(function(e){r[t]=r[t]||[],r[t].push(e)})):(o=e,i=s.handler,a=n,u.markPending(o.blobUri()),new me(function(t){var n;try{var r=function(){n&&n.close()};i(o,function(e){r(),u.markUploaded(o.blobUri(),e),f(o.blobUri(),l(o,e)),t(l(o,e))},function(e){r(),u.removeFailed(o.blobUri()),f(o.blobUri(),c(o,e)),t(c(o,e))},function(e){e<0||100<e||(n||(n=a()),n.progressBar.value(e))})}catch(e){t(c(o,e.message))}}));var o,i,a,t}))};return!1===_(s.handler)&&(s.handler=n),{upload:function(e,t){return s.url||s.handler!==n?o(e,t):new me(function(e){e([])})}}}var ed=function(e){return ae.getOrDie("atob")(e)},td=function(e){var t,n,r=decodeURIComponent(e).split(",");return(n=/data:([^;]+)/.exec(r[0]))&&(t=n[1]),{type:t,data:r[1]}},nd=function(a){return new me(function(e){var t,n,r,o=td(a);try{t=ed(o.data)}catch($N){return void e(new j.Blob([]))}for(n=function i(e){return new(ae.getOrDie("Uint8Array"))(e)}(t.length),r=0;r<n.length;r++)n[r]=t.charCodeAt(r);e(new j.Blob([n],{type:o.type}))})},rd=function(e){return 0===e.indexOf("blob:")?(i=e,new me(function(e,t){var n=function(){t("Cannot convert "+i+" to Blob. Resource might not exist or is inaccessible.")};try{var r=Qf();r.open("GET",i,!0),r.responseType="blob",r.onload=function(){200===this.status?e(this.response):n()},r.onerror=n,r.send()}catch(o){n()}})):0===e.indexOf("data:")?nd(e):null;var i},od=function(r){return new me(function(e){var t=function n(){return new(ae.getOrDie("FileReader"))}();t.onloadend=function(){e(t.result)},t.readAsDataURL(r)})},id=td,ad=0,ud=function(e){return(e||"blobid")+ad++},sd=function(n,r,o,t){var i,a;0!==r.src.indexOf("blob:")?(i=id(r.src).data,(a=n.findFirst(function(e){return e.base64()===i}))?o({image:r,blobInfo:a}):rd(r.src).then(function(e){a=n.create(ud(),e,i),n.add(a),o({image:r,blobInfo:a})},function(e){t(e)})):(a=n.getByUri(r.src))?o({image:r,blobInfo:a}):rd(r.src).then(function(t){od(t).then(function(e){i=id(e).data,a=n.create(ud(),t,i),n.add(a),o({image:r,blobInfo:a})})},function(e){t(e)})},ld=function(e){return e?re(e.getElementsByTagName("img")):[]},cd=0,fd={uuid:function(e){return e+cd+++(t=function(){return Math.round(4294967295*Math.random()).toString(36)},"s"+(new Date).getTime().toString(36)+t()+t()+t());var t}};function dd(o){var t,n,i=function v(){var n=[],o=function(e){var t,n,r;if(!e.blob||!e.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");return t=e.id||fd.uuid("blobid"),n=e.name||t,{id:$(t),name:$(n),filename:$(n+"."+(r=e.blob.type,{"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png"}[r.toLowerCase()]||"dat")),blob:$(e.blob),base64:$(e.base64),blobUri:$(e.blobUri||se.createObjectURL(e.blob)),uri:$(e.uri)}},t=function(t){return e(function(e){return e.id()===t})},e=function(e){return V(n,e)[0]};return{create:function(e,t,n,r){if(A(e))return o({id:e,name:r,blob:t,base64:n});if(R(e))return o(e);throw new Error("Unknown input type")},add:function(e){t(e.id())||n.push(e)},get:t,getByUri:function(t){return e(function(e){return e.blobUri()===t})},findFirst:e,removeByUri:function(t){n=V(n,function(e){return e.blobUri()!==t||(se.revokeObjectURL(e.blobUri()),!1)})},destroy:function(){L(n,function(e){se.revokeObjectURL(e.blobUri())}),n=[]}}}(),a=function b(){var n={},r=function(e,t){return{status:e,resultUri:t}},t=function(e){return e in n};return{hasBlobUri:t,getResultUri:function(e){var t=n[e];return t?t.resultUri:null},isPending:function(e){return!!t(e)&&1===n[e].status},isUploaded:function(e){return!!t(e)&&2===n[e].status},markPending:function(e){n[e]=r(1,null)},markUploaded:function(e,t){n[e]=r(2,t)},removeFailed:function(e){delete n[e]},destroy:function(){n={}}}}(),r=[],u=function(t){return function(e){return o.selection?t(e):[]}},s=function(e,t,n){for(var r=0;-1!==(r=e.indexOf(t,r))&&(e=e.substring(0,r)+n+e.substr(r+t.length),r+=n.length-t.length+1),-1!==r;);return e},l=function(e,t,n){return e=s(e,'src="'+t+'"','src="'+n+'"'),e=s(e,'data-mce-src="'+t+'"','data-mce-src="'+n+'"')},c=function(t,n){L(o.undoManager.data,function(e){"fragmented"===e.type?e.fragments=K(e.fragments,function(e){return l(e,t,n)}):e.content=l(e.content,t,n)})},f=function(){return o.notificationManager.open({text:o.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0})},d=function(e,t){i.removeByUri(e.src),c(e.src,t),o.$(e).attr({src:dc(o)?t+"?"+(new Date).getTime():t,"data-mce-src":o.convertURL(t,"src")})},h=function(n){return t||(t=Zf(a,{url:mc(o),basePath:gc(o),credentials:pc(o),handler:vc(o)})),g().then(u(function(r){var e;return e=K(r,function(e){return e.blobInfo}),t.upload(e,f).then(u(function(e){var t=K(e,function(e,t){var n=r[t].image;return e.status&&hc(o)?d(n,e.url):e.error&&Xf.uploadError(o,e.error),{element:n,status:e.status}});return n&&n(t),t}))}))},e=function(e){if(fc(o))return h(e)},m=function(t){return!1!==Q(r,function(e){return e(t)})&&(0!==t.getAttribute("src").indexOf("data:")||cc(o)(t))},g=function(){return n||(n=function e(o,i){var a={};return{findAll:function(e,n){var t;n||(n=$(!0)),t=V(ld(e),function(e){var t=e.src;return!!he.fileApi&&!e.hasAttribute("data-mce-bogus")&&!e.hasAttribute("data-mce-placeholder")&&!(!t||t===he.transparentSrc)&&(0===t.indexOf("blob:")?!o.isUploaded(t)&&n(e):0===t.indexOf("data:")&&n(e))});var r=K(t,function(n){if(a[n.src])return new me(function(t){a[n.src].then(function(e){if("string"==typeof e)return e;t({image:n,blobInfo:e.blobInfo})})});var e=new me(function(e,t){sd(i,n,e,t)}).then(function(e){return delete a[e.image.src],e})["catch"](function(e){return delete a[n.src],e});return a[n.src]=e});return me.all(r)}}}(a,i)),n.findAll(o.getBody(),m).then(u(function(e){return e=V(e,function(e){return"string"!=typeof e||(Xf.displayError(o,e),!1)}),L(e,function(e){c(e.image.src,e.blobInfo.blobUri()),e.image.src=e.blobInfo.blobUri(),e.image.removeAttribute("data-mce-src")}),e}))},p=function(e){return e.replace(/src="(blob:[^"]+)"/g,function(e,n){var t=a.getResultUri(n);if(t)return'src="'+t+'"';var r=i.getByUri(n);return r||(r=I(o.editorManager.get(),function(e,t){return e||t.editorUpload&&t.editorUpload.blobCache.getByUri(n)},null)),r?'src="data:'+r.blob().type+";base64,"+r.base64()+'"':e})};return o.on("setContent",function(){fc(o)?e():g()}),o.on("RawSaveContent",function(e){e.content=p(e.content)}),o.on("getContent",function(e){e.source_view||"raw"===e.format||(e.content=p(e.content))}),o.on("PostRender",function(){o.parser.addNodeFilter("img",function(e){L(e,function(e){var t=e.attr("src");if(!i.getByUri(t)){var n=a.getResultUri(t);n&&e.attr("src",n)}})})}),{blobCache:i,addFilter:function(e){r.push(e)},uploadImages:h,uploadImagesAuto:e,scanForImages:g,destroy:function(){i.destroy(),a.destroy(),n=t=null}}}var hd=function(e,t,n){return Hr(t,e)?function(e,t){for(var n=_(t)?t:$(!1),r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,a=or.fromDom(i);if(o.push(a),!0===n(a))break;r=i}return o}(e,function(e){return n(e)||Or(e,t)}).slice(0,-1):[]},md=function(e,t){return hd(e,t,$(!1))},gd=md,pd=function(e,t){return[e].concat(md(e,t))},vd=function(e,t){return e.hasOwnProperty(t.nodeName)},bd=function(t,e,n){return r=gd(or.fromDom(n),or.fromDom(e)),U(r,function(e){return vd(t,e.dom())}).isSome();var r},yd=function(e,t){if(Bo.isText(t)){if(0===t.nodeValue.length)return!0;if(/^\s+$/.test(t.nodeValue)&&(!t.nextSibling||vd(e,t.nextSibling)))return!0}return!1},Cd=function(e){var t,n,r,o,i,a,u,s,l,c,f=e.dom,d=e.selection,h=e.schema,m=h.getBlockElements(),g=d.getStart(),p=e.getBody(),v=nc(e);if(g&&Bo.isElement(g)&&v&&(c=p.nodeName.toLowerCase(),h.isValidChild(c,v.toLowerCase())&&!bd(m,p,g))){for(n=(t=d.getRng()).startContainer,r=t.startOffset,o=t.endContainer,i=t.endOffset,l=rf(e),g=p.firstChild;g;)if(b=m,y=g,Bo.isText(y)||Bo.isElement(y)&&!vd(b,y)&&!ll(y)){if(yd(m,g)){g=(u=g).nextSibling,f.remove(u);continue}a||(a=f.create(v,e.settings.forced_root_block_attrs),g.parentNode.insertBefore(a,g),s=!0),g=(u=g).nextSibling,a.appendChild(u)}else a=null,g=g.nextSibling;var b,y;s&&l&&(t.setStart(n,r),t.setEnd(o,i),d.setRng(t),e.nodeChanged())}},wd=function(e){nc(e)&&e.on("NodeChange",d(Cd,e))},xd=function(e,t){return e&&t&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset},Nd=function(t){return $r(t).fold($([t]),function(e){return[t].concat(Nd(e))})},Ed=function(t){return Wr(t).fold($([t]),function(e){return"br"===sr(e)?Vr(e).map(function(e){return[t].concat(Ed(e))}).getOr([]):[t].concat(Ed(e))})},zd=function(o,e){return Ja([(i=e,a=i.startContainer,u=i.startOffset,Bo.isText(a)?0===u?T.some(or.fromDom(a)):T.none():T.from(a.childNodes[u]).map(or.fromDom)),(t=e,n=t.endContainer,r=t.endOffset,Bo.isText(n)?r===n.data.length?T.some(or.fromDom(n)):T.none():T.from(n.childNodes[r-1]).map(or.fromDom))],function(e,t){var n=F(Nd(o),d(Or,e)),r=F(Ed(o),d(Or,t));return n.isSome()&&r.isSome()}).getOr(!1);var t,n,r,i,a,u},Sd=function(e,t,n,r){var o=n,i=new io(n,o),a=e.schema.getNonEmptyElements();do{if(3===n.nodeType&&0!==Gt.trim(n.nodeValue).length)return void(r?t.setStart(n,0):t.setEnd(n,n.nodeValue.length));if(a[n.nodeName]&&!/^(TD|TH)$/.test(n.nodeName))return void(r?t.setStartBefore(n):"BR"===n.nodeName?t.setEndBefore(n):t.setEndAfter(n));if(he.ie&&he.ie<11&&e.isBlock(n)&&e.isEmpty(n))return void(r?t.setStart(n,0):t.setEnd(n,0))}while(n=r?i.next():i.prev());"BODY"===o.nodeName&&(r?t.setStart(o,0):t.setEnd(o,o.childNodes.length))},kd=function(e){var t=e.selection.getSel();return t&&0<t.rangeCount};function Td(i){var r,o=[];"onselectionchange"in i.getDoc()||i.on("NodeChange Click MouseUp KeyUp Focus",function(e){var t,n;n={startContainer:(t=i.selection.getRng()).startContainer,startOffset:t.startOffset,endContainer:t.endContainer,endOffset:t.endOffset},"nodechange"!==e.type&&xd(n,r)||i.fire("SelectionChange"),r=n}),i.on("contextmenu",function(){i.fire("SelectionChange")}),i.on("SelectionChange",function(){var e=i.selection.getStart(!0);!e||!he.range&&i.selection.isCollapsed()||kd(i)&&!function(e){var t,n;if((n=i.$(e).parentsUntil(i.getBody()).add(e)).length===o.length){for(t=n.length;0<=t&&n[t]===o[t];t--);if(-1===t)return o=n,!0}return o=n,!1}(e)&&i.dom.isChildOf(e,i.getBody())&&i.nodeChanged({selectionChange:!0})}),i.on("MouseUp",function(e){!e.isDefaultPrevented()&&kd(i)&&("IMG"===i.selection.getNode().nodeName?be.setEditorTimeout(i,function(){i.nodeChanged()}):i.nodeChanged())}),this.nodeChanged=function(e){var t,n,r,o=i.selection;i.initialized&&o&&!i.settings.disable_nodechange&&!i.readonly&&(r=i.getBody(),(t=o.getStart(!0)||r).ownerDocument===i.getDoc()&&i.dom.isChildOf(t,r)||(t=r),n=[],i.dom.getParent(t,function(e){if(e===r)return!0;n.push(e)}),(e=e||{}).element=t,e.parents=n,i.fire("NodeChange",e))}}var Ad,Rd,Dd=function(e){var t,n,r,o;return o=e.getBoundingClientRect(),n=(t=e.ownerDocument).documentElement,r=t.defaultView,{top:o.top+r.pageYOffset-n.clientTop,left:o.left+r.pageXOffset-n.clientLeft}},Md=function(e,t){return n=(u=e).inline?Dd(u.getBody()):{left:0,top:0},a=(i=e).getBody(),r=i.inline?{left:a.scrollLeft,top:a.scrollTop}:{left:0,top:0},{pageX:(o=function(e,t){if(t.target.ownerDocument===e.getDoc())return{left:t.pageX,top:t.pageY};var n,r,o,i,a,u=Dd(e.getContentAreaContainer()),s=(r=(n=e).getBody(),o=n.getDoc().documentElement,i={left:r.scrollLeft,top:r.scrollTop},a={left:r.scrollLeft||o.scrollLeft,top:r.scrollTop||o.scrollTop},n.inline?i:a);return{left:t.pageX-u.left+s.left,top:t.pageY-u.top+s.top}}(e,t)).left-n.left+r.left,pageY:o.top-n.top+r.top};var n,r,o,i,a,u},Bd=Bo.isContentEditableFalse,_d=Bo.isContentEditableTrue,Od=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},Hd=function(u,s){return function(e){if(0===e.button){var t=F(s.dom.getParents(e.target),eu(Bd,_d)).getOr(null);if(i=s.getBody(),Bd(a=t)&&a!==i){var n=s.dom.getPos(t),r=s.getBody(),o=s.getDoc().documentElement;u.element=t,u.screenX=e.screenX,u.screenY=e.screenY,u.maxX=(s.inline?r.scrollWidth:o.offsetWidth)-2,u.maxY=(s.inline?r.scrollHeight:o.offsetHeight)-2,u.relX=e.pageX-n.x,u.relY=e.pageY-n.y,u.width=t.offsetWidth,u.height=t.offsetHeight,u.ghost=function(e,t,n,r){var o=t.cloneNode(!0);e.dom.setStyles(o,{width:n,height:r}),e.dom.setAttrib(o,"data-mce-selected",null);var i=e.dom.create("div",{"class":"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return e.dom.setStyles(i,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:r}),e.dom.setStyles(o,{margin:0,boxSizing:"border-box"}),i.appendChild(o),i}(s,t,u.width,u.height)}}var i,a}},Pd=function(c,f){return function(e){if(c.dragging&&(s=(i=f).selection,l=s.getSel().getRangeAt(0).startContainer,a=3===l.nodeType?l.parentNode:l,u=c.element,a!==u&&!i.dom.isChildOf(a,u)&&!Bd(a))){var t=(r=c.element,(o=r.cloneNode(!0)).removeAttribute("data-mce-selected"),o),n=f.fire("drop",{targetClone:t,clientX:e.clientX,clientY:e.clientY});n.isDefaultPrevented()||(t=n.targetClone,f.undoManager.transact(function(){Od(c.element),f.insertContent(f.dom.getOuterHTML(t)),f._selectionOverrides.hideFakeCaret()}))}var r,o,i,a,u,s,l;Ld(c)}},Ld=function(e){e.dragging=!1,e.element=null,Od(e.ghost)},Vd=function(e){var t,n,r,o,i,a,g,p,v,u,s,l={};t=vi.DOM,a=j.document,n=Hd(l,e),g=l,p=e,v=be.throttle(function(e,t){p._selectionOverrides.hideFakeCaret(),p.selection.placeCaretAt(e,t)},0),r=function(e){var t,n,r,o,i,a,u,s,l,c,f,d,h=Math.max(Math.abs(e.screenX-g.screenX),Math.abs(e.screenY-g.screenY));if(g.element&&!g.dragging&&10<h){if(p.fire("dragstart",{target:g.element}).isDefaultPrevented())return;g.dragging=!0,p.focus()}if(g.dragging){var m=(f=g,{pageX:(d=Md(p,e)).pageX-f.relX,pageY:d.pageY+5});l=g.ghost,c=p.getBody(),l.parentNode!==c&&c.appendChild(l),t=g.ghost,n=m,r=g.width,o=g.height,i=g.maxX,a=g.maxY,s=u=0,t.style.left=n.pageX+"px",t.style.top=n.pageY+"px",n.pageX+r>i&&(u=n.pageX+r-i),n.pageY+o>a&&(s=n.pageY+o-a),t.style.width=r-u+"px",t.style.height=o-s+"px",v(e.clientX,e.clientY)}},o=Pd(l,e),u=l,i=function(){u.dragging&&s.fire("dragend"),Ld(u)},(s=e).on("mousedown",n),e.on("mousemove",r),e.on("mouseup",o),t.bind(a,"mousemove",r),t.bind(a,"mouseup",i),e.on("remove",function(){t.unbind(a,"mousemove",r),t.unbind(a,"mouseup",i)})},Id=function(e){var n;Vd(e),(n=e).on("drop",function(e){var t="undefined"!=typeof e.clientX?n.getDoc().elementFromPoint(e.clientX,e.clientY):null;(Bd(t)||Bd(n.dom.getContentEditableParent(t)))&&e.preventDefault()})},Fd=function(e){return I(e,function(e,t){return e.concat(function(t){var e=function(e){return K(e,function(e){return(e=Ua(e)).node=t,e})};if(Bo.isElement(t))return e(t.getClientRects());if(Bo.isText(t)){var n=t.ownerDocument.createRange();return n.setStart(t,0),n.setEnd(t,t.data.length),e(n.getClientRects())}}(t))},[])};(Rd=Ad||(Ad={}))[Rd.Up=-1]="Up",Rd[Rd.Down=1]="Down";var Ud,jd=function(o,i,a,e,u,t){var n,s,l=0,c=[],r=function(e){var t,n,r;for(r=Fd([e]),-1===o&&(r=r.reverse()),t=0;t<r.length;t++)if(n=r[t],!a(n,s)){if(0<c.length&&i(n,$t.last(c))&&l++,n.line=l,u(n))return!0;c.push(n)}};return(s=$t.last(t.getClientRects()))&&(r(n=t.getNode()),function(e,t,n,r){for(;r=bs(r,e,Ia,t);)if(n(r))return}(o,e,r,n)),c},qd=d(jd,Ad.Up,$a,Wa),$d=d(jd,Ad.Down,Wa,$a),Wd=function(n){return function(e){return t=n,e.line>t;var t}},Kd=function(n){return function(e){return t=n,e.line===t;var t}},Xd=Bo.isContentEditableFalse,Yd=bs,Gd=function(e,t){return Math.abs(e.left-t)},Jd=function(e,t){return Math.abs(e.right-t)},Qd=function(e,t){return e>=t.left&&e<=t.right},Zd=function(e,o){return $t.reduce(e,function(e,t){var n,r;return n=Math.min(Gd(e,o),Jd(e,o)),r=Math.min(Gd(t,o),Jd(t,o)),Qd(o,t)?t:Qd(o,e)?e:r===n&&Xd(t.node)?t:r<n?t:e})},eh=function(e,t,n,r){for(;r=Yd(r,e,Ia,t);)if(n(r))return},th=function(e,t,n){var r,o,i,a,u,s,l,c=Fd(V(re(e.getElementsByTagName("*")),ss)),f=V(c,function(e){return n>=e.top&&n<=e.bottom});return(r=Zd(f,t))&&(r=Zd((a=e,l=function(t,e){var n;return n=V(Fd([e]),function(e){return!t(e,u)}),s=s.concat(n),0===n.length},(s=[]).push(u=r),eh(Ad.Up,a,d(l,$a),u.node),eh(Ad.Down,a,d(l,Wa),u.node),s),t))&&ss(r.node)?(i=t,{node:(o=r).node,before:Gd(o,i)<Jd(o,i)}):null},nh=function(i,a,e){return!e.collapsed&&I(e.getClientRects(),function(e,t){return e||(o=a,(r=i)>=(n=t).left&&r<=n.right&&o>=n.top&&o<=n.bottom);var n,r,o},!1)},rh=Bo.isContentEditableTrue,oh=Bo.isContentEditableFalse,ih=function(e,t,n,r,o){return t._selectionOverrides.showCaret(e,n,r,o)},ah=function(e,t){var n,r;return e.fire("BeforeObjectSelected",{target:t}).isDefaultPrevented()?null:((r=(n=t).ownerDocument.createRange()).selectNode(n),r)},uh=function(e,t,n){var r=Ss(1,e.getBody(),t),o=xu.fromRangeStart(r),i=o.getNode();if(oh(i))return ih(1,e,i,!o.isAtEnd(),!1);var a=o.getNode(!0);if(oh(a))return ih(1,e,a,!1,!1);var u=e.dom.getParent(o.getNode(),function(e){return oh(e)||rh(e)});return oh(u)?ih(1,e,u,!1,n):null},sh=function(e,t,n){if(!t||!t.collapsed)return t;var r=uh(e,t,n);return r||t},lh=function(t){var e=Li(function(){if(!t.removed&&t.getBody().contains(j.document.activeElement)&&t.selection.getRng().collapsed){var e=sh(t,t.selection.getRng(),!1);t.selection.setRng(e)}},0);t.on("focus",function(){e.throttle()}),t.on("blur",function(){e.cancel()})},ch={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,modifierPressed:function(e){return e.shiftKey||e.ctrlKey||e.altKey||this.metaKeyPressed(e)},metaKeyPressed:function(e){return he.mac?e.metaKey:e.ctrlKey&&!e.altKey}},fh=(Ud="\xa0",function(e){return Ud===e}),dh=function(e){return/^[\r\n\t ]$/.test(e)},hh=function(e){return!dh(e)&&!fh(e)},mh=function(n,r,o){return T.from(o.container()).filter(Bo.isText).exists(function(e){var t=n?0:-1;return r(e.data.charAt(o.offset()+t))})},gh=d(mh,!0,dh),ph=d(mh,!1,dh),vh=function(e){var t=e.container();return Bo.isText(t)&&0===t.data.length},bh=function(e,t){var n=ws(e,t);return Bo.isContentEditableFalse(n)&&!Bo.isBogusAll(n)},yh=d(bh,0),Ch=d(bh,-1),wh=function(e,t){return Bo.isTable(ws(e,t))},xh=d(wh,0),Nh=d(wh,-1),Eh=Bo.isContentEditableTrue,zh=Bo.isContentEditableFalse,Sh=function(e,t){for(var n=e.getBody();t&&t!==n;){if(Eh(t)||zh(t))return t;t=t.parentNode}return null},kh=function(m){var g,a=m.getBody(),o=as(m.getBody(),function(e){return m.dom.isBlock(e)},function(){return rf(m)}),p="sel-"+m.dom.uniqueId(),u=function(e){e&&m.selection.setRng(e)},s=function(){return m.selection.getRng()},v=function(e,t,n,r){return void 0===r&&(r=!0),m.fire("ShowCaret",{target:t,direction:e,before:n}).isDefaultPrevented()?null:(r&&m.selection.scrollIntoView(t,-1===e),o.show(n,t))},b=function(e,t){return t=Ss(e,a,t),-1===e?xu.fromRangeStart(t):xu.fromRangeEnd(t)},t=function(e){return xa(e)||ka(e)||Ta(e)},y=function(e){return t(e.startContainer)||t(e.endContainer)},l=function(e,t){var n,r,o,i,a,u,s,l,c,f,d=m.$,h=m.dom;if(!e)return null;if(e.collapsed){if(!y(e))if(!1===t){if(l=b(-1,e),ss(l.getNode(!0)))return v(-1,l.getNode(!0),!1,!1);if(ss(l.getNode()))return v(-1,l.getNode(),!l.isAtEnd(),!1)}else{if(l=b(1,e),ss(l.getNode()))return v(1,l.getNode(),!l.isAtEnd(),!1);if(ss(l.getNode(!0)))return v(1,l.getNode(!0),!1,!1)}return null}return i=e.startContainer,a=e.startOffset,u=e.endOffset,3===i.nodeType&&0===a&&zh(i.parentNode)&&(i=i.parentNode,a=h.nodeIndex(i),i=i.parentNode),1!==i.nodeType?null:(u===a+1&&(n=i.childNodes[a]),zh(n)?(c=f=n.cloneNode(!0),(s=m.fire("ObjectSelected",{target:n,targetClone:c})).isDefaultPrevented()?null:(r=Zi(or.fromDom(m.getBody()),"#"+p).fold(function(){return d([])},function(e){return d([e.dom()])}),c=s.targetClone,0===r.length&&(r=d('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>').attr("id",p)).appendTo(m.getBody()),e=m.dom.createRng(),c===f&&he.ie?(r.empty().append('<p style="font-size: 0" data-mce-bogus="all">\xa0</p>').append(c),e.setStartAfter(r[0].firstChild.firstChild),e.setEndAfter(c)):(r.empty().append("\xa0").append(c).append("\xa0"),e.setStart(r[0].firstChild,1),e.setEnd(r[0].lastChild,0)),r.css({top:h.getPos(n,m.getBody()).y}),r[0].focus(),(o=m.selection.getSel()).removeAllRanges(),o.addRange(e),L(Ki(or.fromDom(m.getBody()),"*[data-mce-selected]"),function(e){xr(e,"data-mce-selected")}),n.setAttribute("data-mce-selected","1"),g=n,C(),e)):null)},c=function(){g&&(g.removeAttribute("data-mce-selected"),Zi(or.fromDom(m.getBody()),"#"+p).each(Hi),g=null),Zi(or.fromDom(m.getBody()),"#"+p).each(Hi),g=null},C=function(){o.hide()};return he.ceFalse&&function(){m.on("mouseup",function(e){var t=s();t.collapsed&&Lf(m,e.clientX,e.clientY)&&u(uh(m,t,!1))}),m.on("click",function(e){var t;(t=Sh(m,e.target))&&(zh(t)&&(e.preventDefault(),m.focus()),Eh(t)&&m.dom.isChildOf(t,m.selection.getNode())&&c())}),m.on("blur NewBlock",function(){c()}),m.on("ResizeWindow FullscreenStateChanged",function(){return o.reposition()});var n,r,i=function(e,t){var n,r,o=m.dom.getParent(e,m.dom.isBlock),i=m.dom.getParent(t,m.dom.isBlock);return!(!o||!m.dom.isChildOf(o,i)||!1!==zh(Sh(m,o)))||o&&(n=o,r=i,!(m.dom.getParent(n,m.dom.isBlock)===m.dom.getParent(r,m.dom.isBlock)))&&function(e){var t=Us(e);if(!e.firstChild)return!1;var n=xu.before(e.firstChild),r=t.next(n);return r&&!yh(r)&&!Ch(r)}(o)};r=!1,(n=m).on("touchstart",function(){r=!1}),n.on("touchmove",function(){r=!0}),n.on("touchend",function(e){var t=Sh(n,e.target);zh(t)&&(r||(e.preventDefault(),l(ah(n,t))))}),m.on("mousedown",function(e){var t,n=e.target;if((n===a||"HTML"===n.nodeName||m.dom.isChildOf(n,a))&&!1!==Lf(m,e.clientX,e.clientY))if(t=Sh(m,n))zh(t)?(e.preventDefault(),l(ah(m,t))):(c(),Eh(t)&&e.shiftKey||nh(e.clientX,e.clientY,m.selection.getRng())||(C(),m.selection.placeCaretAt(e.clientX,e.clientY)));else if(!1===ss(n)){c(),C();var r=th(a,e.clientX,e.clientY);if(r&&!i(e.target,r.node)){e.preventDefault();var o=v(1,r.node,r.before,!1);m.getBody().focus(),u(o)}}}),m.on("keypress",function(e){ch.modifierPressed(e)||(e.keyCode,zh(m.selection.getNode())&&e.preventDefault())}),m.on("getSelectionRange",function(e){var t=e.range;if(g){if(!g.parentNode)return void(g=null);(t=t.cloneRange()).selectNode(g),e.range=t}}),m.on("setSelectionRange",function(e){var t;(t=l(e.range,e.forward))&&(e.range=t)}),m.on("AfterSetSelectionRange",function(e){var t,n=e.range;y(n)||"mcepastebin"===n.startContainer.parentNode.id||C(),t=n.startContainer.parentNode,m.dom.hasClass(t,"mce-offscreen-selection")||c()}),m.on("copy",function(e){var t,n=e.clipboardData;if(!e.isDefaultPrevented()&&e.clipboardData&&!he.ie){var r=(t=m.dom.get(p))?t.getElementsByTagName("*")[0]:t;r&&(e.preventDefault(),n.clearData(),n.setData("text/html",r.outerHTML),n.setData("text/plain",r.outerText))}}),Id(m),lh(m)}(),{showCaret:v,showBlockCaretContainer:function(e){e.hasAttribute("data-mce-caret")&&(Aa(e),u(s()),m.selection.scrollIntoView(e[0]))},hideFakeCaret:C,destroy:function(){o.destroy(),g=null}}},Th=0,Ah=2,Rh=1,Dh=function(m,g){var e=m.length+g.length+2,p=new Array(e),v=new Array(e),l=function(e,t,n,r,o){var i=c(e,t,n,r);if(null===i||i.start===t&&i.diag===t-r||i.end===e&&i.diag===e-n)for(var a=e,u=n;a<t||u<r;)a<t&&u<r&&m[a]===g[u]?(o.push([0,m[a]]),++a,++u):r-n<t-e?(o.push([2,m[a]]),++a):(o.push([1,g[u]]),++u);else{l(e,i.start,n,i.start-i.diag,o);for(var s=i.start;s<i.end;++s)o.push([0,m[s]]);l(i.end,t,i.end-i.diag,r,o)}},b=function(e,t,n,r){for(var o=e;o-t<r&&o<n&&m[o]===g[o-t];)++o;return{start:e,end:o,diag:t}},c=function(e,t,n,r){var o=t-e,i=r-n;if(0===o||0===i)return null;var a,u,s,l,c,f=o-i,d=i+o,h=(d%2==0?d:d+1)/2;for(p[1+h]=e,v[1+h]=t+1,a=0;a<=h;++a){for(u=-a;u<=a;u+=2){for(s=u+h,u===-a||u!==a&&p[s-1]<p[s+1]?p[s]=p[s+1]:p[s]=p[s-1]+1,c=(l=p[s])-e+n-u;l<t&&c<r&&m[l]===g[c];)p[s]=++l,++c;if(f%2!=0&&f-a<=u&&u<=f+a&&v[s-f]<=p[s])return b(v[s-f],u+e-n,t,r)}for(u=f-a;u<=f+a;u+=2){for(s=u+h-f,u===f-a||u!==f+a&&v[s+1]<=v[s-1]?v[s]=v[s+1]-1:v[s]=v[s-1],c=(l=v[s]-1)-e+n-u;e<=l&&n<=c&&m[l]===g[c];)v[s]=l--,c--;if(f%2==0&&-a<=u&&u<=a&&v[s]<=p[s+f])return b(v[s],u+e-n,t,r)}}},t=[];return l(0,m.length,0,g.length,t),t},Mh=function(e){return Bo.isElement(e)?e.outerHTML:Bo.isText(e)?Ko.encodeRaw(e.data,!1):Bo.isComment(e)?"\x3c!--"+e.data+"--\x3e":""},Bh=function(e,t,n){var r=function(e){var t,n,r;for(r=j.document.createElement("div"),t=j.document.createDocumentFragment(),e&&(r.innerHTML=e);n=r.firstChild;)t.appendChild(n);return t}(t);if(e.hasChildNodes()&&n<e.childNodes.length){var o=e.childNodes[n];o.parentNode.insertBefore(r,o)}else e.appendChild(r)},_h=function(e){return V(K(re(e.childNodes),Mh),function(e){return 0<e.length})},Oh=function(e,t){var n,r,o,i=K(re(t.childNodes),Mh);return n=Dh(i,e),r=t,o=0,L(n,function(e){e[0]===Th?o++:e[0]===Rh?(Bh(r,e[1],o),o++):e[0]===Ah&&function(e,t){if(e.hasChildNodes()&&t<e.childNodes.length){var n=e.childNodes[t];n.parentNode.removeChild(n)}}(r,o)}),t},Hh=Ei(T.none()),Ph=function(e){return{type:"fragmented",fragments:e,content:"",bookmark:null,beforeBookmark:null}},Lh=function(e){return{type:"complete",fragments:null,content:e,bookmark:null,beforeBookmark:null}},Vh=function(e){return"fragmented"===e.type?e.fragments.join(""):e.content},Ih=function(e){var t=or.fromTag("body",Hh.get().getOrThunk(function(){var e=j.document.implementation.createHTMLDocument("undo");return Hh.set(T.some(e)),e}));return ha(t,Vh(e)),L(Ki(t,"*[data-mce-bogus]"),Pi),t.dom().innerHTML},Fh=function(n){var e,t,r;return e=_h(n.getBody()),-1!==(t=(r=J(e,function(e){var t=Kl.trimInternal(n.serializer,e);return 0<t.length?[t]:[]})).join("")).indexOf("</iframe>")?Ph(r):Lh(t)},Uh=function(e,t,n){"fragmented"===t.type?Oh(t.fragments,e.getBody()):e.setContent(t.content,{format:"raw"}),e.selection.moveToBookmark(n?t.beforeBookmark:t.bookmark)},jh=function(e,t){return!(!e||!t)&&(r=t,Vh(e)===Vh(r)||(n=t,Ih(e)===Ih(n)));var n,r};function qh(u){var s,r,o=this,l=0,c=[],t=0,f=function(){return 0===t},i=function(e){f()&&(o.typing=e)},d=function(e){u.setDirty(e)},a=function(e){i(!1),o.add({},e)},n=function(){o.typing&&(i(!1),o.add())};return u.on("init",function(){o.add()}),u.on("BeforeExecCommand",function(e){var t=e.command;"Undo"!==t&&"Redo"!==t&&"mceRepaint"!==t&&(n(),o.beforeChange())}),u.on("ExecCommand",function(e){var t=e.command;"Undo"!==t&&"Redo"!==t&&"mceRepaint"!==t&&a(e)}),u.on("ObjectResizeStart Cut",function(){o.beforeChange()}),u.on("SaveContent ObjectResized blur",a),u.on("DragEnd",a),u.on("KeyUp",function(e){var t=e.keyCode;e.isDefaultPrevented()||((33<=t&&t<=36||37<=t&&t<=40||45===t||e.ctrlKey)&&(a(),u.nodeChanged()),46!==t&&8!==t||u.nodeChanged(),r&&o.typing&&!1===jh(Fh(u),c[0])&&(!1===u.isDirty()&&(d(!0),u.fire("change",{level:c[0],lastLevel:null})),u.fire("TypingUndo"),r=!1,u.nodeChanged()))}),u.on("KeyDown",function(e){var t=e.keyCode;if(!e.isDefaultPrevented())if(33<=t&&t<=36||37<=t&&t<=40||45===t)o.typing&&a(e);else{var n=e.ctrlKey&&!e.altKey||e.metaKey;!(t<16||20<t)||224===t||91===t||o.typing||n||(o.beforeChange(),i(!0),o.add({},e),r=!0)}}),u.on("MouseDown",function(e){o.typing&&a(e)}),u.on("input",function(e){var t;e.inputType&&("insertReplacementText"===e.inputType||"insertText"===(t=e).inputType&&null===t.data)&&a(e)}),u.addShortcut("meta+z","","Undo"),u.addShortcut("meta+y,meta+shift+z","","Redo"),u.on("AddUndo Undo Redo ClearUndos",function(e){e.isDefaultPrevented()||u.nodeChanged()}),o={data:c,typing:!1,beforeChange:function(){f()&&(s=ju.getUndoBookmark(u.selection))},add:function(e,t){var n,r,o,i=u.settings;if(o=Fh(u),e=e||{},e=Gt.extend(e,o),!1===f()||u.removed)return null;if(r=c[l],u.fire("BeforeAddUndo",{level:e,lastLevel:r,originalEvent:t}).isDefaultPrevented())return null;if(r&&jh(r,e))return null;if(c[l]&&(c[l].beforeBookmark=s),i.custom_undo_redo_levels&&c.length>i.custom_undo_redo_levels){for(n=0;n<c.length-1;n++)c[n]=c[n+1];c.length--,l=c.length}e.bookmark=ju.getUndoBookmark(u.selection),l<c.length-1&&(c.length=l+1),c.push(e),l=c.length-1;var a={level:e,lastLevel:r,originalEvent:t};return u.fire("AddUndo",a),0<l&&(d(!0),u.fire("change",a)),e},undo:function(){var e;return o.typing&&(o.add(),o.typing=!1,i(!1)),0<l&&(e=c[--l],Uh(u,e,!0),d(!0),u.fire("undo",{level:e})),e},redo:function(){var e;return l<c.length-1&&(e=c[++l],Uh(u,e,!1),d(!0),u.fire("redo",{level:e})),e},clear:function(){c=[],l=0,o.typing=!1,o.data=c,u.fire("ClearUndos")},hasUndo:function(){return 0<l||o.typing&&c[0]&&!jh(Fh(u),c[0])},hasRedo:function(){return l<c.length-1&&!o.typing},transact:function(e){return n(),o.beforeChange(),o.ignore(e),o.add()},ignore:function(e){try{t++,e()}finally{t--}},extra:function(e,t){var n,r;o.transact(e)&&(r=c[l].bookmark,n=c[l-1],Uh(u,n,!0),o.transact(t)&&(c[l-1].beforeBookmark=r))}}}var $h,Wh,Kh=function(e){var t=Ki(e,"br"),n=V(function(e){for(var t=[],n=e.dom();n;)t.push(or.fromDom(n)),n=n.lastChild;return t}(e).slice(-1),mo);t.length===n.length&&L(n,Hi)},Xh=function(e){Oi(e),Bi(e,or.fromHtml('<br data-mce-bogus="1">'))},Yh=function(n){Wr(n).each(function(t){Vr(t).each(function(e){fo(n)&&mo(t)&&fo(e)&&Hi(t)})})},Gh=hl.isEq,Jh=function(e,t,n){var r=e.formatter.get(n);if(r)for(var o=0;o<r.length;o++)if(!1===r[o].inherit&&e.dom.is(t,r[o].selector))return!0;return!1},Qh=function(t,e,n,r){var o=t.dom.getRoot();return e!==o&&(e=t.dom.getParent(e,function(e){return!!Jh(t,e,n)||e.parentNode===o||!!tm(t,e,n,r,!0)}),tm(t,e,n,r))},Zh=function(e,t,n){return!!Gh(t,n.inline)||!!Gh(t,n.block)||(n.selector?1===t.nodeType&&e.is(t,n.selector):void 0)},em=function(e,t,n,r,o,i){var a,u,s,l=n[r];if(n.onmatch)return n.onmatch(t,n,r);if(l)if("undefined"==typeof l.length){for(a in l)if(l.hasOwnProperty(a)){if(u="attributes"===r?e.getAttrib(t,a):hl.getStyle(e,t,a),o&&!u&&!n.exact)return;if((!o||n.exact)&&!Gh(u,hl.normalizeStyleValue(e,hl.replaceVars(l[a],i),a)))return}}else for(s=0;s<l.length;s++)if("attributes"===r?e.getAttrib(t,l[s]):hl.getStyle(e,t,l[s]))return n;return n},tm=function(e,t,n,r,o){var i,a,u,s,l=e.formatter.get(n),c=e.dom;if(l&&t)for(a=0;a<l.length;a++)if(i=l[a],Zh(e.dom,t,i)&&em(c,t,i,"attributes",o,r)&&em(c,t,i,"styles",o,r)){if(s=i.classes)for(u=0;u<s.length;u++)if(!e.dom.hasClass(t,s[u]))return;return i}},nm={matchNode:tm,matchName:Zh,match:function(e,t,n,r){var o;return r?Qh(e,r,t,n):(r=e.selection.getNode(),!!Qh(e,r,t,n)||!((o=e.selection.getStart())===r||!Qh(e,o,t,n)))},matchAll:function(r,o,i){var e,a=[],u={};return e=r.selection.getStart(),r.dom.getParent(e,function(e){var t,n;for(t=0;t<o.length;t++)n=o[t],!u[n]&&tm(r,e,n,i)&&(u[n]=!0,a.push(n))},r.dom.getRoot()),a},canApply:function(e,t){var n,r,o,i,a,u=e.formatter.get(t),s=e.dom;if(u)for(n=e.selection.getStart(),r=hl.getParents(s,n),i=u.length-1;0<=i;i--){if(!(a=u[i].selector)||u[i].defaultBlock)return!0;for(o=r.length-1;0<=o;o--)if(s.is(r[o],a))return!0}return!1},matchesUnInheritedFormatSelector:Jh},rm=function(e,t){return e.splitText(t)},om=function(e){var t=e.startContainer,n=e.startOffset,r=e.endContainer,o=e.endOffset;return t===r&&Bo.isText(t)?0<n&&n<t.nodeValue.length&&(t=(r=rm(t,n)).previousSibling,n<o?(t=r=rm(r,o-=n).previousSibling,o=r.nodeValue.length,n=0):o=0):(Bo.isText(t)&&0<n&&n<t.nodeValue.length&&(t=rm(t,n),n=0),Bo.isText(r)&&0<o&&o<r.nodeValue.length&&(o=(r=rm(r,o).previousSibling).nodeValue.length)),{startContainer:t,startOffset:n,endContainer:r,endOffset:o}},im=function(e,t,n){if(0!==n){var r,o,i,a=e.data.slice(t,t+n),u=t+n>=e.data.length,s=0===t;e.replaceData(t,n,(o=s,i=u,I((r=a).split(""),function(e,t){return-1!==" \f\n\r\t\x0B".indexOf(t)||"\xa0"===t?e.previousCharIsSpace||""===e.str&&o||e.str.length===r.length-1&&i?{previousCharIsSpace:!1,str:e.str+"\xa0"}:{previousCharIsSpace:!0,str:e.str+" "}:{previousCharIsSpace:!1,str:e.str+t}},{previousCharIsSpace:!1,str:""}).str))}},am=function(e,t){var n,r=e.data.slice(t),o=r.length-(n=r,n.replace(/^\s+/g,"")).length;return im(e,t,o)},um=function(e,t){var n,r,o,i=or.fromDom(e),a=or.fromDom(t);return n=a,r="pre,code",o=d(Or,i),Qi(n,r,o).isSome()},sm=function(e,t){return La(t)&&!1===(r=e,o=t,Bo.isText(o)&&/^[ \t\r\n]*$/.test(o.data)&&!1===um(r,o))||(n=t,Bo.isElement(n)&&"A"===n.nodeName&&n.hasAttribute("name"))||lm(t);var n,r,o},lm=Bo.hasAttribute("data-mce-bookmark"),cm=Bo.hasAttribute("data-mce-bogus"),fm=Bo.hasAttributeValue("data-mce-bogus","all"),dm=function(e){return function(e){var t,n,r=0;if(sm(e,e))return!1;if(!(n=e.firstChild))return!0;t=new io(n,e);do{if(fm(n))n=t.next(!0);else if(cm(n))n=t.next();else if(Bo.isBr(n))r++,n=t.next();else{if(sm(e,n))return!1;n=t.next()}}while(n);return r<=1}(e.dom())},hm=function(e,t){return r=e,o=(n=t).container(),i=n.offset(),!1===xu.isTextPosition(n)&&o===r.parentNode&&i>xu.before(r).offset()?xu(t.container(),t.offset()-1):t;var n,r,o,i},mm=function(e){return La(e.previousSibling)?T.some((t=e.previousSibling,Bo.isText(t)?xu(t,t.data.length):xu.after(t))):e.previousSibling?Zs.lastPositionIn(e.previousSibling):T.none();var t},gm=function(e){return La(e.nextSibling)?T.some((t=e.nextSibling,Bo.isText(t)?xu(t,0):xu.before(t))):e.nextSibling?Zs.firstPositionIn(e.nextSibling):T.none();var t},pm=function(r,o){return mm(o).orThunk(function(){return gm(o)}).orThunk(function(){return e=r,t=o,n=xu.before(t.previousSibling?t.previousSibling:t.parentNode),Zs.prevPosition(e,n).fold(function(){return Zs.nextPosition(e,xu.after(t))},T.some);var e,t,n})},vm=function(n,r){return gm(r).orThunk(function(){return mm(r)}).orThunk(function(){return e=n,t=r,Zs.nextPosition(e,xu.after(t)).fold(function(){return Zs.prevPosition(e,xu.before(t))},T.some);var e,t})},bm=function(e,t,n){return(r=e,o=t,i=n,r?vm(o,i):pm(o,i)).map(d(hm,n));var r,o,i},ym=function(t,n,e){e.fold(function(){t.focus()},function(e){t.selection.setRng(e.toRange(),n)})},Cm=function(e,t){return t&&e.schema.getBlockElements().hasOwnProperty(sr(t))},wm=function(e){if(dm(e)){var t=or.fromHtml('<br data-mce-bogus="1">');return Oi(e),Bi(e,t),T.some(xu.before(t.dom()))}return T.none()},xm=function(e,t,c){var n=Vr(e).filter(function(e){return Bo.isText(e.dom())}),r=Ir(e).filter(function(e){return Bo.isText(e.dom())});return Hi(e),Ja([n,r,t],function(e,t,n){var r,o,i,a,u=e.dom(),s=t.dom(),l=u.data.length;return o=s,i=c,a=Yn((r=u).data).length,r.appendData(o.data),Hi(or.fromDom(o)),i&&am(r,a),n.container()===s?xu(u,l):n}).orThunk(function(){return c&&(n.each(function(e){return t=e.dom(),n=e.dom().length,r=t.data.slice(0,n),o=r.length-Yn(r).length,im(t,n-o,o);var t,n,r,o}),r.each(function(e){return am(e.dom(),0)})),t})},Nm=function(t,n,e,r){void 0===r&&(r=!0);var o,i,a=bm(n,t.getBody(),e.dom()),u=Gi(e,d(Cm,t),(o=t.getBody(),function(e){return e.dom()===o})),s=xm(e,a,(i=e,vr(t.schema.getTextInlineElements(),sr(i))));t.dom.isEmpty(t.getBody())?(t.setContent(""),t.selection.setCursorLocation()):u.bind(wm).fold(function(){r&&ym(t,n,s)},function(e){r&&ym(t,n,T.some(e))})},Em=pa,zm="_mce_caret",Sm=function(e){return 0<function(e){for(var t=[];e;){if(3===e.nodeType&&e.nodeValue!==Em||1<e.childNodes.length)return[];1===e.nodeType&&t.push(e),e=e.firstChild}return t}(e).length},km=function(e){var t;if(e)for(e=(t=new io(e,e)).current();e;e=t.next())if(3===e.nodeType)return e;return null},Tm=function(e){var t=or.fromTag("span");return Cr(t,{id:zm,"data-mce-bogus":"1","data-mce-type":"format-caret"}),e&&Bi(t,or.fromText(Em)),t},Am=function(e,t,n){void 0===n&&(n=!0);var r,o=e.dom,i=e.selection;if(Sm(t))Nm(e,!1,or.fromDom(t),n);else{var a=i.getRng(),u=o.getParent(t,o.isBlock),s=((r=km(t))&&r.nodeValue.charAt(0)===Em&&r.deleteData(0,1),r);a.startContainer===s&&0<a.startOffset&&a.setStart(s,a.startOffset-1),a.endContainer===s&&0<a.endOffset&&a.setEnd(s,a.endOffset-1),o.remove(t,!0),u&&o.isEmpty(u)&&Xh(or.fromDom(u)),i.setRng(a)}},Rm=function(e,t,n){void 0===n&&(n=!0);var r=e.dom,o=e.selection;if(t)Am(e,t,n);else if(!(t=Wu(e.getBody(),o.getStart())))for(;t=r.get(zm);)Am(e,t,!1)},Dm=function(e,t,n){var r=e.dom,o=r.getParent(n,d(hl.isTextBlock,e));o&&r.isEmpty(o)?n.parentNode.replaceChild(t,n):(Kh(or.fromDom(n)),r.isEmpty(n)?n.parentNode.replaceChild(t,n):r.insertAfter(t,n))},Mm=function(e,t){return e.appendChild(t),t},Bm=function(e,t){var n,r,o=(n=function(e,t){return Mm(e,t.cloneNode(!1))},r=t,function(e,t){for(var n=e.length-1;0<=n;n--)t(e[n],n,e)}(e,function(e){r=n(r,e)}),r);return Mm(o,o.ownerDocument.createTextNode(Em))},_m=function(i){i.on("mouseup keydown",function(e){var t,n,r,o;t=i,n=e.keyCode,r=t.selection,o=t.getBody(),Rm(t,null,!1),8!==n&&46!==n||!r.isCollapsed()||r.getStart().innerHTML!==Em||Rm(t,Wu(o,r.getStart())),37!==n&&39!==n||Rm(t,Wu(o,r.getStart()))})},Om=function(e,t){return e.schema.getTextInlineElements().hasOwnProperty(sr(t))&&!$u(t.dom())&&!Bo.isBogus(t.dom())},Hm={},Pm=$t.filter,Lm=$t.each;Wh=function(e){var t,n,r=e.selection.getRng();t=Bo.matchNodeNames("pre"),r.collapsed||(n=e.selection.getSelectedBlocks(),Lm(Pm(Pm(n,t),function(e){return t(e.previousSibling)&&-1!==$t.indexOf(n,e.previousSibling)}),function(e){var t,n;t=e.previousSibling,pn(n=e).remove(),pn(t).append("<br><br>").append(n.childNodes)}))},Hm[$h="pre"]||(Hm[$h]=[]),Hm[$h].push(Wh);var Vm=function(e,t){Lm(Hm[e],function(e){e(t)})},Im=Gt.each,Fm=function(o){this.compare=function(e,t){if(e.nodeName!==t.nodeName)return!1;var n=function(n){var r={};return Im(o.getAttribs(n),function(e){var t=e.nodeName.toLowerCase();0!==t.indexOf("_")&&"style"!==t&&0!==t.indexOf("data-")&&(r[t]=o.getAttrib(n,t))}),r},r=function(e,t){var n,r;for(r in e)if(e.hasOwnProperty(r)){if(void 0===(n=t[r]))return!1;if(e[r]!==n)return!1;delete t[r]}for(r in t)if(t.hasOwnProperty(r))return!1;return!0};return!(!r(n(e),n(t))||!r(o.parseStyle(o.getAttrib(e,"style")),o.parseStyle(o.getAttrib(t,"style")))||ll(e)||ll(t))}},Um=/^(src|href|style)$/,jm=Gt.each,qm=hl.isEq,$m=function(e,t,n){return e.isChildOf(t,n)&&t!==n&&!e.isBlock(n)},Wm=function(e,t,n){var r,o,i;return r=t[n?"startContainer":"endContainer"],o=t[n?"startOffset":"endOffset"],Bo.isElement(r)&&(i=r.childNodes.length-1,!n&&o&&o--,r=r.childNodes[i<o?i:o]),Bo.isText(r)&&n&&o>=r.nodeValue.length&&(r=new io(r,e.getBody()).next()||r),Bo.isText(r)&&!n&&0===o&&(r=new io(r,e.getBody()).prev()||r),r},Km=function(e,t,n,r){var o=e.create(n,r);return t.parentNode.insertBefore(o,t),o.appendChild(t),o},Xm=function(e,t,n,r,o){var i=or.fromDom(t),a=or.fromDom(e.create(r,o)),u=n?Ur(i):Fr(i);return _i(a,u),n?(Ri(i,a),Mi(a,i)):(Di(i,a),Bi(a,i)),a.dom()},Ym=function(e,t,n,r){return!(t=hl.getNonWhiteSpaceSibling(t,n,r))||"BR"===t.nodeName||e.isBlock(t)},Gm=function(e,n,r,o,i){var t,a,u,s,l,c,f,d,h,m,g,p,v,b,y=e.dom;if(l=y,!(qm(c=o,(f=n).inline)||qm(c,f.block)||(f.selector?Bo.isElement(c)&&l.is(c,f.selector):void 0)||(s=o,n.links&&"A"===s.tagName)))return!1;if("all"!==n.remove)for(jm(n.styles,function(e,t){e=hl.normalizeStyleValue(y,hl.replaceVars(e,r),t),"number"==typeof t&&(t=e,i=0),(n.remove_similar||!i||qm(hl.getStyle(y,i,t),e))&&y.setStyle(o,t,""),u=1}),u&&""===y.getAttrib(o,"style")&&(o.removeAttribute("style"),o.removeAttribute("data-mce-style")),jm(n.attributes,function(e,t){var n;if(e=hl.replaceVars(e,r),"number"==typeof t&&(t=e,i=0),!i||qm(y.getAttrib(i,t),e)){if("class"===t&&(e=y.getAttrib(o,t))&&(n="",jm(e.split(/\s+/),function(e){/mce\-\w+/.test(e)&&(n+=(n?" ":"")+e)}),n))return void y.setAttrib(o,t,n);"class"===t&&o.removeAttribute("className"),Um.test(t)&&o.removeAttribute("data-mce-"+t),o.removeAttribute(t)}}),jm(n.classes,function(e){e=hl.replaceVars(e,r),i&&!y.hasClass(i,e)||y.removeClass(o,e)}),a=y.getAttribs(o),t=0;t<a.length;t++){var C=a[t].nodeName;if(0!==C.indexOf("_")&&0!==C.indexOf("data-"))return!1}return"none"!==n.remove?(d=e,m=n,p=(h=o).parentNode,v=d.dom,b=nc(d),m.block&&(b?p===v.getRoot()&&(m.list_block&&qm(h,m.list_block)||jm(Gt.grep(h.childNodes),function(e){hl.isValid(d,b,e.nodeName.toLowerCase())?g?g.appendChild(e):(g=Km(v,e,b),v.setAttribs(g,d.settings.forced_root_block_attrs)):g=0})):v.isBlock(h)&&!v.isBlock(p)&&(Ym(v,h,!1)||Ym(v,h.firstChild,!0,1)||h.insertBefore(v.create("br"),h.firstChild),Ym(v,h,!0)||Ym(v,h.lastChild,!1,1)||h.appendChild(v.create("br")))),m.selector&&m.inline&&!qm(m.inline,h)||v.remove(h,1),!0):void 0},Jm=Gm,Qm=function(s,l,c,e,f){var t,n,d=s.formatter.get(l),h=d[0],a=!0,u=s.dom,r=s.selection,i=function(e){var n,t,r,o,i,a,u=(n=s,t=e,r=l,o=c,i=f,jm(hl.getParents(n.dom,t.parentNode).reverse(),function(e){var t;a||"_start"===e.id||"_end"===e.id||(t=nm.matchNode(n,e,r,o,i))&&!1!==t.split&&(a=e)}),a);return function(e,t,n,r,o,i,a,u){var s,l,c,f,d,h,m=e.dom;if(n){for(h=n.parentNode,s=r.parentNode;s&&s!==h;s=s.parentNode){for(l=m.clone(s,!1),d=0;d<t.length;d++)if(Gm(e,t[d],u,l,l)){l=0;break}l&&(c&&l.appendChild(c),f||(f=l),c=l)}!i||a.mixed&&m.isBlock(n)||(r=m.split(n,r)),c&&(o.parentNode.insertBefore(c,o),f.appendChild(o))}return r}(s,d,u,e,e,!0,h,c)},m=function(e){var t,n,r,o,i;if(Bo.isElement(e)&&u.getContentEditable(e)&&(o=a,a="true"===u.getContentEditable(e),i=!0),t=Gt.grep(e.childNodes),a&&!i)for(n=0,r=d.length;n<r&&!Gm(s,d[n],c,e,e);n++);if(h.deep&&t.length){for(n=0,r=t.length;n<r;n++)m(t[n]);i&&(a=o)}},g=function(e){var t,n=u.get(e?"_start":"_end"),r=n[e?"firstChild":"lastChild"];return ll(t=r)&&Bo.isElement(t)&&("_start"===t.id||"_end"===t.id)&&(r=r[e?"firstChild":"lastChild"]),Bo.isText(r)&&0===r.data.length&&(r=e?n.previousSibling||n.nextSibling:n.nextSibling||n.previousSibling),u.remove(n,!0),r},o=function(e){var t,n,r=e.commonAncestorContainer;if(e=zl(s,e,d,!0),h.split){if(e=om(e),(t=Wm(s,e,!0))!==(n=Wm(s,e))){if(/^(TR|TH|TD)$/.test(t.nodeName)&&t.firstChild&&(t="TR"===t.nodeName?t.firstChild.firstChild||t:t.firstChild||t),r&&/^T(HEAD|BODY|FOOT|R)$/.test(r.nodeName)&&/^(TH|TD)$/.test(n.nodeName)&&n.firstChild&&(n=n.firstChild||n),$m(u,t,n)){var o=T.from(t.firstChild).getOr(t);return i(Xm(u,o,!0,"span",{id:"_start","data-mce-type":"bookmark"})),void g(!0)}if($m(u,n,t))return o=T.from(n.lastChild).getOr(n),i(Xm(u,o,!1,"span",{id:"_end","data-mce-type":"bookmark"})),void g(!1);t=Km(u,t,"span",{id:"_start","data-mce-type":"bookmark"}),n=Km(u,n,"span",{id:"_end","data-mce-type":"bookmark"}),i(t),i(n),t=g(!0),n=g()}else t=n=i(t);e.startContainer=t.parentNode?t.parentNode:t,e.startOffset=u.nodeIndex(t),e.endContainer=n.parentNode?n.parentNode:n,e.endOffset=u.nodeIndex(n)+1}kl(u,e,function(e){jm(e,function(e){m(e),Bo.isElement(e)&&"underline"===s.dom.getStyle(e,"text-decoration")&&e.parentNode&&"underline"===hl.getTextDecoration(u,e.parentNode)&&Gm(s,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:"underline"}},null,e)})})};if(e)e.nodeType?((n=u.createRng()).setStartBefore(e),n.setEndAfter(e),o(n)):o(e);else if("false"!==u.getContentEditable(r.getNode()))r.isCollapsed()&&h.inline&&!u.select("td[data-mce-selected],th[data-mce-selected]").length?function(e,t,n,r){var o,i,a,u,s,l,c,f=e.dom,d=e.selection,h=[],m=d.getRng();for(o=m.startContainer,i=m.startOffset,3===(s=o).nodeType&&(i!==o.nodeValue.length&&(u=!0),s=s.parentNode);s;){if(nm.matchNode(e,s,t,n,r)){l=s;break}s.nextSibling&&(u=!0),h.push(s),s=s.parentNode}if(l)if(u){a=d.getBookmark(),m.collapse(!0);var g=zl(e,m,e.formatter.get(t),!0);g=om(g),e.formatter.remove(t,n,g),d.moveToBookmark(a)}else{c=Wu(e.getBody(),l);var p=Tm(!1).dom(),v=Bm(h,p);Dm(e,p,c||l),Am(e,c,!1),d.setCursorLocation(v,1),f.isEmpty(l)&&f.remove(l)}}(s,l,c,f):(t=ju.getPersistentBookmark(s.selection,!0),o(r.getRng()),r.moveToBookmark(t),h.inline&&nm.match(s,l,c,r.getStart())&&hl.moveStart(u,r,r.getRng()),s.nodeChanged());else{e=r.getNode();for(var p=0,v=d.length;p<v&&(!d[p].ceFalseOverride||!Gm(s,d[p],c,e,e));p++);}},Zm=Gt.each,eg=function(e){return e&&1===e.nodeType&&!ll(e)&&!$u(e)&&!Bo.isBogus(e)},tg=function(e,t){var n;for(n=e;n;n=n[t]){if(3===n.nodeType&&0!==n.nodeValue.length)return e;if(1===n.nodeType&&!ll(n))return n}return e},ng=function(e,t,n){var r,o,i=new Fm(e);if(t&&n&&(t=tg(t,"previousSibling"),n=tg(n,"nextSibling"),i.compare(t,n))){for(r=t.nextSibling;r&&r!==n;)r=(o=r).nextSibling,t.appendChild(o);return e.remove(n),Gt.each(Gt.grep(n.childNodes),function(e){t.appendChild(e)}),t}return n},rg=function(e,t,n){Zm(e.childNodes,function(e){eg(e)&&(t(e)&&n(e),e.hasChildNodes()&&rg(e,t,n))})},og=function(n,e){return d(function(e,t){return!(!t||!hl.getStyle(n,t,e))},e)},ig=function(r,e,t){return d(function(e,t,n){r.setStyle(n,e,t),""===n.getAttribute("style")&&n.removeAttribute("style"),ag(r,n)},e,t)},ag=function(e,t){"SPAN"===t.nodeName&&0===e.getAttribs(t).length&&e.remove(t,!0)},ug=function(e,t){var n;1===t.nodeType&&t.parentNode&&1===t.parentNode.nodeType&&(n=hl.getTextDecoration(e,t.parentNode),e.getStyle(t,"color")&&n?e.setStyle(t,"text-decoration",n):e.getStyle(t,"text-decoration")===n&&e.setStyle(t,"text-decoration",null))},sg=function(n,e,r,o){Zm(e,function(t){Zm(n.dom.select(t.inline,o),function(e){eg(e)&&Jm(n,t,r,e,t.exact?e:null)}),function(r,e,t){if(e.clear_child_styles){var n=e.links?"*:not(a)":"*";Zm(r.select(n,t),function(n){eg(n)&&Zm(e.styles,function(e,t){r.setStyle(n,t,"")})})}}(n.dom,t,o)})},lg=function(e,t,n,r){(t.styles.color||t.styles.textDecoration)&&(Gt.walk(r,d(ug,e),"childNodes"),ug(e,r))},cg=function(e,t,n,r){t.styles&&t.styles.backgroundColor&&rg(r,og(e,"fontSize"),ig(e,"backgroundColor",hl.replaceVars(t.styles.backgroundColor,n)))},fg=function(e,t,n,r){"sub"!==t.inline&&"sup"!==t.inline||(rg(r,og(e,"fontSize"),ig(e,"fontSize","")),e.remove(e.select("sup"===t.inline?"sub":"sup",r),!0))},dg=function(e,t,n,r){r&&!1!==t.merge_siblings&&(r=ng(e,hl.getNonWhiteSpaceSibling(r),r),r=ng(e,r,hl.getNonWhiteSpaceSibling(r,!0)))},hg=function(t,n,r,o,i){nm.matchNode(t,i.parentNode,r,o)&&Jm(t,n,o,i)||n.merge_with_parents&&t.dom.getParent(i.parentNode,function(e){if(nm.matchNode(t,e,r,o))return Jm(t,n,o,i),!0})},mg=function(a){var u=xu.fromRangeStart(a),s=xu.fromRangeEnd(a),l=a.commonAncestorContainer;return Zs.fromPosition(!1,l,s).map(function(e){return!Cs(u,s,l)&&Cs(u,e,l)?(t=u.container(),n=u.offset(),r=e.container(),o=e.offset(),(i=j.document.createRange()).setStart(t,n),i.setEnd(r,o),i):a;var t,n,r,o,i}).getOr(a)},gg=function(e){return e.collapsed?e:mg(e)},pg=Gt.each,vg=function(m,g,p,r){var e,t,v=m.formatter.get(g),b=v[0],o=!r&&m.selection.isCollapsed(),i=m.dom,n=m.selection,y=function(n,e){if(e=e||b,n){if(e.onformat&&e.onformat(n,e,p,r),pg(e.styles,function(e,t){i.setStyle(n,t,hl.replaceVars(e,p))}),e.styles){var t=i.getAttrib(n,"style");t&&n.setAttribute("data-mce-style",t)}pg(e.attributes,function(e,t){i.setAttrib(n,t,hl.replaceVars(e,p))}),pg(e.classes,function(e){e=hl.replaceVars(e,p),i.hasClass(n,e)||i.addClass(n,e)})}},C=function(e,t){var n=!1;return!!b.selector&&(pg(e,function(e){if(!("collapsed"in e&&e.collapsed!==o))return i.is(t,e.selector)&&!$u(t)?(y(t,e),!(n=!0)):void 0}),n)},a=function(s,e,t,l){var c,f,d=[],h=!0;c=b.inline||b.block,f=s.create(c),y(f),kl(s,e,function(e){var a,u=function(e){var t,n,r,o;if(o=h,t=e.nodeName.toLowerCase(),n=e.parentNode.nodeName.toLowerCase(),1===e.nodeType&&s.getContentEditable(e)&&(o=h,h="true"===s.getContentEditable(e),r=!0),hl.isEq(t,"br"))return a=0,void(b.block&&s.remove(e));if(b.wrapper&&nm.matchNode(m,e,g,p))a=0;else{if(h&&!r&&b.block&&!b.wrapper&&hl.isTextBlock(m,t)&&hl.isValid(m,n,c))return e=s.rename(e,c),y(e),d.push(e),void(a=0);if(b.selector){var i=C(v,e);if(!b.inline||i)return void(a=0)}!h||r||!hl.isValid(m,c,t)||!hl.isValid(m,n,c)||!l&&3===e.nodeType&&1===e.nodeValue.length&&65279===e.nodeValue.charCodeAt(0)||$u(e)||b.inline&&s.isBlock(e)?(a=0,pg(Gt.grep(e.childNodes),u),r&&(h=o),a=0):(a||(a=s.clone(f,!1),e.parentNode.insertBefore(a,e),d.push(a)),a.appendChild(e))}};pg(e,u)}),!0===b.links&&pg(d,function(e){var t=function(e){"A"===e.nodeName&&y(e,b),pg(Gt.grep(e.childNodes),t)};t(e)}),pg(d,function(e){var t,n,r,o,i,a=function(e){var n=!1;return pg(e.childNodes,function(e){if((t=e)&&1===t.nodeType&&!ll(t)&&!$u(t)&&!Bo.isBogus(t))return n=e,!1;var t}),n};n=0,pg(e.childNodes,function(e){hl.isWhiteSpaceNode(e)||ll(e)||n++}),t=n,!(1<d.length)&&s.isBlock(e)||0!==t?(b.inline||b.wrapper)&&(b.exact||1!==t||((o=a(r=e))&&!ll(o)&&nm.matchName(s,o,b)&&(i=s.clone(o,!1),y(i),s.replace(i,r,!0),s.remove(o,1)),e=i||r),sg(m,v,p,e),hg(m,b,g,p,e),cg(s,b,p,e),fg(s,b,p,e),dg(s,b,p,e)):s.remove(e,1)})};if("false"!==i.getContentEditable(n.getNode())){if(b){if(r)r.nodeType?C(v,r)||((t=i.createRng()).setStartBefore(r),t.setEndAfter(r),a(i,zl(m,t,v),0,!0)):a(i,r,0,!0);else if(o&&b.inline&&!i.select("td[data-mce-selected],th[data-mce-selected]").length)!function(e,t,n){var r,o,i,a,u,s,l=e.selection;a=(r=l.getRng(!0)).startOffset,s=r.startContainer.nodeValue,(o=Wu(e.getBody(),l.getStart()))&&(i=km(o));var c,f,d=/[^\s\u00a0\u00ad\u200b\ufeff]/;s&&0<a&&a<s.length&&d.test(s.charAt(a))&&d.test(s.charAt(a-1))?(u=l.getBookmark(),r.collapse(!0),r=zl(e,r,e.formatter.get(t)),r=om(r),e.formatter.apply(t,n,r),l.moveToBookmark(u)):(o&&i.nodeValue===Em||(c=e.getDoc(),f=Tm(!0).dom(),i=(o=c.importNode(f,!0)).firstChild,r.insertNode(o),a=1),e.formatter.apply(t,n,o),l.setCursorLocation(i,a))}(m,g,p);else{var u=m.selection.getNode();m.settings.forced_root_block||!v[0].defaultBlock||i.getParent(u,i.isBlock)||vg(m,v[0].defaultBlock),m.selection.setRng(gg(m.selection.getRng())),e=ju.getPersistentBookmark(m.selection,!0),a(i,zl(m,n.getRng(),v)),b.styles&&lg(i,b,p,u),n.moveToBookmark(e),hl.moveStart(i,n,n.getRng()),m.nodeChanged()}Vm(g,m)}}else{r=n.getNode();for(var s=0,l=v.length;s<l;s++)if(v[s].ceFalseOverride&&i.is(r,v[s].selector))return void y(r,v[s])}},bg={applyFormat:vg},yg=Gt.each,Cg=function(e,t,n,r,o){var i,a,u,s,l,c,f,d;null===t.get()&&(a=e,u={},(i=t).set({}),a.on("NodeChange",function(n){var r=hl.getParents(a.dom,n.element),o={};r=Gt.grep(r,function(e){return 1===e.nodeType&&!e.getAttribute("data-mce-bogus")}),yg(i.get(),function(e,n){yg(r,function(t){return a.formatter.matchNode(t,n,{},e.similar)?(u[n]||(yg(e,function(e){e(!0,{node:t,format:n,parents:r})}),u[n]=e),o[n]=e,!1):!nm.matchesUnInheritedFormatSelector(a,t,n)&&void 0})}),yg(u,function(e,t){o[t]||(delete u[t],yg(e,function(e){e(!1,{node:n.element,format:t,parents:r})}))})})),l=n,c=r,f=o,d=(s=t).get(),yg(l.split(","),function(e){d[e]||(d[e]=[],d[e].similar=f),d[e].push(c)}),s.set(d)},wg={get:function(r){var t={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"left"},inherit:!1,preview:!1,defaultBlock:"div"},{selector:"img,table",collapsed:!1,styles:{"float":"left"},preview:"font-family font-size"}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"img,table",collapsed:!1,styles:{"float":"right"},preview:"font-family font-size"}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"justify"},inherit:!1,defaultBlock:"div",preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all"},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all"}],italic:[{inline:"em",remove:"all"},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all"}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all"}],strikethrough:[{inline:"span",styles:{textDecoration:"line-through"},exact:!0},{inline:"strike",remove:"all"}],forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},fontsize_class:{inline:"span",attributes:{"class":"%value"}},blockquote:{block:"blockquote",wrapper:!0,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:function(){return!0},onformat:function(n,e,t){Gt.each(t,function(e,t){r.setAttrib(n,t,e)})}},removeformat:[{selector:"b,strong,em,i,font,u,strike,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return Gt.each("p h1 h2 h3 h4 h5 h6 div address pre div dt dd samp".split(/\s/),function(e){t[e]={block:e,remove:"all"}}),t}},xg=Gt.each,Ng=vi.DOM,Eg=function(e,t){var n,o,r,h=t&&t.schema||oi({}),m=function(e){var t,n,r;return o="string"==typeof e?{name:e,classes:[],attrs:{}}:e,t=Ng.create(o.name),n=t,(r=o).classes.length&&Ng.addClass(n,r.classes.join(" ")),Ng.setAttribs(n,r.attrs),t},g=function(n,e,t){var r,o,i,a,u,s,l,c,f=0<e.length&&e[0],d=f&&f.name;if(u=d,s="string"!=typeof(a=n)?a.nodeName.toLowerCase():a,l=h.getElementRule(s),i=!(!(c=l&&l.parentsRequired)||!c.length)&&(u&&-1!==Gt.inArray(c,u)?u:c[0]))d===i?(o=e[0],e=e.slice(1)):o=i;else if(f)o=e[0],e=e.slice(1);else if(!t)return n;return o&&(r=m(o)).appendChild(n),t&&(r||(r=Ng.create("div")).appendChild(n),Gt.each(t,function(e){var t=m(e);r.insertBefore(t,n)})),g(r,e,o&&o.siblings)};return e&&e.length?(o=e[0],n=m(o),(r=Ng.create("div")).appendChild(g(n,e.slice(1),o.siblings)),r):""},zg=function(e){var t,a={classes:[],attrs:{}};return"*"!==(e=a.selector=Gt.trim(e))&&(t=e.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,function(e,t,n,r,o){switch(t){case"#":a.attrs.id=n;break;case".":a.classes.push(n);break;case":":-1!==Gt.inArray("checked disabled enabled read-only required".split(" "),n)&&(a.attrs[n]=n)}if("["===r){var i=o.match(/([\w\-]+)(?:\=\"([^\"]+))?/);i&&(a.attrs[i[1]]=i[2])}return""})),a.name=t||"div",a},Sg=function(e){return e&&"string"==typeof e?(e=(e=e.split(/\s*,\s*/)[0]).replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),Gt.map(e.split(/(?:>|\s+(?![^\[\]]+\]))/),function(e){var t=Gt.map(e.split(/(?:~\+|~|\+)/),zg),n=t.pop();return t.length&&(n.siblings=t),n}).reverse()):[]},kg=function(n,e){var t,r,o,i,a,u,s="";if(!1===(u=n.settings.preview_styles))return"";"string"!=typeof u&&(u="font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow");var l=function(e){return e.replace(/%(\w+)/g,"")};if("string"==typeof e){if(!(e=n.formatter.get(e)))return;e=e[0]}return"preview"in e&&!1===(u=e.preview)?"":(t=e.block||e.inline||"span",r=(i=Sg(e.selector)).length?(i[0].name||(i[0].name=t),t=e.selector,Eg(i,n)):Eg([t],n),o=Ng.select(t,r)[0]||r.firstChild,xg(e.styles,function(e,t){(e=l(e))&&Ng.setStyle(o,t,e)}),xg(e.attributes,function(e,t){(e=l(e))&&Ng.setAttrib(o,t,e)}),xg(e.classes,function(e){e=l(e),Ng.hasClass(o,e)||Ng.addClass(o,e)}),n.fire("PreviewFormats"),Ng.setStyles(r,{position:"absolute",left:-65535}),n.getBody().appendChild(r),a=Ng.getStyle(n.getBody(),"fontSize",!0),a=/px$/.test(a)?parseInt(a,10):0,xg(u.split(" "),function(e){var t=Ng.getStyle(o,e,!0);if(!("background-color"===e&&/transparent|rgba\s*\([^)]+,\s*0\)/.test(t)&&(t=Ng.getStyle(n.getBody(),e,!0),"#ffffff"===Ng.toHex(t).toLowerCase())||"color"===e&&"#000000"===Ng.toHex(t).toLowerCase())){if("font-size"===e&&/em|%$/.test(t)){if(0===a)return;t=parseFloat(t)/(/%$/.test(t)?100:1)*a+"px"}"border"===e&&t&&(s+="padding:0 2px;"),s+=e+":"+t+";"}}),n.fire("AfterPreviewFormats"),Ng.remove(r),s)},Tg=function(e,t,n,r,o){var i=t.get(n);!nm.match(e,n,r,o)||"toggle"in i[0]&&!i[0].toggle?bg.applyFormat(e,n,r,o):Qm(e,n,r,o)},Ag=function(e){e.addShortcut("meta+b","","Bold"),e.addShortcut("meta+i","","Italic"),e.addShortcut("meta+u","","Underline");for(var t=1;t<=6;t++)e.addShortcut("access+"+t,"",["FormatBlock",!1,"h"+t]);e.addShortcut("access+7","",["FormatBlock",!1,"p"]),e.addShortcut("access+8","",["FormatBlock",!1,"div"]),e.addShortcut("access+9","",["FormatBlock",!1,"address"])};function Rg(e){var t=function o(e){var n={},r=function(e,t){e&&("string"!=typeof e?Gt.each(e,function(e,t){r(t,e)}):(D(t)||(t=[t]),Gt.each(t,function(e){"undefined"==typeof e.deep&&(e.deep=!e.selector),"undefined"==typeof e.split&&(e.split=!e.selector||e.inline),"undefined"==typeof e.remove&&e.selector&&!e.inline&&(e.remove="none"),e.selector&&e.inline&&(e.mixed=!0,e.block_expand=!0),"string"==typeof e.classes&&(e.classes=e.classes.split(/\s+/))}),n[e]=t))};return r(wg.get(e.dom)),r(e.settings.formats),{get:function(e){return e?n[e]:n},has:function(e){return vr(n,e)},register:r,unregister:function(e){return e&&n[e]&&delete n[e],n}}}(e),n=Ei(null);return Ag(e),_m(e),{get:t.get,has:t.has,register:t.register,unregister:t.unregister,apply:d(bg.applyFormat,e),remove:d(Qm,e),toggle:d(Tg,e,t),match:d(nm.match,e),matchAll:d(nm.matchAll,e),matchNode:d(nm.matchNode,e),canApply:d(nm.canApply,e),formatChanged:d(Cg,e,n),getCssText:d(kg,e)}}var Dg={register:function(t,s,l){t.addAttributeFilter("data-mce-tabindex",function(e,t){for(var n,r=e.length;r--;)(n=e[r]).attr("tabindex",n.attributes.map["data-mce-tabindex"]),n.attr(t,null)}),t.addAttributeFilter("src,href,style",function(e,t){for(var n,r,o=e.length,i="data-mce-"+t,a=s.url_converter,u=s.url_converter_scope;o--;)(r=(n=e[o]).attributes.map[i])!==undefined?(n.attr(t,0<r.length?r:null),n.attr(i,null)):(r=n.attributes.map[t],"style"===t?r=l.serializeStyle(l.parseStyle(r),n.name):a&&(r=a.call(u,r,t,n.name)),n.attr(t,0<r.length?r:null))}),t.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)(n=(t=e[r]).attr("class"))&&(n=t.attr("class").replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),t.attr("class",0<n.length?n:null))}),t.addAttributeFilter("data-mce-type",function(e,t,n){for(var r,o=e.length;o--;)"bookmark"!==(r=e[o]).attributes.map["data-mce-type"]||n.cleanup||r.remove()}),t.addNodeFilter("noscript",function(e){for(var t,n=e.length;n--;)(t=e[n].firstChild)&&(t.value=Ko.decode(t.value))}),t.addNodeFilter("script,style",function(e,t){for(var n,r,o,i=e.length,a=function(e){return e.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"")};i--;)r=(n=e[i]).firstChild?n.firstChild.value:"","script"===t?((o=n.attr("type"))&&n.attr("type","mce-no/type"===o?null:o.replace(/^mce\-/,"")),"xhtml"===s.element_format&&0<r.length&&(n.firstChild.value="// <![CDATA[\n"+a(r)+"\n// ]]>")):"xhtml"===s.element_format&&0<r.length&&(n.firstChild.value="\x3c!--\n"+a(r)+"\n--\x3e")}),t.addNodeFilter("#comment",function(e){for(var t,n=e.length;n--;)0===(t=e[n]).value.indexOf("[CDATA[")?(t.name="#cdata",t.type=4,t.value=t.value.replace(/^\[CDATA\[|\]\]$/g,"")):0===t.value.indexOf("mce:protected ")&&(t.name="#text",t.type=3,t.raw=!0,t.value=unescape(t.value).substr(14))}),t.addNodeFilter("xml:namespace,input",function(e,t){for(var n,r=e.length;r--;)7===(n=e[r]).type?n.remove():1===n.type&&("input"!==t||"type"in n.attributes.map||n.attr("type","text"))}),t.addAttributeFilter("data-mce-type",function(e){L(e,function(e){"format-caret"===e.attr("data-mce-type")&&(e.isEmpty(t.schema.getNonEmptyElements())?e.remove():e.unwrap())})}),t.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-type,data-mce-resize",function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)})},trimTrailingBr:function(e){var t,n,r=function(e){return e&&"br"===e.name};r(t=e.lastChild)&&r(n=t.prev)&&(t.remove(),n.remove())}},Mg={process:function(e,t,n){return f=n,(c=e)&&c.hasEventListeners("PreProcess")&&!f.no_events?(o=t,i=n,l=(r=e).dom,o=o.cloneNode(!0),(a=j.document.implementation).createHTMLDocument&&(u=a.createHTMLDocument(""),Gt.each("BODY"===o.nodeName?o.childNodes:[o],function(e){u.body.appendChild(u.importNode(e,!0))}),o="BODY"!==o.nodeName?u.body.firstChild:u.body,s=l.doc,l.doc=u),sf(r,Pl(i,{node:o})),s&&(l.doc=s),o):t;var r,o,i,a,u,s,l,c,f}},Bg=function(e,a,u){e.addNodeFilter("font",function(e){L(e,function(e){var t,n=a.parse(e.attr("style")),r=e.attr("color"),o=e.attr("face"),i=e.attr("size");r&&(n.color=r),o&&(n["font-family"]=o),i&&(n["font-size"]=u[parseInt(e.attr("size"),10)-1]),e.name="span",e.attr("style",a.serialize(n)),t=e,L(["color","face","size"],function(e){t.attr(e,null)})})})},_g=function(e,t){var n,r=ai();t.convert_fonts_to_spans&&Bg(e,r,Gt.explode(t.font_size_legacy_values)),n=r,e.addNodeFilter("strike",function(e){L(e,function(e){var t=n.parse(e.attr("style"));t["text-decoration"]="line-through",e.name="span",e.attr("style",n.serialize(t))})})},Og={register:function(e,t){t.inline_styles&&_g(e,t)}},Hg=function(e,t,n,r){(e.padd_empty_with_br||t.insert)&&n[r.name]?r.empty().append(new Fl("br",1)).shortEnded=!0:r.empty().append(new Fl("#text",3)).value="\xa0"},Pg=function(e){return Lg(e,"#text")&&"\xa0"===e.firstChild.value},Lg=function(e,t){return e&&e.firstChild&&e.firstChild===e.lastChild&&e.firstChild.name===t},Vg=function(r,e,t,n){return n.isEmpty(e,t,function(e){return t=e,(n=r.getElementRule(t.name))&&n.paddEmpty;var t,n})},Ig=function(e,t){return e&&(t[e.name]||"br"===e.name)},Fg=function(e,g){var p=e.schema;g.remove_trailing_brs&&e.addNodeFilter("br",function(e,t,n){var r,o,i,a,u,s,l,c,f=e.length,d=Gt.extend({},p.getBlockElements()),h=p.getNonEmptyElements(),m=p.getNonEmptyElements();for(d.body=1,r=0;r<f;r++)if(i=(o=e[r]).parent,d[o.parent.name]&&o===i.lastChild){for(u=o.prev;u;){if("span"!==(s=u.name)||"bookmark"!==u.attr("data-mce-type")){if("br"!==s)break;if("br"===s){o=null;break}}u=u.prev}o&&(o.remove(),Vg(p,h,m,i)&&(l=p.getElementRule(i.name))&&(l.removeEmpty?i.remove():l.paddEmpty&&Hg(g,n,d,i)))}else{for(a=o;i&&i.firstChild===a&&i.lastChild===a&&!d[(a=i).name];)i=i.parent;a===i&&!0!==g.padd_empty_with_br&&((c=new Fl("#text",3)).value="\xa0",o.replace(c))}}),e.addAttributeFilter("href",function(e){var t,n,r,o=e.length;if(!g.allow_unsafe_link_target)for(;o--;)"a"===(t=e[o]).name&&"_blank"===t.attr("target")&&t.attr("rel",(n=t.attr("rel"),r=n?Gt.trim(n):"",/\b(noopener)\b/g.test(r)?r:r.split(" ").filter(function(e){return 0<e.length}).concat(["noopener"]).sort().join(" ")))}),g.allow_html_in_named_anchor||e.addAttributeFilter("id,name",function(e){for(var t,n,r,o,i=e.length;i--;)if("a"===(o=e[i]).name&&o.firstChild&&!o.attr("href"))for(r=o.parent,t=o.lastChild;n=t.prev,r.insert(t,o),t=n;);}),g.fix_list_elements&&e.addNodeFilter("ul,ol",function(e){for(var t,n,r=e.length;r--;)if("ul"===(n=(t=e[r]).parent).name||"ol"===n.name)if(t.prev&&"li"===t.prev.name)t.prev.append(t);else{var o=new Fl("li",1);o.attr("style","list-style-type: none"),t.wrap(o)}}),g.validate&&p.getValidClasses()&&e.addAttributeFilter("class",function(e){for(var t,n,r,o,i,a,u,s=e.length,l=p.getValidClasses();s--;){for(n=(t=e[s]).attr("class").split(" "),i="",r=0;r<n.length;r++)o=n[r],u=!1,(a=l["*"])&&a[o]&&(u=!0),a=l[t.name],!u&&a&&a[o]&&(u=!0),u&&(i&&(i+=" "),i+=o);i.length||(i=null),t.attr("class",i)}})},Ug=Gt.makeMap,jg=Gt.each,qg=Gt.explode,$g=Gt.extend;function Wg(T,A){void 0===A&&(A=oi());var R={},D=[],M={},B={};(T=T||{}).validate=!("validate"in T)||T.validate,T.root_name=T.root_name||"body";var _=function(e){var t,n,r;(n=e.name)in R&&((r=M[n])?r.push(e):M[n]=[e]),t=D.length;for(;t--;)(n=D[t].name)in e.attributes.map&&((r=B[n])?r.push(e):B[n]=[e]);return e},e={schema:A,addAttributeFilter:function(e,n){jg(qg(e),function(e){var t;for(t=0;t<D.length;t++)if(D[t].name===e)return void D[t].callbacks.push(n);D.push({name:e,callbacks:[n]})})},getAttributeFilters:function(){return[].concat(D)},addNodeFilter:function(e,n){jg(qg(e),function(e){var t=R[e];t||(R[e]=t=[]),t.push(n)})},getNodeFilters:function(){var e=[];for(var t in R)R.hasOwnProperty(t)&&e.push({name:t,callbacks:R[t]});return e},filterNode:_,parse:function(e,a){var t,n,r,o,i,u,s,l,c,f,d,h=[];a=a||{},M={},B={},c=$g(Ug("script,style,head,html,body,title,meta,param"),A.getBlockElements());var m,g=A.getNonEmptyElements(),p=A.children,v=T.validate,b="forced_root_block"in a?a.forced_root_block:T.forced_root_block,y=!1===(m=b)?"":!0===m?"p":m,C=A.getWhiteSpaceElements(),w=/^[ \t\r\n]+/,x=/[ \t\r\n]+$/,N=/[ \t\r\n]+/g,E=/^[ \t\r\n]+$/;f=C.hasOwnProperty(a.context)||C.hasOwnProperty(T.root_name);var z=function(e,t){var n,r=new Fl(e,t);return e in R&&((n=M[e])?n.push(r):M[e]=[r]),r},S=function(e){var t,n,r,o,i=A.getBlockElements();for(t=e.prev;t&&3===t.type;){if(0<(r=t.value.replace(x,"")).length)return void(t.value=r);if(n=t.next){if(3===n.type&&n.value.length){t=t.prev;continue}if(!i[n.name]&&"script"!==n.name&&"style"!==n.name){t=t.prev;continue}}o=t.prev,t.remove(),t=o}};t=$l({validate:v,allow_script_urls:T.allow_script_urls,allow_conditional_comments:T.allow_conditional_comments,self_closing_elements:function(e){var t,n={};for(t in e)"li"!==t&&"p"!==t&&(n[t]=e[t]);return n}(A.getSelfClosingElements()),cdata:function(e){d.append(z("#cdata",4)).value=e},text:function(e,t){var n;f||(e=e.replace(N," "),Ig(d.lastChild,c)&&(e=e.replace(w,""))),0!==e.length&&((n=z("#text",3)).raw=!!t,d.append(n).value=e)},comment:function(e){d.append(z("#comment",8)).value=e},pi:function(e,t){d.append(z(e,7)).value=t,S(d)},doctype:function(e){d.append(z("#doctype",10)).value=e,S(d)},start:function(e,t,n){var r,o,i,a,u;if(i=v?A.getElementRule(e):{}){for((r=z(i.outputName||e,1)).attributes=t,r.shortEnded=n,d.append(r),(u=p[d.name])&&p[r.name]&&!u[r.name]&&h.push(r),o=D.length;o--;)(a=D[o].name)in t.map&&((s=B[a])?s.push(r):B[a]=[r]);c[e]&&S(r),n||(d=r),!f&&C[e]&&(f=!0)}},end:function(e){var t,n,r,o,i;if(n=v?A.getElementRule(e):{}){if(c[e]&&!f){if((t=d.firstChild)&&3===t.type)if(0<(r=t.value.replace(w,"")).length)t.value=r,t=t.next;else for(o=t.next,t.remove(),t=o;t&&3===t.type;)r=t.value,o=t.next,(0===r.length||E.test(r))&&(t.remove(),t=o),t=o;if((t=d.lastChild)&&3===t.type)if(0<(r=t.value.replace(x,"")).length)t.value=r,t=t.prev;else for(o=t.prev,t.remove(),t=o;t&&3===t.type;)r=t.value,o=t.prev,(0===r.length||E.test(r))&&(t.remove(),t=o),t=o}if(f&&C[e]&&(f=!1),n.removeEmpty&&Vg(A,g,C,d)&&!d.attributes.map.name&&!d.attr("id"))return i=d.parent,c[d.name]?d.empty().remove():d.unwrap(),void(d=i);n.paddEmpty&&(Pg(d)||Vg(A,g,C,d))&&Hg(T,a,c,d),d=d.parent}}},A);var k=d=new Fl(a.context||T.root_name,11);if(t.parse(e),v&&h.length&&(a.context?a.invalid=!0:function(e){var t,n,r,o,i,a,u,s,l,c,f,d,h,m,g,p;for(d=Ug("tr,td,th,tbody,thead,tfoot,table"),c=A.getNonEmptyElements(),f=A.getWhiteSpaceElements(),h=A.getTextBlockElements(),m=A.getSpecialElements(),t=0;t<e.length;t++)if((n=e[t]).parent&&!n.fixed)if(h[n.name]&&"li"===n.parent.name){for(g=n.next;g&&h[g.name];)g.name="li",g.fixed=!0,n.parent.insert(g,n.parent),g=g.next;n.unwrap(n)}else{for(o=[n],r=n.parent;r&&!A.isValidChild(r.name,n.name)&&!d[r.name];r=r.parent)o.push(r);if(r&&1<o.length){for(o.reverse(),i=a=_(o[0].clone()),l=0;l<o.length-1;l++){for(A.isValidChild(a.name,o[l].name)?(u=_(o[l].clone()),a.append(u)):u=a,s=o[l].firstChild;s&&s!==o[l+1];)p=s.next,u.append(s),s=p;a=u}Vg(A,c,f,i)?r.insert(n,o[0],!0):(r.insert(i,o[0],!0),r.insert(n,i)),r=o[0],(Vg(A,c,f,r)||Lg(r,"br"))&&r.empty().remove()}else if(n.parent){if("li"===n.name){if((g=n.prev)&&("ul"===g.name||"ul"===g.name)){g.append(n);continue}if((g=n.next)&&("ul"===g.name||"ul"===g.name)){g.insert(n,g.firstChild,!0);continue}n.wrap(_(new Fl("ul",1)));continue}A.isValidChild(n.parent.name,"div")&&A.isValidChild("div",n.name)?n.wrap(_(new Fl("div",1))):m[n.name]?n.empty().remove():n.unwrap()}}}(h)),y&&("body"===k.name||a.isRootContent)&&function(){var e,t,n=k.firstChild,r=function(e){e&&((n=e.firstChild)&&3===n.type&&(n.value=n.value.replace(w,"")),(n=e.lastChild)&&3===n.type&&(n.value=n.value.replace(x,"")))};if(A.isValidChild(k.name,y.toLowerCase())){for(;n;)e=n.next,3===n.type||1===n.type&&"p"!==n.name&&!c[n.name]&&!n.attr("data-mce-type")?(t||((t=z(y,1)).attr(T.forced_root_block_attrs),k.insert(t,n)),t.append(n)):(r(t),t=null),n=e;r(t)}}(),!a.invalid){for(l in M){for(s=R[l],i=(n=M[l]).length;i--;)n[i].parent||n.splice(i,1);for(r=0,o=s.length;r<o;r++)s[r](n,l,a)}for(r=0,o=D.length;r<o;r++)if((s=D[r]).name in B){for(i=(n=B[s.name]).length;i--;)n[i].parent||n.splice(i,1);for(i=0,u=s.callbacks.length;i<u;i++)s.callbacks[i](n,s.name,a)}}return k}};return Fg(e,T),Og.register(e,T),e}var Kg=function(e,t,n){-1===Gt.inArray(t,n)&&(e.addAttributeFilter(n,function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)}),t.push(n))},Xg=function(e,t,n){var r=va(n.getInner?t.innerHTML:e.getOuterHTML(t));return n.selection||wo(or.fromDom(t))?r:Gt.trim(r)},Yg=function(e,t,n){var r=n.selection?Pl({forced_root_block:!1},n):n,o=e.parse(t,r);return Dg.trimTrailingBr(o),o},Gg=function(e,t,n,r,o){var i,a,u,s,l=(i=r,kc(t,n).serialize(i));return a=e,s=l,(u=o).no_events||!a?s:lf(a,Pl(u,{content:s})).content};function Jg(e,t){var n=function r(a,u){var s,l,c,e=["data-mce-selected"];return s=u&&u.dom?u.dom:vi.DOM,l=u&&u.schema?u.schema:oi(a),a.entity_encoding=a.entity_encoding||"named",a.remove_trailing_brs=!("remove_trailing_brs"in a)||a.remove_trailing_brs,c=Wg(a,l),Dg.register(c,a,s),{schema:l,addNodeFilter:c.addNodeFilter,addAttributeFilter:c.addAttributeFilter,serialize:function(e,t){var n=Pl({format:"html"},t||{}),r=Mg.process(u,e,n),o=Xg(s,r,n),i=Yg(c,o,n);return"tree"===n.format?i:Gg(u,a,l,i,n)},addRules:function(e){l.addValidElements(e)},setRules:function(e){l.setValidElements(e)},addTempAttr:d(Kg,c,e),getTempAttrs:function(){return e}}}(e,t);return{schema:n.schema,addNodeFilter:n.addNodeFilter,addAttributeFilter:n.addAttributeFilter,serialize:n.serialize,addRules:n.addRules,setRules:n.setRules,addTempAttr:n.addTempAttr,getTempAttrs:n.getTempAttrs}}function Qg(e){return{getBookmark:d(ul,e),moveToBookmark:d(sl,e)}}(Qg||(Qg={})).isBookmarkNode=ll;var Zg,ep,tp=Qg,np=Bo.isContentEditableFalse,rp=Bo.isContentEditableTrue,op=function(r,a){var u,s,l,c,f,d,h,m,g,p,v,b,i,y,C,w,x,N=a.dom,E=Gt.each,z=a.getDoc(),S=j.document,k=Math.abs,T=Math.round,A=a.getBody();c={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]};var R=function(e){return e&&("IMG"===e.nodeName||a.dom.is(e,"figure.image"))},e=function(e){var t,n,r=e.target;t=e,n=a.selection.getRng(),!R(t.target)||nh(t.clientX,t.clientY,n)||e.isDefaultPrevented()||(e.preventDefault(),a.selection.select(r))},D=function(e){return a.dom.is(e,"figure.image")?e.querySelector("img"):e},M=function(e){var t=a.settings.object_resizing;return!1!==t&&!he.iOS&&("string"!=typeof t&&(t="table,img,figure.image,div"),"false"!==e.getAttribute("data-mce-resize")&&e!==a.getBody()&&Br(or.fromDom(e),t))},B=function(e){var t,n,r,o;t=e.screenX-d,n=e.screenY-h,y=t*f[2]+p,C=n*f[3]+v,y=y<5?5:y,C=C<5?5:C,(R(u)&&!1!==a.settings.resize_img_proportional?!ch.modifierPressed(e):ch.modifierPressed(e)||R(u)&&f[2]*f[3]!=0)&&(k(t)>k(n)?(C=T(y*b),y=T(C/b)):(y=T(C/b),C=T(y*b))),N.setStyles(D(s),{width:y,height:C}),r=0<(r=f.startPos.x+t)?r:0,o=0<(o=f.startPos.y+n)?o:0,N.setStyles(l,{left:r,top:o,display:"block"}),l.innerHTML=y+" &times; "+C,f[2]<0&&s.clientWidth<=y&&N.setStyle(s,"left",m+(p-y)),f[3]<0&&s.clientHeight<=C&&N.setStyle(s,"top",g+(v-C)),(t=A.scrollWidth-w)+(n=A.scrollHeight-x)!=0&&N.setStyles(l,{left:r-t,top:o-n}),i||(hf(a,u,p,v),i=!0)},_=function(){i=!1;var e=function(e,t){t&&(u.style[e]||!a.schema.isValid(u.nodeName.toLowerCase(),e)?N.setStyle(D(u),e,t):N.setAttrib(D(u),e,t))};e("width",y),e("height",C),N.unbind(z,"mousemove",B),N.unbind(z,"mouseup",_),S!==z&&(N.unbind(S,"mousemove",B),N.unbind(S,"mouseup",_)),N.remove(s),N.remove(l),o(u),mf(a,u,y,C),N.setAttrib(u,"style",N.getAttrib(u,"style")),a.nodeChanged()},o=function(e){var t,r,o,n,i;O(),P(),t=N.getPos(e,A),m=t.x,g=t.y,i=e.getBoundingClientRect(),r=i.width||i.right-i.left,o=i.height||i.bottom-i.top,u!==e&&(u=e,y=C=0),n=a.fire("ObjectSelected",{target:e}),M(e)&&!n.isDefaultPrevented()?E(c,function(n,e){var t;(t=N.get("mceResizeHandle"+e))&&N.remove(t),t=N.add(A,"div",{id:"mceResizeHandle"+e,"data-mce-bogus":"all","class":"mce-resizehandle",unselectable:!0,style:"cursor:"+e+"-resize; margin:0; padding:0"}),11===he.ie&&(t.contentEditable=!1),N.bind(t,"mousedown",function(e){var t;e.stopImmediatePropagation(),e.preventDefault(),d=(t=e).screenX,h=t.screenY,p=D(u).clientWidth,v=D(u).clientHeight,b=v/p,(f=n).startPos={x:r*n[0]+m,y:o*n[1]+g},w=A.scrollWidth,x=A.scrollHeight,s=u.cloneNode(!0),N.addClass(s,"mce-clonedresizable"),N.setAttrib(s,"data-mce-bogus","all"),s.contentEditable=!1,s.unSelectabe=!0,N.setStyles(s,{left:m,top:g,margin:0}),s.removeAttribute("data-mce-selected"),A.appendChild(s),N.bind(z,"mousemove",B),N.bind(z,"mouseup",_),S!==z&&(N.bind(S,"mousemove",B),N.bind(S,"mouseup",_)),l=N.add(A,"div",{"class":"mce-resize-helper","data-mce-bogus":"all"},p+" &times; "+v)}),n.elm=t,N.setStyles(t,{left:r*n[0]+m-t.offsetWidth/2,top:o*n[1]+g-t.offsetHeight/2})}):O(),u.setAttribute("data-mce-selected","1")},O=function(){var e,t;for(e in P(),u&&u.removeAttribute("data-mce-selected"),c)(t=N.get("mceResizeHandle"+e))&&(N.unbind(t),N.remove(t))},n=function(e){var t,n=function(e,t){if(e)do{if(e===t)return!0}while(e=e.parentNode)};i||a.removed||(E(N.select("img[data-mce-selected],hr[data-mce-selected]"),function(e){e.removeAttribute("data-mce-selected")}),t="mousedown"===e.type?e.target:r.getNode(),n(t=N.$(t).closest("table,img,figure.image,hr")[0],A)&&(L(),n(r.getStart(!0),t)&&n(r.getEnd(!0),t))?o(t):O())},H=function(e){return np(function(e,t){for(;t&&t!==e;){if(rp(t)||np(t))return t;t=t.parentNode}return null}(a.getBody(),e))},P=function(){for(var e in c){var t=c[e];t.elm&&(N.unbind(t.elm),delete t.elm)}},L=function(){try{a.getDoc().execCommand("enableObjectResizing",!1,!1)}catch(e){}};return a.on("init",function(){L(),he.ie&&11<=he.ie&&(a.on("mousedown click",function(e){var t=e.target,n=t.nodeName;i||!/^(TABLE|IMG|HR)$/.test(n)||H(t)||(2!==e.button&&a.selection.select(t,"TABLE"===n),"mousedown"===e.type&&a.nodeChanged())}),a.dom.bind(A,"mscontrolselect",function(e){var t=function(e){be.setEditorTimeout(a,function(){a.selection.select(e)})};if(H(e.target))return e.preventDefault(),void t(e.target);/^(TABLE|IMG|HR)$/.test(e.target.nodeName)&&(e.preventDefault(),"IMG"===e.target.tagName&&t(e.target))}));var t=be.throttle(function(e){a.composing||n(e)});a.on("nodechange ResizeEditor ResizeWindow drop FullscreenStateChanged",t),a.on("keyup compositionend",function(e){u&&"TABLE"===u.nodeName&&t(e)}),a.on("hide blur",O),a.on("contextmenu",e)}),a.on("remove",P),{isResizable:M,showResizeRect:o,hideResizeRect:O,updateResizeRect:n,destroy:function(){u=s=null}}},ip=function(e){for(var t=0,n=0,r=e;r&&r.nodeType;)t+=r.offsetLeft||0,n+=r.offsetTop||0,r=r.offsetParent;return{x:t,y:n}},ap=function(e,t,n){var r,o,i,a,u,s=e.dom,l=s.getRoot(),c=0;if(u={elm:t,alignToTop:n},e.fire("scrollIntoView",u),!u.isDefaultPrevented()&&Bo.isElement(t)){if(!1===n&&(c=t.offsetHeight),"BODY"!==l.nodeName){var f=e.selection.getScrollContainer();if(f)return r=ip(t).y-ip(f).y+c,a=f.clientHeight,void((r<(i=f.scrollTop)||i+a<r+25)&&(f.scrollTop=r<i?r:r-a+25))}o=s.getViewPort(e.getWin()),r=s.getPos(t).y+c,i=o.y,a=o.h,(r<o.y||i+a<r+25)&&e.getWin().scrollTo(0,r<i?r:r-a+25)}},up=function(d,e){te(yu.fromRangeStart(e).getClientRects()).each(function(e){var t,n,r,o,i,a,u,s,l,c=function(e){if(e.inline)return e.getBody().getBoundingClientRect();var t=e.getWin();return{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight,width:t.innerWidth,height:t.innerHeight}}(d),f={x:(i=t=c,a=n=e,a.left>i.left&&a.right<i.right?0:a.left<i.left?a.left-i.left:a.right-i.right),y:(r=t,o=n,o.top>r.top&&o.bottom<r.bottom?0:o.top<r.top?o.top-r.top:o.bottom-r.bottom)};s=0!==f.x?0<f.x?f.x+4:f.x-4:0,l=0!==f.y?0<f.y?f.y+4:f.y-4:0,(u=d).inline?(u.getBody().scrollLeft+=s,u.getBody().scrollTop+=l):u.getWin().scrollBy(s,l)})},sp=function(e){return Bo.isContentEditableTrue(e)||Bo.isContentEditableFalse(e)},lp=function(e,t,n){var r,o,i,a,u,s=n;if(s.caretPositionFromPoint)(o=s.caretPositionFromPoint(e,t))&&((r=n.createRange()).setStart(o.offsetNode,o.offset),r.collapse(!0));else if(n.caretRangeFromPoint)r=n.caretRangeFromPoint(e,t);else if(s.body.createTextRange){r=s.body.createTextRange();try{r.moveToPoint(e,t),r.collapse(!0)}catch(l){r=function(e,n,t){var r,o,i;if(r=t.elementFromPoint(e,n),o=t.body.createTextRange(),r&&"HTML"!==r.tagName||(r=t.body),o.moveToElementText(r),0<(i=(i=Gt.toArray(o.getClientRects())).sort(function(e,t){return(e=Math.abs(Math.max(e.top-n,e.bottom-n)))-(t=Math.abs(Math.max(t.top-n,t.bottom-n)))})).length){n=(i[0].bottom+i[0].top)/2;try{return o.moveToPoint(e,n),o.collapse(!0),o}catch(a){}}return null}(e,t,n)}return i=r,a=n.body,u=i&&i.parentElement?i.parentElement():null,Bo.isContentEditableFalse(function(e,t,n){for(;e&&e!==t;){if(n(e))return e;e=e.parentNode}return null}(u,a,sp))?null:i}return r},cp=function(n,e){return K(e,function(e){var t=n.fire("GetSelectionRange",{range:e});return t.range!==e?t.range:e})},fp=function(e,t){var n=(t||j.document).createDocumentFragment();return L(e,function(e){n.appendChild(e.dom())}),or.fromDom(n)},dp=Sr("element","width","rows"),hp=Sr("element","cells"),mp=Sr("x","y"),gp=function(e,t){var n=parseInt(wr(e,t),10);return isNaN(n)?1:n},pp=function(e){return I(e,function(e,t){return t.cells().length>e?t.cells().length:e},0)},vp=function(e,t){for(var n=e.rows(),r=0;r<n.length;r++)for(var o=n[r].cells(),i=0;i<o.length;i++)if(Or(o[i],t))return T.some(mp(i,r));return T.none()},bp=function(e,t,n,r,o){for(var i=[],a=e.rows(),u=n;u<=o;u++){var s=a[u].cells(),l=t<r?s.slice(t,r+1):s.slice(r,t+1);i.push(hp(a[u].element(),l))}return i},yp=function(e){var o=dp(fa(e),0,[]);return L(Ki(e,"tr"),function(n,r){L(Ki(n,"td,th"),function(e,t){!function(e,t,n,r,o){for(var i=gp(o,"rowspan"),a=gp(o,"colspan"),u=e.rows(),s=n;s<n+i;s++){u[s]||(u[s]=hp(da(r),[]));for(var l=t;l<t+a;l++)u[s].cells()[l]=s===n&&l===t?o:fa(o)}}(o,function(e,t,n){for(;r=t,o=n,i=void 0,((i=e.rows())[o]?i[o].cells():[])[r];)t++;var r,o,i;return t}(o,t,r),r,n,e)})}),dp(o.element(),pp(o.rows()),o.rows())},Cp=function(e){return n=K((t=e).rows(),function(e){var t=K(e.cells(),function(e){var t=da(e);return xr(t,"colspan"),xr(t,"rowspan"),t}),n=fa(e.element());return _i(n,t),n}),r=fa(t.element()),o=or.fromTag("tbody"),_i(o,n),Bi(r,o),r;var t,n,r,o},wp=function(c,e,t){return vp(c,e).bind(function(l){return vp(c,t).map(function(e){return t=c,r=e,o=(n=l).x(),i=n.y(),a=r.x(),u=r.y(),s=i<u?bp(t,o,i,a,u):bp(t,o,u,a,i),dp(t.element(),pp(s),s);var t,n,r,o,i,a,u,s})})},xp=function(e){var t=[];if(e)for(var n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));return t},Np=xp,Ep=function(e){return J(e,function(e){var t=Ka(e);return t?[or.fromDom(t)]:[]})},zp=function(e){return 1<xp(e).length},Sp=function(e){return V(Ep(e),Co)},kp=function(e){return Ki(e,"td[data-mce-selected],th[data-mce-selected]")},Tp=function(e,t){var n=kp(t),r=Sp(e);return 0<n.length?n:r},Ap=Tp,Rp=function(e){return Tp(Np(e.selection.getSel()),or.fromDom(e.getBody()))},Dp=function(n,t){return F(n,function(e){return"li"===sr(e)&&zd(e,t)}).fold($([]),function(e){return(t=n,F(t,function(e){return"ul"===sr(e)||"ol"===sr(e)})).map(function(e){return[or.fromTag("li"),or.fromTag(sr(e))]}).getOr([]);var t})},Mp=function(e,t){var n,r=or.fromDom(t.commonAncestorContainer),o=pd(r,e),i=V(o,function(e){return ho(e)||co(e)}),a=Dp(o,t),u=i.concat(a.length?a:vo(n=r)?Lr(n).filter(po).fold($([]),function(e){return[n,e]}):po(n)?[n]:[]);return K(u,fa)},Bp=function(){return fp([])},_p=function(e,t){return n=or.fromDom(t.cloneContents()),r=Mp(e,t),o=I(r,function(e,t){return Bi(t,e),t},n),0<r.length?fp([o]):o;var n,r,o},Op=function(e,o){return(t=e,n=o[0],Qi(n,"table",d(Or,t))).bind(function(e){var t=o[0],n=o[o.length-1],r=yp(e);return wp(r,t,n).map(function(e){return fp([Cp(e)])})}).getOrThunk(Bp);var t,n},Hp=function(e,t){var n,r,o=Ap(t,e);return 0<o.length?Op(e,o):(n=e,0<(r=t).length&&r[0].collapsed?Bp():_p(n,r[0]))},Pp=function(e,t){if(void 0===t&&(t={}),t.get=!0,t.format=t.format||"html",t.selection=!0,(t=e.fire("BeforeGetContent",t)).isDefaultPrevented())return e.fire("GetContent",t),t.content;if("text"===t.format)return l=e,T.from(l.selection.getRng()).map(function(e){var t=l.dom.add(l.getBody(),"div",{"data-mce-bogus":"all",style:"overflow: hidden; opacity: 0;"},e.cloneContents()),n=va(t.innerText);return l.dom.remove(t),n}).getOr("");t.getInner=!0;var n,r,o,i,a,u,s,l,c=(r=t,i=(n=e).selection.getRng(),a=n.dom.create("body"),u=n.selection.getSel(),s=cp(n,Np(u)),(o=r.contextual?Hp(or.fromDom(n.getBody()),s).dom():i.cloneContents())&&a.appendChild(o),n.selection.serializer.serialize(a,r));return"tree"===t.format?c:(t.content=e.selection.isCollapsed()?"":c,e.fire("GetContent",t),t.content)},Lp=function(e,t,n){return null!==function(e,t,n){for(;e&&e!==t;){if(n(e))return e;e=e.parentNode}return null}(e,t,n)},Vp=function(e,t,n){return Lp(e,t,function(e){return e.nodeName===n})},Ip=function(e){return e&&"TABLE"===e.nodeName},Fp=function(e,t,n){for(var r=new io(t,e.getParent(t.parentNode,e.isBlock)||e.getRoot());t=r[n?"prev":"next"]();)if(Bo.isBr(t))return!0},Up=function(e,t,n,r,o){var i,a,u,s,l,c,f=e.getRoot(),d=e.schema.getNonEmptyElements();if(u=e.getParent(o.parentNode,e.isBlock)||f,r&&Bo.isBr(o)&&t&&e.isEmpty(u))return T.some(yu(o.parentNode,e.nodeIndex(o)));for(i=new io(o,u);s=i[r?"prev":"next"]();){if("false"===e.getContentEditableParent(s)||(c=f,xa(l=s)&&!1===Lp(l,c,$u)))return T.none();if(Bo.isText(s)&&0<s.nodeValue.length)return!1===Vp(s,f,"A")?T.some(yu(s,r?s.nodeValue.length:0)):T.none();if(e.isBlock(s)||d[s.nodeName.toLowerCase()])return T.none();a=s}return n&&a?T.some(yu(a,0)):T.none()},jp=function(e,t,n,r){var o,i,a,u,s,l,c,f,d,h,m=e.getRoot(),g=!1;if(o=r[(n?"start":"end")+"Container"],i=r[(n?"start":"end")+"Offset"],c=Bo.isElement(o)&&i===o.childNodes.length,s=e.schema.getNonEmptyElements(),l=n,xa(o))return T.none();if(Bo.isElement(o)&&i>o.childNodes.length-1&&(l=!1),Bo.isDocument(o)&&(o=m,i=0),o===m){if(l&&(u=o.childNodes[0<i?i-1:0])){if(xa(u))return T.none();if(s[u.nodeName]||Ip(u))return T.none()}if(o.hasChildNodes()){if(i=Math.min(!l&&0<i?i-1:i,o.childNodes.length-1),o=o.childNodes[i],i=Bo.isText(o)&&c?o.data.length:0,!t&&o===m.lastChild&&Ip(o))return T.none();if(function(e,t){for(;t&&t!==e;){if(Bo.isContentEditableFalse(t))return!0;t=t.parentNode}return!1}(m,o)||xa(o))return T.none();if(o.hasChildNodes()&&!1===Ip(o)){a=new io(u=o,m);do{if(Bo.isContentEditableFalse(u)||xa(u)){g=!1;break}if(Bo.isText(u)&&0<u.nodeValue.length){i=l?0:u.nodeValue.length,o=u,g=!0;break}if(s[u.nodeName.toLowerCase()]&&(!(f=u)||!/^(TD|TH|CAPTION)$/.test(f.nodeName))){i=e.nodeIndex(u),o=u.parentNode,l||i++,g=!0;break}}while(u=l?a.next():a.prev())}}}return t&&(Bo.isText(o)&&0===i&&Up(e,c,t,!0,o).each(function(e){o=e.container(),i=e.offset(),g=!0}),Bo.isElement(o)&&((u=o.childNodes[i])||(u=o.childNodes[i-1]),!u||!Bo.isBr(u)||(h="A",(d=u).previousSibling&&d.previousSibling.nodeName===h)||Fp(e,u,!1)||Fp(e,u,!0)||Up(e,c,t,!0,u).each(function(e){o=e.container(),i=e.offset(),g=!0}))),l&&!t&&Bo.isText(o)&&i===o.nodeValue.length&&Up(e,c,t,!1,o).each(function(e){o=e.container(),i=e.offset(),g=!0}),g?T.some(yu(o,i)):T.none()},qp=function(e,t){var n=t.collapsed,r=t.cloneRange(),o=yu.fromRangeStart(t);return jp(e,n,!0,r).each(function(e){n&&yu.isAbove(o,e)||r.setStart(e.container(),e.offset())}),n||jp(e,n,!1,r).each(function(e){r.setEnd(e.container(),e.offset())}),n&&r.collapse(!0),xd(t,r)?T.none():T.some(r)},$p=function(e){return 0===e.dom().length?(Hi(e),T.none()):T.some(e)},Wp=function(e,t,n){var r,o;if(o=t,(r=(r=n)||{format:"html"}).set=!0,r.selection=!0,r.content=o,(n=r).no_events||!(n=e.fire("BeforeSetContent",n)).isDefaultPrevented()){var i=e.selection.getRng();!function(r,e){var t=T.from(e.firstChild).map(or.fromDom),n=T.from(e.lastChild).map(or.fromDom);r.deleteContents(),r.insertNode(e);var o=t.bind(Vr).filter(fr).bind($p),i=n.bind(Ir).filter(fr).bind($p);Ja([o,t.filter(fr)],function(e,t){var n,r;n=t.dom(),r=e.dom().data,n.insertData(0,r),Hi(e)}),Ja([i,n.filter(fr)],function(e,t){var n=t.dom().length;t.dom().appendData(e.dom().data),r.setEnd(t.dom(),n),Hi(e)}),r.collapse(!1)}(i,i.createContextualFragment(n.content)),e.selection.setRng(i),up(e,i),n.no_events||e.fire("SetContent",n)}else e.fire("SetContent",n)},Kp=function(e,t,n,r,o){var i=n?t.startContainer:t.endContainer,a=n?t.startOffset:t.endOffset;return T.from(i).map(or.fromDom).map(function(e){return r&&t.collapsed?e:qr(e,o(e,a)).getOr(e)}).bind(function(e){return cr(e)?T.some(e):Lr(e)}).map(function(e){return e.dom()}).getOr(e)},Xp=function(e,t,n){return Kp(e,t,!0,n,function(e,t){return Math.min(e.dom().childNodes.length,t)})},Yp=function(e,t,n){return Kp(e,t,!1,n,function(e,t){return 0<t?t-1:t})},Gp=function(e,t){for(var n=e;e&&Bo.isText(e)&&0===e.length;)e=t?e.nextSibling:e.previousSibling;return e||n},Jp=function(e,t,n){if(e&&e.hasOwnProperty(t)){var r=V(e[t],function(e){return e!==n});0===r.length?delete e[t]:e[t]=r}},Qp=function(e){return!!e.select},Zp=function(e){return!(!e||!e.ownerDocument)&&Hr(or.fromDom(e.ownerDocument),or.fromDom(e))},ev=function(u,s,e,l){var n,t,c,f,r=function p(i,n){var a,u;return{selectorChangedWithUnbind:function(e,t){return a||(a={},u={},n.on("NodeChange",function(e){var n=e.element,r=i.getParents(n,null,i.getRoot()),o={};Gt.each(a,function(e,n){Gt.each(r,function(t){if(i.is(t,n))return u[n]||(Gt.each(e,function(e){e(!0,{node:t,selector:n,parents:r})}),u[n]=e),o[n]=e,!1})}),Gt.each(u,function(e,t){o[t]||(delete u[t],Gt.each(e,function(e){e(!1,{node:n,selector:t,parents:r})}))})})),a[e]||(a[e]=[]),a[e].push(t),{unbind:function(){Jp(a,e,t),Jp(u,e,t)}}}}}(u,l).selectorChangedWithUnbind,o=function(e,t){return Wp(l,e,t)},i=function(e){var t=h();t.collapse(!!e),a(t)},d=function(){return s.getSelection?s.getSelection():s.document.selection},h=function(){var e,t,n,r,o=function(e,t,n){try{return t.compareBoundaryPoints(e,n)}catch(r){return-1}};if(!s)return null;if(null==(r=s.document))return null;if(l.bookmark!==undefined&&!1===rf(l)){var i=Yc(l);if(i.isSome())return i.map(function(e){return cp(l,[e])[0]}).getOr(r.createRange())}try{(e=d())&&(t=0<e.rangeCount?e.getRangeAt(0):e.createRange?e.createRange():r.createRange())}catch(a){}return(t=cp(l,[t])[0])||(t=r.createRange?r.createRange():r.body.createTextRange()),t.setStart&&9===t.startContainer.nodeType&&t.collapsed&&(n=u.getRoot(),t.setStart(n,0),t.setEnd(n,0)),c&&f&&(0===o(t.START_TO_START,t,c)&&0===o(t.END_TO_END,t,c)?t=f:f=c=null),t},a=function(e,t){var n,r;if((o=e)&&(Qp(o)||Zp(o.startContainer)&&Zp(o.endContainer))){var o,i=Qp(e)?e:null;if(i){f=null;try{i.select()}catch(a){}}else{if(n=d(),e=l.fire("SetSelectionRange",{range:e,forward:t}).range,n){f=e;try{n.removeAllRanges(),n.addRange(e)}catch(a){}!1===t&&n.extend&&(n.collapse(e.endContainer,e.endOffset),n.extend(e.startContainer,e.startOffset)),c=0<n.rangeCount?n.getRangeAt(0):null}e.collapsed||e.startContainer!==e.endContainer||!n.setBaseAndExtent||he.ie||e.endOffset-e.startOffset<2&&e.startContainer.hasChildNodes()&&(r=e.startContainer.childNodes[e.startOffset])&&"IMG"===r.tagName&&(n.setBaseAndExtent(e.startContainer,e.startOffset,e.endContainer,e.endOffset),n.anchorNode===e.startContainer&&n.focusNode===e.endContainer||n.setBaseAndExtent(r,0,r,1)),l.fire("AfterSetSelectionRange",{range:e,forward:t})}}},m=function(){var e,t,n=d();return!(n&&n.anchorNode&&n.focusNode)||((e=u.createRng()).setStart(n.anchorNode,n.anchorOffset),e.collapse(!0),(t=u.createRng()).setStart(n.focusNode,n.focusOffset),t.collapse(!0),e.compareBoundaryPoints(e.START_TO_START,t)<=0)},g={bookmarkManager:null,controlSelection:null,dom:u,win:s,serializer:e,editor:l,collapse:i,setCursorLocation:function(e,t){var n=u.createRng();e?(n.setStart(e,t),n.setEnd(e,t),a(n),i(!1)):(Sd(u,n,l.getBody(),!0),a(n))},getContent:function(e){return Pp(l,e)},setContent:o,getBookmark:function(e,t){return n.getBookmark(e,t)},moveToBookmark:function(e){return n.moveToBookmark(e)},select:function(e,t){var r,n,o;return(r=u,n=e,o=t,T.from(n).map(function(e){var t=r.nodeIndex(e),n=r.createRng();return n.setStart(e.parentNode,t),n.setEnd(e.parentNode,t+1),o&&(Sd(r,n,e,!0),Sd(r,n,e,!1)),n})).each(a),e},isCollapsed:function(){var e=h(),t=d();return!(!e||e.item)&&(e.compareEndPoints?0===e.compareEndPoints("StartToEnd",e):!t||e.collapsed)},isForward:m,setNode:function(e){return o(u.getOuterHTML(e)),e},getNode:function(){return e=l.getBody(),(t=h())?(r=t.startContainer,o=t.endContainer,i=t.startOffset,a=t.endOffset,n=t.commonAncestorContainer,!t.collapsed&&(r===o&&a-i<2&&r.hasChildNodes()&&(n=r.childNodes[i]),3===r.nodeType&&3===o.nodeType&&(r=r.length===i?Gp(r.nextSibling,!0):r.parentNode,o=0===a?Gp(o.previousSibling,!1):o.parentNode,r&&r===o))?r:n&&3===n.nodeType?n.parentNode:n):e;var e,t,n,r,o,i,a},getSel:d,setRng:a,getRng:h,getStart:function(e){return Xp(l.getBody(),h(),e)},getEnd:function(e){return Yp(l.getBody(),h(),e)},getSelectedBlocks:function(e,t){return function(e,t,n,r){var o,i,a=[];if(i=e.getRoot(),n=e.getParent(n||Xp(i,t,t.collapsed),e.isBlock),r=e.getParent(r||Yp(i,t,t.collapsed),e.isBlock),n&&n!==i&&a.push(n),n&&r&&n!==r)for(var u=new io(o=n,i);(o=u.next())&&o!==r;)e.isBlock(o)&&a.push(o);return r&&n!==r&&r!==i&&a.push(r),a}(u,h(),e,t)},normalize:function(){var e=h(),t=d();if(zp(t)||!kd(l))return e;var n=qp(u,e);return n.each(function(e){a(e,m())}),n.getOr(e)},selectorChanged:function(e,t){return r(e,t),g},selectorChangedWithUnbind:r,getScrollContainer:function(){for(var e,t=u.getRoot();t&&"BODY"!==t.nodeName;){if(t.scrollHeight>t.clientHeight){e=t;break}t=t.parentNode}return e},scrollIntoView:function(e,t){return ap(l,e,t)},placeCaretAt:function(e,t){return a(lp(e,t,l.getDoc()))},getBoundingClientRect:function(){var e=h();return e.collapsed?xu.fromRangeStart(e).getClientRects()[0]:e.getBoundingClientRect()},destroy:function(){s=c=f=null,t.destroy()}};return n=tp(g),t=op(g,l),g.bookmarkManager=n,g.controlSelection=t,g},tv=Bo.isText,nv=function(e){return tv(e)&&e.data[0]===pa},rv=function(e){return tv(e)&&e.data[e.data.length-1]===pa},ov=function(e){return e.ownerDocument.createTextNode(pa)},iv=function(e,t){return e?function(e){if(tv(e.previousSibling))return rv(e.previousSibling)||e.previousSibling.appendData(pa),e.previousSibling;if(tv(e))return nv(e)||e.insertData(0,pa),e;var t=ov(e);return e.parentNode.insertBefore(t,e),t}(t):function(e){if(tv(e.nextSibling))return nv(e.nextSibling)||e.nextSibling.insertData(0,pa),e.nextSibling;if(tv(e))return rv(e)||e.appendData(pa),e;var t=ov(e);return e.nextSibling?e.parentNode.insertBefore(t,e.nextSibling):e.parentNode.appendChild(t),t}(t)},av=d(iv,!0),uv=d(iv,!1),sv=function(e,t){return Bo.isText(e.container())?iv(t,e.container()):iv(t,e.getNode())},lv=function(e,t){var n=t.get();return n&&e.container()===n&&wa(n)},cv=function(n,e){return e.fold(function(e){ns.remove(n.get());var t=av(e);return n.set(t),T.some(xu(t,t.length-1))},function(e){return Zs.firstPositionIn(e).map(function(e){if(lv(e,n))return xu(n.get(),1);ns.remove(n.get());var t=sv(e,!0);return n.set(t),xu(t,1)})},function(e){return Zs.lastPositionIn(e).map(function(e){if(lv(e,n))return xu(n.get(),n.get().length-1);ns.remove(n.get());var t=sv(e,!1);return n.set(t),xu(t,t.length-1)})},function(e){ns.remove(n.get());var t=uv(e);return n.set(t),T.some(xu(t,1))})},fv=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,dv=function(e,t){if(!t)return t;var n=t.container(),r=t.offset();return e?wa(n)?Bo.isText(n.nextSibling)?xu(n.nextSibling,0):xu.after(n):Ea(t)?xu(n,r+1):t:wa(n)?Bo.isText(n.previousSibling)?xu(n.previousSibling,n.previousSibling.data.length):xu.before(n):za(t)?xu(n,r-1):t},hv={isInlineTarget:function(e,t){var n=Rf(e,"inline_boundaries_selector").getOr("a[href],code");return Br(or.fromDom(t),n)},findRootInline:function(e,t,n){var r,o,i,a=(r=e,o=t,i=n,V(vi.DOM.getParents(i.container(),"*",o),r));return T.from(a[a.length-1])},isRtl:function(e){return"rtl"===vi.DOM.getStyle(e,"direction",!0)||(t=e.textContent,fv.test(t));var t},isAtZwsp:function(e){return Ea(e)||za(e)},normalizePosition:dv,normalizeForwards:d(dv,!0),normalizeBackwards:d(dv,!1),hasSameParentBlock:function(e,t,n){var r=ys(t,e),o=ys(n,e);return r&&r===o}},mv=function(e,t){for(var n=0;n<e.length;n++){var r=e[n].apply(null,t);if(r.isSome())return r}return T.none()},gv=Mc([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),pv=function(e,t){var n=ys(t,e);return n||e},vv=function(e,t,n){var r=hv.normalizeForwards(n),o=pv(t,r.container());return hv.findRootInline(e,o,r).fold(function(){return Zs.nextPosition(o,r).bind(d(hv.findRootInline,e,o)).map(function(e){return gv.before(e)})},T.none)},bv=function(e,t){return null===Wu(e,t)},yv=function(e,t,n){return hv.findRootInline(e,t,n).filter(d(bv,t))},Cv=function(e,t,n){var r=hv.normalizeBackwards(n);return yv(e,t,r).bind(function(e){return Zs.prevPosition(e,r).isNone()?T.some(gv.start(e)):T.none()})},wv=function(e,t,n){var r=hv.normalizeForwards(n);return yv(e,t,r).bind(function(e){return Zs.nextPosition(e,r).isNone()?T.some(gv.end(e)):T.none()})},xv=function(e,t,n){var r=hv.normalizeBackwards(n),o=pv(t,r.container());return hv.findRootInline(e,o,r).fold(function(){return Zs.prevPosition(o,r).bind(d(hv.findRootInline,e,o)).map(function(e){return gv.after(e)})},T.none)},Nv=function(e){return!1===hv.isRtl(zv(e))},Ev=function(e,t,n){return mv([vv,Cv,wv,xv],[e,t,n]).filter(Nv)},zv=function(e){return e.fold(W,W,W,W)},Sv=function(e){return e.fold($("before"),$("start"),$("end"),$("after"))},kv=function(e){return e.fold(gv.before,gv.before,gv.after,gv.after)},Tv=function(n,e,r,t,o,i){return Ja([hv.findRootInline(e,r,t),hv.findRootInline(e,r,o)],function(e,t){return e!==t&&hv.hasSameParentBlock(r,e,t)?gv.after(n?e:t):i}).getOr(i)},Av=function(e,r){return e.fold($(!0),function(e){return n=r,!(Sv(t=e)===Sv(n)&&zv(t)===zv(n));var t,n})},Rv=function(e,t){return e?t.fold(q(T.some,gv.start),T.none,q(T.some,gv.after),T.none):t.fold(T.none,q(T.some,gv.before),T.none,q(T.some,gv.end))},Dv=function(a,u,s,l){var e=hv.normalizePosition(a,l),c=Ev(u,s,e);return Ev(u,s,e).bind(d(Rv,a)).orThunk(function(){return t=a,n=u,r=s,o=c,e=l,i=hv.normalizePosition(t,e),Zs.fromPosition(t,r,i).map(d(hv.normalizePosition,t)).fold(function(){return o.map(kv)},function(e){return Ev(n,r,e).map(d(Tv,t,n,r,i,e)).filter(d(Av,o))}).filter(Nv);var t,n,r,o,e,i})},Mv=Ev,Bv=Dv,_v=(d(Dv,!1),d(Dv,!0),kv),Ov=function(e){return e.fold(gv.start,gv.start,gv.end,gv.end)},Hv=function(e){return _(e.selection.getSel().modify)},Pv=function(e,t,n){var r=e?1:-1;return t.setRng(xu(n.container(),n.offset()+r).toRange()),t.getSel().modify("move",e?"forward":"backward","word"),!0},Lv=function(e,t){var n=t.selection.getRng(),r=e?xu.fromRangeEnd(n):xu.fromRangeStart(n);return!!Hv(t)&&(e&&Ea(r)?Pv(!0,t.selection,r):!(e||!za(r))&&Pv(!1,t.selection,r))},Vv=function(e,t){var n=e.dom.createRng();n.setStart(t.container(),t.offset()),n.setEnd(t.container(),t.offset()),e.selection.setRng(n)},Iv=function(e){return!1!==e.settings.inline_boundaries},Fv=function(e,t){e?t.setAttribute("data-mce-selected","inline-boundary"):t.removeAttribute("data-mce-selected")},Uv=function(t,e,n){return cv(e,n).map(function(e){return Vv(t,e),n})},jv=function(e,t,n){return function(){return!!Iv(t)&&Lv(e,t)}},qv={move:function(a,u,s){return function(){return!!Iv(a)&&(t=a,n=u,e=s,r=t.getBody(),o=xu.fromRangeStart(t.selection.getRng()),i=d(hv.isInlineTarget,t),Bv(e,i,r,o).bind(function(e){return Uv(t,n,e)})).isSome();var t,n,e,r,o,i}},moveNextWord:d(jv,!0),movePrevWord:d(jv,!1),setupSelectedState:function(a){var u=Ei(null),s=d(hv.isInlineTarget,a);return a.on("NodeChange",function(e){var t,n,r,o,i;Iv(a)&&(t=s,n=a.dom,r=e.parents,o=V(n.select('*[data-mce-selected="inline-boundary"]'),t),i=V(r,t),L(ee(o,i),d(Fv,!1)),L(ee(i,o),d(Fv,!0)),function(e,t){if(e.selection.isCollapsed()&&!0!==e.composing&&t.get()){var n=xu.fromRangeStart(e.selection.getRng());xu.isTextPosition(n)&&!1===hv.isAtZwsp(n)&&(Vv(e,ns.removeAndReposition(t.get(),n)),t.set(null))}}(a,u),function(n,r,o,e){if(r.selection.isCollapsed()){var t=V(e,n);L(t,function(e){var t=xu.fromRangeStart(r.selection.getRng());Mv(n,r.getBody(),t).bind(function(e){return Uv(r,o,e)})})}}(s,a,u,e.parents))}),u},setCaretPosition:Vv},$v=Bo.isContentEditableFalse,Wv=Ka,Kv=function(e,t,n,r){var o=e===Cu.Forwards,i=o?yh:Ch;if(!r.collapsed){var a=Wv(r);if($v(a))return ih(e,t,a,e===Cu.Backwards,!0)}var u=Ca(r.startContainer),s=Ts(e,t.getBody(),r);if(i(s))return ah(t,s.getNode(!o));var l=hv.normalizePosition(o,n(s));if(!l)return u?r:null;if(i(l))return ih(e,t,l.getNode(!o),o,!0);var c=n(l);return c&&i(c)&&Ds(l,c)?ih(e,t,c.getNode(!o),o,!0):u?sh(t,l.toRange(),!0):null},Xv=function(e,t,n,r){var o,i,a,u,s,l,c,f,d;if(d=Wv(r),o=Ts(e,t.getBody(),r),i=n(t.getBody(),Wd(1),o),a=V(i,Kd(1)),s=$t.last(o.getClientRects()),(yh(o)||xh(o))&&(d=o.getNode()),(Ch(o)||Nh(o))&&(d=o.getNode(!0)),!s)return null;if(l=s.left,(u=Zd(a,l))&&$v(u.node))return c=Math.abs(l-u.left),f=Math.abs(l-u.right),ih(e,t,u.node,c<f,!0);if(d){var h=function(e,t,n,r){var o,i,a,u,s,l,c=Us(t),f=[],d=0,h=function(e){return $t.last(e.getClientRects())};l=h(u=1===e?(o=c.next,i=Wa,a=$a,xu.after(r)):(o=c.prev,i=$a,a=Wa,xu.before(r)));do{if(u.isVisible()&&!a(s=h(u),l)){if(0<f.length&&i(s,$t.last(f))&&d++,(s=Ua(s)).position=u,s.line=d,n(s))return f;f.push(s)}}while(u=o(u));return f}(e,t.getBody(),Wd(1),d);if(u=Zd(V(h,Kd(1)),l))return sh(t,u.position.toRange(),!0);if(u=$t.last(V(h,Kd(0))))return sh(t,u.position.toRange(),!0)}},Yv=function(e,t,n){var r,o,i,a,u=Us(e.getBody()),s=d(Rs,u.next),l=d(Rs,u.prev);if(n.collapsed&&e.settings.forced_root_block){if(!(r=e.dom.getParent(n.startContainer,"PRE")))return;(1===t?s(xu.fromRangeStart(n)):l(xu.fromRangeStart(n)))||(a=(i=e).dom.create(nc(i)),(!he.ie||11<=he.ie)&&(a.innerHTML='<br data-mce-bogus="1">'),o=a,1===t?e.$(r).after(o):e.$(r).before(o),e.selection.select(o,!0),e.selection.collapse())}},Gv=function(c,f){return function(){var e,t,n,r,o,i,a,u,s,l=(t=f,r=Us((e=c).getBody()),o=d(Rs,r.next),i=d(Rs,r.prev),a=t?Cu.Forwards:Cu.Backwards,u=t?o:i,s=e.selection.getRng(),(n=Kv(a,e,u,s))?n:(n=Yv(e,a,s))||null);return!!l&&(c.selection.setRng(l),!0)}},Jv=function(u,s){return function(){var e,t,n,r,o,i,a=(r=(t=s)?1:-1,o=t?$d:qd,i=(e=u).selection.getRng(),(n=Xv(r,e,o,i))?n:(n=Yv(e,r,i))||null);return!!a&&(u.selection.setRng(a),!0)}};(ep=Zg||(Zg={}))[ep.Br=0]="Br",ep[ep.Block=1]="Block",ep[ep.Wrap=2]="Wrap",ep[ep.Eol=3]="Eol";var Qv=function(e,t){return e===Cu.Backwards?t.reverse():t},Zv=function(e,t,n,r){for(var o,i,a,u,s,l,c=Us(n),f=r,d=[];f&&(s=c,l=f,o=t===Cu.Forwards?s.next(l):s.prev(l));){if(Bo.isBr(o.getNode(!1)))return t===Cu.Forwards?{positions:Qv(t,d).concat([o]),breakType:Zg.Br,breakAt:T.some(o)}:{positions:Qv(t,d),breakType:Zg.Br,breakAt:T.some(o)};if(o.isVisible()){if(e(f,o)){var h=(i=t,a=f,u=o,Bo.isBr(u.getNode(i===Cu.Forwards))?Zg.Br:!1===Cs(a,u)?Zg.Block:Zg.Wrap);return{positions:Qv(t,d),breakType:h,breakAt:T.some(o)}}d.push(o),f=o}else f=o}return{positions:Qv(t,d),breakType:Zg.Eol,breakAt:T.none()}},eb=function(n,r,o,e){return r(o,e).breakAt.map(function(e){var t=r(o,e).positions;return n===Cu.Backwards?t.concat(e):[e].concat(t)}).getOr([])},tb=function(e,i){return I(e,function(e,o){return e.fold(function(){return T.some(o)},function(r){return Ja([te(r.getClientRects()),te(o.getClientRects())],function(e,t){var n=Math.abs(i-e.left);return Math.abs(i-t.left)<=n?o:r}).or(e)})},T.none())},nb=function(t,e){return te(e.getClientRects()).bind(function(e){return tb(t,e.left)})},rb=d(Zv,yu.isAbove,-1),ob=d(Zv,yu.isBelow,1),ib=d(eb,-1,rb),ab=d(eb,1,ob),ub=function(e,t,n,r,o){var i,a,u,s,l=Ki(or.fromDom(n),"td,th,caption").map(function(e){return e.dom()}),c=V((i=e,J(l,function(e){var t,n,r=(t=Ua(e.getBoundingClientRect()),n=-1,{left:t.left-n,top:t.top-n,right:t.right+2*n,bottom:t.bottom+2*n,width:t.width+n,height:t.height+n});return[{x:r.left,y:i(r),cell:e},{x:r.right,y:i(r),cell:e}]})),function(e){return t(e,o)});return(a=c,u=r,s=o,I(a,function(e,r){return e.fold(function(){return T.some(r)},function(e){var t=Math.sqrt(Math.abs(e.x-u)+Math.abs(e.y-s)),n=Math.sqrt(Math.abs(r.x-u)+Math.abs(r.y-s));return T.some(n<t?r:e)})},T.none())).map(function(e){return e.cell})},sb=d(ub,function(e){return e.bottom},function(e,t){return e.y<t}),lb=d(ub,function(e){return e.top},function(e,t){return e.y>t}),cb=function(t,n){return te(n.getClientRects()).bind(function(e){return sb(t,e.left,e.top)}).bind(function(e){return nb((t=e,Zs.lastPositionIn(t).map(function(e){return rb(t,e).positions.concat(e)}).getOr([])),n);var t})},fb=function(t,n){return ne(n.getClientRects()).bind(function(e){return lb(t,e.left,e.top)}).bind(function(e){return nb((t=e,Zs.firstPositionIn(t).map(function(e){return[e].concat(ob(t,e).positions)}).getOr([])),n);var t})},db=function(e,t){e.selection.setRng(t),up(e,t)},hb=function(e,t,n){var r,o,i,a,u=e(t,n);return(a=u).breakType===Zg.Wrap&&0===a.positions.length||!Bo.isBr(n.getNode())&&(i=u).breakType===Zg.Br&&1===i.positions.length?(r=e,o=t,!u.breakAt.map(function(e){return r(o,e).breakAt.isSome()}).getOr(!1)):u.breakAt.isNone()},mb=d(hb,rb),gb=d(hb,ob),pb=function(e,t,n,r){var o,i,a,u,s=e.selection.getRng(),l=t?1:-1;if(us()&&(o=t,i=s,a=n,u=xu.fromRangeStart(i),Zs.positionIn(!o,a).map(function(e){return e.isEqual(u)}).getOr(!1))){var c=ih(l,e,n,!t,!0);return db(e,c),!0}return!1},vb=function(e,t){var n=t.getNode(e);return Bo.isElement(n)&&"TABLE"===n.nodeName?T.some(n):T.none()},bb=function(u,s,l){var e=vb(!!s,l),t=!1===s;e.fold(function(){return db(u,l.toRange())},function(a){return Zs.positionIn(t,u.getBody()).filter(function(e){return e.isEqual(l)}).fold(function(){return db(u,l.toRange())},function(e){return n=s,o=a,t=l,void((i=nc(r=u))?r.undoManager.transact(function(){var e=or.fromTag(i);Cr(e,rc(r)),Bi(e,or.fromTag("br")),n?Di(or.fromDom(o),e):Ri(or.fromDom(o),e);var t=r.dom.createRng();t.setStart(e.dom(),0),t.setEnd(e.dom(),0),db(r,t)}):db(r,t.toRange()));var n,r,o,t,i})})},yb=function(e,t,n,r){var o,i,a,u,s,l,c=e.selection.getRng(),f=xu.fromRangeStart(c),d=e.getBody();if(t||!mb(r,f))return!(!t||!gb(r,f))&&(o=d,h=fb(i=n,a=f).orThunk(function(){return te(a.getClientRects()).bind(function(e){return tb(ab(o,xu.after(i)),e.left)})}).getOr(xu.after(i)),bb(e,t,h),!0);var h=(u=d,cb(s=n,l=f).orThunk(function(){return te(l.getClientRects()).bind(function(e){return tb(ib(u,xu.before(s)),e.left)})}).getOr(xu.before(s)));return bb(e,t,h),!0},Cb=function(t,n){return function(){return T.from(t.dom.getParent(t.selection.getNode(),"td,th")).bind(function(e){return T.from(t.dom.getParent(e,"table")).map(function(e){return pb(t,n,e)})}).getOr(!1)}},wb=function(n,r){return function(){return T.from(n.dom.getParent(n.selection.getNode(),"td,th")).bind(function(t){return T.from(n.dom.getParent(t,"table")).map(function(e){return yb(n,r,e,t)})}).getOr(!1)}},xb=function(e){return P(["figcaption"],sr(e))},Nb=function(e){var t=j.document.createRange();return t.setStartBefore(e.dom()),t.setEndBefore(e.dom()),t},Eb=function(e,t,n){n?Bi(e,t):Mi(e,t)},zb=function(e,t,n,r){return""===t?(c=e,f=r,d=or.fromTag("br"),Eb(c,d,f),Nb(d)):(o=e,i=r,a=t,u=n,s=or.fromTag(a),l=or.fromTag("br"),Cr(s,u),Bi(s,l),Eb(o,s,i),Nb(l));var o,i,a,u,s,l,c,f,d},Sb=function(e,t,n){return t?(o=e.dom(),ob(o,n).breakAt.isNone()):(r=e.dom(),rb(r,n).breakAt.isNone());var r,o},kb=function(t,n){var e,r,o,i=or.fromDom(t.getBody()),a=xu.fromRangeStart(t.selection.getRng()),u=nc(t),s=rc(t);return(e=a,r=i,o=d(Or,r),Ji(or.fromDom(e.container()),fo,o).filter(xb)).exists(function(){if(Sb(i,n,a)){var e=zb(i,u,s,n);return t.selection.setRng(e),!0}return!1})},Tb=function(e,t){return function(){return!!e.selection.isCollapsed()&&kb(e,t)}},Ab=function(e,r){return J(K(e,function(e){return Pl({shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0,action:o},e)}),function(e){return t=e,(n=r).keyCode===t.keyCode&&n.shiftKey===t.shiftKey&&n.altKey===t.altKey&&n.ctrlKey===t.ctrlKey&&n.metaKey===t.metaKey?[e]:[];var t,n})},Rb=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,r)}},Db=function(e,t){return F(Ab(e,t),function(e){return e.action()})},Mb=function(i,a){i.on("keydown",function(e){var t,n,r,o;!1===e.isDefaultPrevented()&&(t=i,n=a,r=e,o=nr.detect().os,Db([{keyCode:ch.RIGHT,action:Gv(t,!0)},{keyCode:ch.LEFT,action:Gv(t,!1)},{keyCode:ch.UP,action:Jv(t,!1)},{keyCode:ch.DOWN,action:Jv(t,!0)},{keyCode:ch.RIGHT,action:Cb(t,!0)},{keyCode:ch.LEFT,action:Cb(t,!1)},{keyCode:ch.UP,action:wb(t,!1)},{keyCode:ch.DOWN,action:wb(t,!0)},{keyCode:ch.RIGHT,action:qv.move(t,n,!0)},{keyCode:ch.LEFT,action:qv.move(t,n,!1)},{keyCode:ch.RIGHT,ctrlKey:!o.isOSX(),altKey:o.isOSX(),action:qv.moveNextWord(t,n)},{keyCode:ch.LEFT,ctrlKey:!o.isOSX(),altKey:o.isOSX(),action:qv.movePrevWord(t,n)},{keyCode:ch.UP,action:Tb(t,!1)},{keyCode:ch.DOWN,action:Tb(t,!0)}],r).each(function(e){r.preventDefault()}))})},Bb=function(e,t){return Hr(e,t)?Ji(t,function(e){return go(e)||vo(e)},(n=e,function(e){return Or(n,or.fromDom(e.dom().parentNode))})):T.none();var n},_b=function(e){var t,n,r;e.dom.isEmpty(e.getBody())&&(e.setContent(""),n=(t=e).getBody(),r=n.firstChild&&t.dom.isBlock(n.firstChild)?n.firstChild:n,t.selection.setCursorLocation(r,0))},Ob=function(i,a,u){return Ja([Zs.firstPositionIn(u),Zs.lastPositionIn(u)],function(e,t){var n=hv.normalizePosition(!0,e),r=hv.normalizePosition(!1,t),o=hv.normalizePosition(!1,a);return i?Zs.nextPosition(u,o).map(function(e){return e.isEqual(r)&&a.isEqual(n)}).getOr(!1):Zs.prevPosition(u,o).map(function(e){return e.isEqual(n)&&a.isEqual(r)}).getOr(!1)}).getOr(!0)},Hb=Sr("block","position"),Pb=Sr("from","to"),Lb=function(e,t){var n=or.fromDom(e),r=or.fromDom(t.container());return Bb(n,r).map(function(e){return Hb(e,t)})},Vb=function(o,i,e){var t=Lb(o,xu.fromRangeStart(e)),n=t.bind(function(e){return Zs.fromPosition(i,o,e.position()).bind(function(e){return Lb(o,e).map(function(e){return t=o,n=i,r=e,Bo.isBr(r.position().getNode())&&!1===dm(r.block())?Zs.positionIn(!1,r.block().dom()).bind(function(e){return e.isEqual(r.position())?Zs.fromPosition(n,t,e).bind(function(e){return Lb(t,e)}):T.some(r)}).getOr(r):r;var t,n,r})})});return Ja([t,n],Pb).filter(function(e){return!1===Or((r=e).from().block(),r.to().block())&&Lr((n=e).from().block()).bind(function(t){return Lr(n.to().block()).filter(function(e){return Or(t,e)})}).isSome()&&(t=e,!1===Bo.isContentEditableFalse(t.from().block())&&!1===Bo.isContentEditableFalse(t.to().block()));var t,n,r})},Ib=function(e,t,n){return n.collapsed?Vb(e,t,n):T.none()},Fb=function(e){var t,n=(t=jr(e),U(t,fo).fold(function(){return t},function(e){return t.slice(0,e)}));return L(n,Hi),n},Ub=function(e,t){var n=pd(t,e);return F(n.reverse(),dm).each(Hi)},jb=function(e,t,n,r){if(dm(n))return Xh(n),Zs.firstPositionIn(n.dom());0===V(Fr(r),function(e){return!dm(e)}).length&&dm(t)&&Ri(r,or.fromTag("br"));var o=Zs.prevPosition(n.dom(),xu.before(r.dom()));return L(Fb(t),function(e){Ri(r,e)}),Ub(e,t),o},qb=function(e,t,n){if(dm(n))return Hi(n),dm(t)&&Xh(t),Zs.firstPositionIn(t.dom());var r=Zs.lastPositionIn(n.dom());return L(Fb(t),function(e){Bi(n,e)}),Ub(e,t),r},$b=function(e,t){return Hr(t,e)?(n=pd(e,t),T.from(n[n.length-1])):T.none();var n},Wb=function(e,t){Zs.positionIn(e,t.dom()).map(function(e){return e.getNode()}).map(or.fromDom).filter(mo).each(Hi)},Kb=function(e,t,n){return Wb(!0,t),Wb(!1,n),$b(t,n).fold(d(qb,e,t,n),d(jb,e,t,n))},Xb=function(e,t,n,r){return t?Kb(e,r,n):Kb(e,n,r)},Yb=function(t,n){var e,r=or.fromDom(t.getBody());return(e=Ib(r.dom(),n,t.selection.getRng()).bind(function(e){return Xb(r,n,e.from().block(),e.to().block())})).each(function(e){t.selection.setRng(e.toRange())}),e.isSome()},Gb=function(e,t){var n=or.fromDom(t),r=d(Or,e);return Gi(n,Co,r).isSome()},Jb=function(e,t){var n,r,o=Zs.prevPosition(e.dom(),xu.fromRangeStart(t)).isNone(),i=Zs.nextPosition(e.dom(),xu.fromRangeEnd(t)).isNone();return!(Gb(n=e,(r=t).startContainer)||Gb(n,r.endContainer))&&o&&i},Qb=function(e){var n,r,o,t,i=or.fromDom(e.getBody()),a=e.selection.getRng();return Jb(i,a)?((t=e).setContent(""),t.selection.setCursorLocation(),!0):(n=i,r=e.selection,o=r.getRng(),Ja([Bb(n,or.fromDom(o.startContainer)),Bb(n,or.fromDom(o.endContainer))],function(e,t){return!1===Or(e,t)&&(o.deleteContents(),Xb(n,!0,e,t).each(function(e){r.setRng(e.toRange())}),!0)}).getOr(!1))},Zb=function(e,t){return!e.selection.isCollapsed()&&Qb(e)},ey=function(e){return As(e).exists(mo)},ty=function(e,t,n){var r=V(pd(or.fromDom(n.container()),t),fo),o=te(r).getOr(t);return Zs.fromPosition(e,o.dom(),n).filter(ey)},ny=function(e,t){return As(t).exists(mo)||ty(!0,e,t).isSome()},ry=function(e,t){return(n=t,T.from(n.getNode(!0)).map(or.fromDom)).exists(mo)||ty(!1,e,t).isSome();var n},oy=d(ty,!1),iy=d(ty,!0),ay=Mc([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),uy=function(e,t,n,r){var o=r.getNode(!1===t);return Bb(or.fromDom(e),or.fromDom(n.getNode())).map(function(e){return dm(e)?ay.remove(e.dom()):ay.moveToElement(o)}).orThunk(function(){return T.some(ay.moveToElement(o))})},sy=function(u,s,l){return Zs.fromPosition(s,u,l).bind(function(e){return a=e.getNode(),Co(or.fromDom(a))||vo(or.fromDom(a))?T.none():(t=u,o=e,i=function(e){return ho(or.fromDom(e))&&!Cs(r,o,t)},ks(!(n=s),r=l).fold(function(){return ks(n,o).fold($(!1),i)},i)?T.none():s&&Bo.isContentEditableFalse(e.getNode())?uy(u,s,l,e):!1===s&&Bo.isContentEditableFalse(e.getNode(!0))?uy(u,s,l,e):s&&Ch(l)?T.some(ay.moveToPosition(e)):!1===s&&yh(l)?T.some(ay.moveToPosition(e)):T.none());var t,n,r,o,i,a})},ly=function(r,e,o){return i=e,a=o.getNode(!1===i),u=i?"after":"before",Bo.isElement(a)&&a.getAttribute("data-mce-caret")===u?(t=e,n=o.getNode(!1===e),t&&Bo.isContentEditableFalse(n.nextSibling)?T.some(ay.moveToElement(n.nextSibling)):!1===t&&Bo.isContentEditableFalse(n.previousSibling)?T.some(ay.moveToElement(n.previousSibling)):T.none()).fold(function(){return sy(r,e,o)},T.some):sy(r,e,o).bind(function(e){return t=r,n=o,e.fold(function(e){return T.some(ay.remove(e))},function(e){return T.some(ay.moveToElement(e))},function(e){return Cs(n,e,t)?T.none():T.some(ay.moveToPosition(e))});var t,n});var t,n,i,a,u},cy=function(a,u){var e,t,n,r,o,i;return(e=a.getBody(),t=u,n=a.selection.getRng(),r=Ss(t?1:-1,e,n),o=xu.fromRangeStart(r),i=or.fromDom(e),!1===t&&Ch(o)?T.some(ay.remove(o.getNode(!0))):t&&yh(o)?T.some(ay.remove(o.getNode())):!1===t&&yh(o)&&ry(i,o)?oy(i,o).map(function(e){return ay.remove(e.getNode())}):t&&Ch(o)&&ny(i,o)?iy(i,o).map(function(e){return ay.remove(e.getNode())}):ly(e,t,o)).map(function(e){return e.fold((o=a,i=u,function(e){return o._selectionOverrides.hideFakeCaret(),Nm(o,i,or.fromDom(e)),!0}),(n=a,r=u,function(e){var t=r?xu.before(e):xu.after(e);return n.selection.setRng(t.toRange()),!0}),(t=a,function(e){return t.selection.setRng(e.toRange()),!0}));var t,n,r,o,i}).getOr(!1)},fy=function(e,t){var n,r=e.selection.getNode();return!!Bo.isContentEditableFalse(r)&&(n=or.fromDom(e.getBody()),L(Ki(n,".mce-offscreen-selection"),Hi),Nm(e,t,or.fromDom(e.selection.getNode())),_b(e),!0)},dy=function(e,t){return e.selection.isCollapsed()?cy(e,t):fy(e,t)},hy=function(e){var t,n=function(e,t){for(;t&&t!==e;){if(Bo.isContentEditableTrue(t)||Bo.isContentEditableFalse(t))return t;t=t.parentNode}return null}(e.getBody(),e.selection.getNode());return Bo.isContentEditableTrue(n)&&e.dom.isBlock(n)&&e.dom.isEmpty(n)&&(t=e.dom.create("br",{"data-mce-bogus":"1"}),e.dom.setHTML(n,""),n.appendChild(t),e.selection.setRng(xu.before(t).toRange())),!0},my=function(e,t,n,r,o,i){var a,u,s=ih(r,e,i.getNode(!o),o,!0);if(t.collapsed){var l=t.cloneRange();o?l.setEnd(s.startContainer,s.startOffset):l.setStart(s.endContainer,s.endOffset),l.deleteContents()}else t.deleteContents();return e.selection.setRng(s),a=e.dom,u=n,Bo.isText(u)&&0===u.data.length&&a.remove(u),!0},gy=function(e,t){return function(e,t){var n=e.selection.getRng();if(!Bo.isText(n.commonAncestorContainer))return!1;var r=t?Cu.Forwards:Cu.Backwards,o=Us(e.getBody()),i=d(Rs,o.next),a=d(Rs,o.prev),u=t?i:a,s=t?yh:Ch,l=Ts(r,e.getBody(),n),c=hv.normalizePosition(t,u(l));if(!c)return!1;if(s(c))return my(e,n,l.getNode(),r,t,c);var f=u(c);return!!(f&&s(f)&&Ds(c,f))&&my(e,n,l.getNode(),r,t,f)}(e,t)},py=function(t,n){return function(e){return cv(n,e).map(function(e){return qv.setCaretPosition(t,e),!0}).getOr(!1)}},vy=function(r,o,i,a){var u=r.getBody(),s=d(hv.isInlineTarget,r);r.undoManager.ignore(function(){var e,t,n;r.selection.setRng((e=i,t=a,(n=j.document.createRange()).setStart(e.container(),e.offset()),n.setEnd(t.container(),t.offset()),n)),r.execCommand("Delete"),Mv(s,u,xu.fromRangeStart(r.selection.getRng())).map(Ov).map(py(r,o))}),r.nodeChanged()},by=function(n,r,i,o){var e,t,a=(e=n.getBody(),t=o.container(),ys(t,e)||e),u=d(hv.isInlineTarget,n),s=Mv(u,a,o);return s.bind(function(e){return i?e.fold($(T.some(Ov(e))),T.none,$(T.some(_v(e))),T.none):e.fold(T.none,$(T.some(_v(e))),T.none,$(T.some(Ov(e))))}).map(py(n,r)).getOrThunk(function(){var t=Zs.navigate(i,a,o),e=t.bind(function(e){return Mv(u,a,e)});return s.isSome()&&e.isSome()?hv.findRootInline(u,a,o).map(function(e){return o=e,!!Ja([Zs.firstPositionIn(o),Zs.lastPositionIn(o)],function(e,t){var n=hv.normalizePosition(!0,e),r=hv.normalizePosition(!1,t);return Zs.nextPosition(o,n).map(function(e){return e.isEqual(r)}).getOr(!0)}).getOr(!0)&&(Nm(n,i,or.fromDom(e)),!0);var o}).getOr(!1):e.bind(function(e){return t.map(function(e){return i?vy(n,r,o,e):vy(n,r,e,o),!0})}).getOr(!1)})},yy=function(e,t,n){if(e.selection.isCollapsed()&&!1!==e.settings.inline_boundaries){var r=xu.fromRangeStart(e.selection.getRng());return by(e,t,n,r)}return!1},Cy=function(e){return 1===jr(e).length},wy=function(e,t,n,r){var o,i,a,u,s=d(Om,t),l=K(V(r,s),function(e){return e.dom()});if(0===l.length)Nm(t,e,n);else{var c=(o=n.dom(),i=l,a=Tm(!1),u=Bm(i,a.dom()),Ri(or.fromDom(o),a),Hi(or.fromDom(o)),xu(u,0));t.selection.setRng(c.toRange())}},xy=function(r,o){var t,e=or.fromDom(r.getBody()),n=or.fromDom(r.selection.getStart()),i=V((t=pd(n,e),U(t,fo).fold($(t),function(e){return t.slice(0,e)})),Cy);return ne(i).map(function(e){var t,n=xu.fromRangeStart(r.selection.getRng());return!(!Ob(o,n,e.dom())||$u((t=e).dom())&&Sm(t.dom())||(wy(o,r,e,i),0))}).getOr(!1)},Ny=function(e,t){return!!e.selection.isCollapsed()&&xy(e,t)},Ey=Sr("start","end"),zy=Sr("rng","table","cells"),Sy=Mc([{removeTable:["element"]},{emptyCells:["cells"]}]),ky=function(e,t){return ea(or.fromDom(e),"td,th",t)},Ty=function(e,t){return Qi(e,"table",t)},Ay=function(e){return!1===Or(e.start(),e.end())},Ry=function(e,n){return Ty(e.start(),n).bind(function(t){return Ty(e.end(),n).bind(function(e){return Or(t,e)?T.some(t):T.none()})})},Dy=function(e){return Ki(e,"td,th")},My=function(r,e){var t=ky(e.startContainer,r),n=ky(e.endContainer,r);return e.collapsed?T.none():Ja([t,n],Ey).fold(function(){return t.fold(function(){return n.bind(function(t){return Ty(t,r).bind(function(e){return te(Dy(e)).map(function(e){return Ey(e,t)})})})},function(t){return Ty(t,r).bind(function(e){return ne(Dy(e)).map(function(e){return Ey(t,e)})})})},function(e){return By(r,e)?T.none():(n=r,Ty((t=e).start(),n).bind(function(e){return ne(Dy(e)).map(function(e){return Ey(t.start(),e)})}));var t,n})},By=function(e,t){return Ry(t,e).isSome()},_y=function(e,t){var n,r,o,i,a=d(Or,e);return(n=t,r=a,o=ky(n.startContainer,r),i=ky(n.endContainer,r),Ja([o,i],Ey).filter(Ay).filter(function(e){return By(r,e)}).orThunk(function(){return My(r,n)})).bind(function(e){return Ry(t=e,a).map(function(e){return zy(t,e,Dy(e))});var t})},Oy=function(e,t){return U(e,function(e){return Or(e,t)})},Hy=function(n){return(r=n,Ja([Oy(r.cells(),r.rng().start()),Oy(r.cells(),r.rng().end())],function(e,t){return r.cells().slice(e,t+1)})).map(function(e){var t=n.cells();return e.length===t.length?Sy.removeTable(n.table()):Sy.emptyCells(e)});var r},Py=function(e,t){return _y(e,t).bind(Hy)},Ly=function(e,t){return L(t,Xh),e.selection.setCursorLocation(t[0].dom(),0),!0},Vy=function(e,t){return Nm(e,!1,t),!0},Iy=function(n,e,r,t){return Uy(e,t).fold(function(){return t=n,Py(e,r).map(function(e){return e.fold(d(Vy,t),d(Ly,t))});var t},function(e){return jy(n,e)}).getOr(!1)},Fy=function(e,t){return F(pd(t,e),Co)},Uy=function(e,t){return F(pd(t,e),function(e){return"caption"===sr(e)})},jy=function(e,t){return Xh(t),e.selection.setCursorLocation(t.dom(),0),T.some(!0)},qy=function(u,s,l,c,f){return Zs.navigate(l,u.getBody(),f).bind(function(e){return r=c,o=l,i=f,a=e,Zs.firstPositionIn(r.dom()).bind(function(t){return Zs.lastPositionIn(r.dom()).map(function(e){return o?i.isEqual(t)&&a.isEqual(e):i.isEqual(e)&&a.isEqual(t)})}).getOr(!0)?jy(u,c):(t=c,n=e,Uy(s,or.fromDom(n.getNode())).map(function(e){return!1===Or(e,t)}));var t,n,r,o,i,a}).or(T.some(!0))},$y=function(a,u,s,e){var l=xu.fromRangeStart(a.selection.getRng());return Fy(s,e).bind(function(e){return dm(e)?jy(a,e):(t=a,n=s,r=u,o=e,i=l,Zs.navigate(r,t.getBody(),i).bind(function(e){return Fy(n,or.fromDom(e.getNode())).map(function(e){return!1===Or(e,o)})}));var t,n,r,o,i})},Wy=function(a,u,e){var s=or.fromDom(a.getBody());return Uy(s,e).fold(function(){return $y(a,u,s,e)},function(e){return t=a,n=u,r=s,o=e,i=xu.fromRangeStart(t.selection.getRng()),dm(o)?jy(t,o):qy(t,r,n,o,i);var t,n,r,o,i}).getOr(!1)},Ky=function(e,t){var n,r,o,i,a,u=or.fromDom(e.selection.getStart(!0)),s=Rp(e);return e.selection.isCollapsed()&&0===s.length?Wy(e,t,u):(n=e,r=u,o=or.fromDom(n.getBody()),i=n.selection.getRng(),0!==(a=Rp(n)).length?Ly(n,a):Iy(n,o,i,r))},Xy=function(o,i){o.on("keydown",function(e){var t,n,r;!1===e.isDefaultPrevented()&&(t=o,n=i,r=e,Db([{keyCode:ch.BACKSPACE,action:Rb(dy,t,!1)},{keyCode:ch.DELETE,action:Rb(dy,t,!0)},{keyCode:ch.BACKSPACE,action:Rb(gy,t,!1)},{keyCode:ch.DELETE,action:Rb(gy,t,!0)},{keyCode:ch.BACKSPACE,action:Rb(yy,t,n,!1)},{keyCode:ch.DELETE,action:Rb(yy,t,n,!0)},{keyCode:ch.BACKSPACE,action:Rb(Ky,t,!1)},{keyCode:ch.DELETE,action:Rb(Ky,t,!0)},{keyCode:ch.BACKSPACE,action:Rb(Zb,t,!1)},{keyCode:ch.DELETE,action:Rb(Zb,t,!0)},{keyCode:ch.BACKSPACE,action:Rb(Yb,t,!1)},{keyCode:ch.DELETE,action:Rb(Yb,t,!0)},{keyCode:ch.BACKSPACE,action:Rb(Ny,t,!1)},{keyCode:ch.DELETE,action:Rb(Ny,t,!0)}],r).each(function(e){r.preventDefault()}))}),o.on("keyup",function(e){var t,n;!1===e.isDefaultPrevented()&&(t=o,n=e,Db([{keyCode:ch.BACKSPACE,action:Rb(hy,t)},{keyCode:ch.DELETE,action:Rb(hy,t)}],n))})},Yy=function(e){return T.from(e.dom.getParent(e.selection.getStart(!0),e.dom.isBlock))},Gy=function(e,t){var n,r,o,i=t,a=e.dom,u=e.schema.getMoveCaretBeforeOnEnterElements();if(t){if(/^(LI|DT|DD)$/.test(t.nodeName)){var s=function(e){for(;e;){if(1===e.nodeType||3===e.nodeType&&e.data&&/[\r\n\s]/.test(e.data))return e;e=e.nextSibling}}(t.firstChild);s&&/^(UL|OL|DL)$/.test(s.nodeName)&&t.insertBefore(a.doc.createTextNode("\xa0"),t.firstChild)}if(o=a.createRng(),t.normalize(),t.hasChildNodes()){for(n=new io(t,t);r=n.current();){if(Bo.isText(r)){o.setStart(r,0),o.setEnd(r,0);break}if(u[r.nodeName.toLowerCase()]){o.setStartBefore(r),o.setEndBefore(r);break}i=r,r=n.next()}r||(o.setStart(i,0),o.setEnd(i,0))}else Bo.isBr(t)?t.nextSibling&&a.isBlock(t.nextSibling)?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)):(o.setStart(t,0),o.setEnd(t,0));e.selection.setRng(o),a.remove(void 0),e.selection.scrollIntoView(t)}},Jy=function(e,t){var n,r,o=e.getRoot();for(n=t;n!==o&&"false"!==e.getContentEditable(n);)"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},Qy=Yy,Zy=function(e){return Yy(e).fold($(""),function(e){return e.nodeName.toUpperCase()})},eC=function(e){return Yy(e).filter(function(e){return vo(or.fromDom(e))}).isSome()},tC=function(e,t){return e&&e.parentNode&&e.parentNode.nodeName===t},nC=function(e){return e&&/^(OL|UL|LI)$/.test(e.nodeName)},rC=function(e){var t=e.parentNode;return/^(LI|DT|DD)$/.test(t.nodeName)?t:e},oC=function(e,t,n){for(var r=e[n?"firstChild":"lastChild"];r&&!Bo.isElement(r);)r=r[n?"nextSibling":"previousSibling"];return r===t},iC=function(e,t,n,r,o){var i=e.dom,a=e.selection.getRng();if(n!==e.getBody()){var u;nC(u=n)&&nC(u.parentNode)&&(o="LI");var s,l,c=o?t(o):i.create("BR");if(oC(n,r,!0)&&oC(n,r,!1))tC(n,"LI")?i.insertAfter(c,rC(n)):i.replace(c,n);else if(oC(n,r,!0))tC(n,"LI")?(i.insertAfter(c,rC(n)),c.appendChild(i.doc.createTextNode(" ")),c.appendChild(n)):n.parentNode.insertBefore(c,n);else if(oC(n,r,!1))i.insertAfter(c,rC(n));else{n=rC(n);var f=a.cloneRange();f.setStartAfter(r),f.setEndAfter(n);var d=f.extractContents();"LI"===o&&(l="LI",(s=d).firstChild&&s.firstChild.nodeName===l)?(c=d.firstChild,i.insertAfter(d,n)):(i.insertAfter(d,n),i.insertAfter(c,n))}i.remove(r),Gy(e,c)}},aC=function(e){e.innerHTML='<br data-mce-bogus="1">'},uC=function(e,t){return e.nodeName===t||e.previousSibling&&e.previousSibling.nodeName===t},sC=function(e,t){return t&&e.isBlock(t)&&!/^(TD|TH|CAPTION|FORM)$/.test(t.nodeName)&&!/^(fixed|absolute)/i.test(t.style.position)&&"true"!==e.getContentEditable(t)},lC=function(e,t,n){return!1===Bo.isText(t)?n:e?1===n&&t.data.charAt(n-1)===pa?0:n:n===t.data.length-1&&t.data.charAt(n)===pa?t.data.length:n},cC=function(e,t){var n,r,o=e.getRoot();for(n=t;n!==o&&"false"!==e.getContentEditable(n);)"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},fC=function(e,t){var n=nc(e);n&&n.toLowerCase()===t.tagName.toLowerCase()&&e.dom.setAttribs(t,rc(e))},dC=function(a,e){var t,u,s,i,l,n,r,o,c,f,d,h,m,g=a.dom,p=a.schema,v=p.getNonEmptyElements(),b=a.selection.getRng(),y=function(e){var t,n,r,o=s,i=p.getTextInlineElements();if(e||"TABLE"===f||"HR"===f?(t=g.create(e||h),fC(a,t)):t=l.cloneNode(!1),r=t,!1===ac(a))g.setAttrib(t,"style",null),g.setAttrib(t,"class",null);else do{if(i[o.nodeName]){if($u(o))continue;n=o.cloneNode(!1),g.setAttrib(n,"id",""),t.hasChildNodes()?n.appendChild(t.firstChild):r=n,t.appendChild(n)}}while((o=o.parentNode)&&o!==u);return aC(r),t},C=function(e){var t,n,r,o;if(o=lC(e,s,i),Bo.isText(s)&&(e?0<o:o<s.nodeValue.length))return!1;if(s.parentNode===l&&m&&!e)return!0;if(e&&Bo.isElement(s)&&s===l.firstChild)return!0;if(uC(s,"TABLE")||uC(s,"HR"))return m&&!e||!m&&e;for(t=new io(s,l),Bo.isText(s)&&(e&&0===o?t.prev():e||o!==s.nodeValue.length||t.next());n=t.current();){if(Bo.isElement(n)){if(!n.getAttribute("data-mce-bogus")&&(r=n.nodeName.toLowerCase(),v[r]&&"br"!==r))return!1}else if(Bo.isText(n)&&!/^[ \t\r\n]*$/.test(n.nodeValue))return!1;e?t.prev():t.next()}return!0},w=function(){r=/^(H[1-6]|PRE|FIGURE)$/.test(f)&&"HGROUP"!==d?y(h):y(),uc(a)&&sC(g,c)&&g.isEmpty(l)?r=g.split(c,l):g.insertAfter(r,l),Gy(a,r)};qp(g,b).each(function(e){b.setStart(e.startContainer,e.startOffset),b.setEnd(e.endContainer,e.endOffset)}),s=b.startContainer,i=b.startOffset,h=nc(a),n=!(!e||!e.shiftKey);var x,N,E,z,S,k,T=!(!e||!e.ctrlKey);Bo.isElement(s)&&s.hasChildNodes()&&(m=i>s.childNodes.length-1,s=s.childNodes[Math.min(i,s.childNodes.length-1)]||s,i=m&&Bo.isText(s)?s.nodeValue.length:0),(u=cC(g,s))&&((h&&!n||!h&&n)&&(s=function(e,t,n,r,o){var i,a,u,s,l,c,f,d=t||"P",h=e.dom,m=cC(h,r);if(!(a=h.getParent(r,h.isBlock))||!sC(h,a)){if(c=(a=a||m)===e.getBody()||(f=a)&&/^(TD|TH|CAPTION)$/.test(f.nodeName)?a.nodeName.toLowerCase():a.parentNode.nodeName.toLowerCase(),!a.hasChildNodes())return i=h.create(d),fC(e,i),a.appendChild(i),n.setStart(i,0),n.setEnd(i,0),i;for(s=r;s.parentNode!==a;)s=s.parentNode;for(;s&&!h.isBlock(s);)s=(u=s).previousSibling;if(u&&e.schema.isValidChild(c,d.toLowerCase())){for(i=h.create(d),fC(e,i),u.parentNode.insertBefore(i,u),s=u;s&&!h.isBlock(s);)l=s.nextSibling,i.appendChild(s),s=l;n.setStart(r,o),n.setEnd(r,o)}}return r}(a,h,b,s,i)),l=g.getParent(s,g.isBlock),c=l?g.getParent(l.parentNode,g.isBlock):null,f=l?l.nodeName.toUpperCase():"","LI"!==(d=c?c.nodeName.toUpperCase():"")||T||(c=(l=c).parentNode,f=d),/^(LI|DT|DD)$/.test(f)&&g.isEmpty(l)?iC(a,y,c,l,h):h&&l===a.getBody()||(h=h||"P",Ca(l)?(r=Aa(l),g.isEmpty(l)&&aC(l),Gy(a,r)):C()?w():C(!0)?(r=l.parentNode.insertBefore(y(),l),Gy(a,uC(l,"HR")?r:l)):((t=(S=b,k=S.cloneRange(),k.setStart(S.startContainer,lC(!0,S.startContainer,S.startOffset)),k.setEnd(S.endContainer,lC(!1,S.endContainer,S.endOffset)),k).cloneRange()).setEndAfter(l),o=t.extractContents(),z=o,L(Wi(or.fromDom(z),fr),function(e){var t=e.dom();t.nodeValue=va(t.nodeValue)}),function(e){for(;Bo.isText(e)&&(e.nodeValue=e.nodeValue.replace(/^[\r\n]+/,"")),e=e.firstChild;);}(o),r=o.firstChild,g.insertAfter(o,l),function(e,t,n){var r,o=n,i=[];if(o){for(;o=o.firstChild;){if(e.isBlock(o))return;Bo.isElement(o)&&!t[o.nodeName.toLowerCase()]&&i.push(o)}for(r=i.length;r--;)!(o=i[r]).hasChildNodes()||o.firstChild===o.lastChild&&""===o.firstChild.nodeValue?e.remove(o):(a=e,(u=o)&&"A"===u.nodeName&&a.isEmpty(u)&&e.remove(o));var a,u}}(g,v,r),x=g,(N=l).normalize(),(E=N.lastChild)&&!/^(left|right)$/gi.test(x.getStyle(E,"float",!0))||x.add(N,"br"),g.isEmpty(l)&&aC(l),r.normalize(),g.isEmpty(r)?(g.remove(r),w()):Gy(a,r)),g.setAttrib(r,"id",""),a.fire("NewBlock",{newBlock:r})))},hC=function(e,t,n){var r=e.create("span",{},"&nbsp;");n.parentNode.insertBefore(r,n),t.scrollIntoView(r),e.remove(r)},mC=function(e,t,n,r){var o=e.createRng();r?(o.setStartBefore(n),o.setEndBefore(n)):(o.setStartAfter(n),o.setEndAfter(n)),t.setRng(o)},gC=function(e,t){var n,r,o=e.selection,i=e.dom,a=o.getRng();qp(i,a).each(function(e){a.setStart(e.startContainer,e.startOffset),a.setEnd(e.endContainer,e.endOffset)});var u=a.startOffset,s=a.startContainer;if(1===s.nodeType&&s.hasChildNodes()){var l=u>s.childNodes.length-1;s=s.childNodes[Math.min(u,s.childNodes.length-1)]||s,u=l&&3===s.nodeType?s.nodeValue.length:0}var c=i.getParent(s,i.isBlock),f=c?i.getParent(c.parentNode,i.isBlock):null,d=f?f.nodeName.toUpperCase():"",h=!(!t||!t.ctrlKey);"LI"!==d||h||(c=f),s&&3===s.nodeType&&u>=s.nodeValue.length&&(function(e,t,n){for(var r,o=new io(t,n),i=e.getNonEmptyElements();r=o.next();)if(i[r.nodeName.toLowerCase()]||0<r.length)return!0}(e.schema,s,c)||(n=i.create("br"),a.insertNode(n),a.setStartAfter(n),a.setEndAfter(n),r=!0)),n=i.create("br"),Bu(i,a,n),hC(i,o,n),mC(i,o,n,r),e.undoManager.add()},pC=function(e,t){var n=or.fromTag("br");Ri(or.fromDom(t),n),e.undoManager.add()},vC=function(e,t){bC(e.getBody(),t)||Di(or.fromDom(t),or.fromTag("br"));var n=or.fromTag("br");Di(or.fromDom(t),n),hC(e.dom,e.selection,n.dom()),mC(e.dom,e.selection,n.dom(),!1),e.undoManager.add()},bC=function(e,t){return n=xu.after(t),!!Bo.isBr(n.getNode())||Zs.nextPosition(e,xu.after(t)).map(function(e){return Bo.isBr(e.getNode())}).getOr(!1);var n},yC=function(e){return e&&"A"===e.nodeName&&"href"in e},CC=function(e){return e.fold($(!1),yC,yC,$(!1))},wC=function(e,t){t.fold(o,d(pC,e),d(vC,e),o)},xC=function(e,t){var n,r,o,i=(n=e,r=d(hv.isInlineTarget,n),o=xu.fromRangeStart(n.selection.getRng()),Mv(r,n.getBody(),o).filter(CC));i.isSome()?i.each(d(wC,e)):gC(e,t)},NC=function(e,t){return Qy(e).filter(function(e){return 0<t.length&&Br(or.fromDom(e),t)}).isSome()},EC=function(e){return NC(e,oc(e))},zC=function(e){return NC(e,ic(e))},SC=Mc([{br:[]},{block:[]},{none:[]}]),kC=function(e,t){return zC(e)},TC=function(n){return function(e,t){return""===nc(e)===n}},AC=function(n){return function(e,t){return eC(e)===n}},RC=function(n,r){return function(e,t){return Zy(e)===n.toUpperCase()===r}},DC=function(e){return RC("pre",e)},MC=function(n){return function(e,t){return tc(e)===n}},BC=function(e,t){return EC(e)},_C=function(e,t){return t},OC=function(e){var t=nc(e),n=Jy(e.dom,e.selection.getStart());return n&&e.schema.isValidChild(n.nodeName,t||"P")},HC=function(e,t){return function(n,r){return I(e,function(e,t){return e&&t(n,r)},!0)?T.some(t):T.none()}},PC=function(e,t){return mv([HC([kC],SC.none()),HC([RC("summary",!0)],SC.br()),HC([DC(!0),MC(!1),_C],SC.br()),HC([DC(!0),MC(!1)],SC.block()),HC([DC(!0),MC(!0),_C],SC.block()),HC([DC(!0),MC(!0)],SC.br()),HC([AC(!0),_C],SC.br()),HC([AC(!0)],SC.block()),HC([TC(!0),_C,OC],SC.block()),HC([TC(!0)],SC.br()),HC([BC],SC.br()),HC([TC(!1),_C],SC.br()),HC([OC],SC.block())],[e,!(!t||!t.shiftKey)]).getOr(SC.none())},LC=function(e,t){PC(e,t).fold(function(){xC(e,t)},function(){dC(e,t)},o)},VC=function(o){o.on("keydown",function(e){var t,n,r;e.keyCode===ch.ENTER&&(t=o,(n=e).isDefaultPrevented()||(n.preventDefault(),(r=t.undoManager).typing&&(r.typing=!1,r.add()),t.undoManager.transact(function(){!1===t.selection.isCollapsed()&&t.execCommand("Delete"),LC(t,n)})))})},IC=function(n,r){var e=r.container(),t=r.offset();return Bo.isText(e)?(e.insertData(t,n),T.some(yu(e,t+n.length))):As(r).map(function(e){var t=or.fromText(n);return r.isAtEnd()?Di(e,t):Ri(e,t),yu(t.dom(),n.length)})},FC=d(IC,"\xa0"),UC=d(IC," "),jC=function(e,t,n){return Zs.navigateIgnore(e,t,n,vh)},qC=function(t,n,r){var e=V(pd(or.fromDom(r.container()),n),fo);return te(e).fold(function(){return jC(t,n.dom(),r).forall(function(e){return!1===Cs(e,r,n.dom())})},function(e){return jC(t,e.dom(),r).isNone()})},$C=d(qC,!1),WC=d(qC,!0),KC=function(e){return yu.isTextPosition(e)&&!e.isAtStart()&&!e.isAtEnd()},XC=function(e,t){var n=V(pd(or.fromDom(t.container()),e),fo);return te(n).getOr(e)},YC=function(e,t){return KC(t)?ph(t):ph(t)||Zs.prevPosition(XC(e,t).dom(),t).exists(ph)},GC=function(e,t){return KC(t)?gh(t):gh(t)||Zs.nextPosition(XC(e,t).dom(),t).exists(gh)},JC=function(e){return As(e).bind(function(e){return Ji(e,cr)}).exists(function(e){return t=Nr(e,"white-space"),P(["pre","pre-line","pre-wrap"],t);var t})},QC=function(e,t){return o=e,i=t,Zs.prevPosition(o.dom(),i).isNone()||(n=e,r=t,Zs.nextPosition(n.dom(),r).isNone())||$C(e,t)||WC(e,t)||ry(e,t)||ny(e,t);var n,r,o,i},ZC=function(e,t){var n,r,o,i=(r=(n=t).container(),o=n.offset(),Bo.isText(r)&&o<r.data.length?yu(r,o+1):n);return!JC(i)&&(WC(e,i)||ny(e,i)||GC(e,i))},ew=function(e,t){return n=e,!JC(r=t)&&($C(n,r)||ry(n,r)||YC(n,r))||ZC(e,t);var n,r},tw=function(e,t){return fh(e.charAt(t))},nw=function(e){var t=e.container();return Bo.isText(t)&&Kn(t.data,"\xa0")},rw=function(e){var t=e.data,n=K(t.split(""),function(e,t,n){return fh(e)&&0<t&&t<n.length-1&&hh(n[t-1])&&hh(n[t+1])?" ":e}).join("");return n!==t&&(e.data=n,!0)},ow=function(c,e){return T.some(e).filter(nw).bind(function(e){var t,n,r,o,i,a,u,s,l=e.container();return i=c,u=(a=l).data,s=yu(a,0),tw(u,0)&&!ew(i,s)&&(a.data=" "+u.slice(1),1)||rw(l)||(t=c,r=(n=l).data,o=yu(n,r.length-1),tw(r,r.length-1)&&!ew(t,o)&&(n.data=r.slice(0,-1)+" ",1))?T.some(e):T.none()})},iw=function(t){var e=or.fromDom(t.getBody());t.selection.isCollapsed()&&ow(e,yu.fromRangeStart(t.selection.getRng())).each(function(e){t.selection.setRng(e.toRange())})},aw=function(r,o){return function(e){return t=r,!JC(n=e)&&(QC(t,n)||YC(t,n)||GC(t,n))?FC(o):UC(o);var t,n}},uw=function(e){var t,n,r=xu.fromRangeStart(e.selection.getRng()),o=or.fromDom(e.getBody());if(e.selection.isCollapsed()){var i=d(hv.isInlineTarget,e),a=xu.fromRangeStart(e.selection.getRng());return Mv(i,e.getBody(),a).bind((n=o,function(e){return e.fold(function(e){return Zs.prevPosition(n.dom(),xu.before(e))},function(e){return Zs.firstPositionIn(e)},function(e){return Zs.lastPositionIn(e)},function(e){return Zs.nextPosition(n.dom(),xu.after(e))})})).bind(aw(o,r)).exists((t=e,function(e){return t.selection.setRng(e.toRange()),t.nodeChanged(),!0}))}return!1},sw=function(r){r.on("keydown",function(e){var t,n;!1===e.isDefaultPrevented()&&(t=r,n=e,Db([{keyCode:ch.SPACEBAR,action:Rb(uw,t)}],n).each(function(e){n.preventDefault()}))})},lw=function(e,t){var n;t.hasAttribute("data-mce-caret")&&(Aa(t),(n=e).selection.setRng(n.selection.getRng()),e.selection.scrollIntoView(t))},cw=function(e,t){var n,r=(n=e,Zi(or.fromDom(n.getBody()),"*[data-mce-caret]").fold($(null),function(e){return e.dom()}));if(r)return"compositionstart"===t.type?(t.preventDefault(),t.stopPropagation(),void lw(e,r)):void(Na(r)&&(lw(e,r),e.undoManager.add()))},fw=function(e){e.on("keyup compositionstart",d(cw,e))},dw=nr.detect().browser,hw=function(t){var e,n;e=t,n=Li(function(){e.composing||iw(e)},0),dw.isIE()&&(e.on("keypress",function(e){n.throttle()}),e.on("remove",function(e){n.cancel()})),t.on("input",function(e){!1===e.isComposing&&iw(t)})},mw=function(e){var t=qv.setupSelectedState(e);fw(e),Mb(e,t),Xy(e,t),VC(e),sw(e),hw(e)};function gw(u){var s,n,r,o=Gt.each,l=ch.BACKSPACE,c=ch.DELETE,f=u.dom,d=u.selection,e=u.settings,t=u.parser,i=he.gecko,a=he.ie,h=he.webkit,m="data:text/mce-internal,",g=a?"Text":"URL",p=function(e,t){try{u.getDoc().execCommand(e,!1,t)}catch(n){}},v=function(e){return e.isDefaultPrevented()},b=function(){u.shortcuts.add("meta+a",null,"SelectAll")},y=function(){u.on("keydown",function(e){if(!v(e)&&e.keyCode===l&&d.isCollapsed()&&0===d.getRng().startOffset){var t=d.getNode().previousSibling;if(t&&t.nodeName&&"table"===t.nodeName.toLowerCase())return e.preventDefault(),!1}})},C=function(){u.inline||(u.contentStyles.push("body {min-height: 150px}"),u.on("click",function(e){var t;if("HTML"===e.target.nodeName){if(11<he.ie)return void u.getBody().focus();t=u.selection.getRng(),u.getBody().focus(),u.selection.setRng(t),u.selection.normalize(),u.nodeChanged()}}))};return u.on("keydown",function(e){var t,n,r,o,i;if(!v(e)&&e.keyCode===ch.BACKSPACE&&(n=(t=d.getRng()).startContainer,r=t.startOffset,o=f.getRoot(),i=n,t.collapsed&&0===r)){for(;i&&i.parentNode&&i.parentNode.firstChild===i&&i.parentNode!==o;)i=i.parentNode;"BLOCKQUOTE"===i.tagName&&(u.formatter.toggle("blockquote",null,i),(t=f.createRng()).setStart(n,0),t.setEnd(n,0),d.setRng(t))}}),s=function(e){var t=f.create("body"),n=e.cloneContents();return t.appendChild(n),d.serializer.serialize(t,{format:"html"})},u.on("keydown",function(e){var t,n,r,o,i,a=e.keyCode;if(!v(e)&&(a===c||a===l)){if(t=u.selection.isCollapsed(),n=u.getBody(),t&&!f.isEmpty(n))return;if(!t&&(r=u.selection.getRng(),o=s(r),(i=f.createRng()).selectNode(u.getBody()),o!==s(i)))return;e.preventDefault(),u.setContent(""),n.firstChild&&f.isBlock(n.firstChild)?u.selection.setCursorLocation(n.firstChild,0):u.selection.setCursorLocation(n,0),u.nodeChanged()}}),he.windowsPhone||u.on("keyup focusin mouseup",function(e){ch.modifierPressed(e)||d.normalize()},!0),h&&(u.inline||f.bind(u.getDoc(),"mousedown mouseup",function(e){var t;if(e.target===u.getDoc().documentElement)if(t=d.getRng(),u.getBody().focus(),"mousedown"===e.type){if(xa(t.startContainer))return;d.placeCaretAt(e.clientX,e.clientY)}else d.setRng(t)}),u.on("click",function(e){var t=e.target;/^(IMG|HR)$/.test(t.nodeName)&&"false"!==f.getContentEditableParent(t)&&(e.preventDefault(),u.selection.select(t),u.nodeChanged()),"A"===t.nodeName&&f.hasClass(t,"mce-item-anchor")&&(e.preventDefault(),d.select(t))}),e.forced_root_block&&u.on("init",function(){p("DefaultParagraphSeparator",nc(u))}),u.on("init",function(){u.dom.bind(u.getBody(),"submit",function(e){e.preventDefault()})}),y(),t.addNodeFilter("br",function(e){for(var t=e.length;t--;)"Apple-interchange-newline"===e[t].attr("class")&&e[t].remove()}),he.iOS?(u.inline||u.on("keydown",function(){j.document.activeElement===j.document.body&&u.getWin().focus()}),C(),u.on("click",function(e){var t=e.target;do{if("A"===t.tagName)return void e.preventDefault()}while(t=t.parentNode)}),u.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")):b()),11<=he.ie&&(C(),y()),he.ie&&(b(),p("AutoUrlDetect",!1),u.on("dragstart",function(e){var t,n,r;(t=e).dataTransfer&&(u.selection.isCollapsed()&&"IMG"===t.target.tagName&&d.select(t.target),0<(n=u.selection.getContent()).length&&(r=m+escape(u.id)+","+escape(n),t.dataTransfer.setData(g,r)))}),u.on("drop",function(e){if(!v(e)){var t=(i=e).dataTransfer&&(a=i.dataTransfer.getData(g))&&0<=a.indexOf(m)?(a=a.substr(m.length).split(","),{id:unescape(a[0]),html:unescape(a[1])}):null;if(t&&t.id!==u.id){e.preventDefault();var n=lp(e.x,e.y,u.getDoc());d.setRng(n),r=t.html,o=!0,u.queryCommandSupported("mceInsertClipboardContent")?u.execCommand("mceInsertClipboardContent",!1,{content:r,internal:o}):u.execCommand("mceInsertContent",!1,r)}}var r,o,i,a})),i&&(u.on("keydown",function(e){if(!v(e)&&e.keyCode===l){if(!u.getBody().getElementsByTagName("hr").length)return;if(d.isCollapsed()&&0===d.getRng().startOffset){var t=d.getNode(),n=t.previousSibling;if("HR"===t.nodeName)return f.remove(t),void e.preventDefault();n&&n.nodeName&&"hr"===n.nodeName.toLowerCase()&&(f.remove(n),e.preventDefault())}}}),j.Range.prototype.getClientRects||u.on("mousedown",function(e){if(!v(e)&&"HTML"===e.target.nodeName){var t=u.getBody();t.blur(),be.setEditorTimeout(u,function(){t.focus()})}}),n=function(){var e=f.getAttribs(d.getStart().cloneNode(!1));return function(){var t=d.getStart();t!==u.getBody()&&(f.setAttrib(t,"style",null),o(e,function(e){t.setAttributeNode(e.cloneNode(!0))}))}},r=function(){return!d.isCollapsed()&&f.getParent(d.getStart(),f.isBlock)!==f.getParent(d.getEnd(),f.isBlock)},u.on("keypress",function(e){var t;if(!v(e)&&(8===e.keyCode||46===e.keyCode)&&r())return t=n(),u.getDoc().execCommand("delete",!1,null),t(),e.preventDefault(),!1}),f.bind(u.getDoc(),"cut",function(e){var t;!v(e)&&r()&&(t=n(),be.setEditorTimeout(u,function(){t()}))}),e.readonly||u.on("BeforeExecCommand MouseDown",function(){p("StyleWithCSS",!1),p("enableInlineTableEditing",!1),e.object_resizing||p("enableObjectResizing",!1)}),u.on("SetContent ExecCommand",function(e){"setcontent"!==e.type&&"mceInsertLink"!==e.command||o(f.select("a"),function(e){var t=e.parentNode,n=f.getRoot();if(t.lastChild===e){for(;t&&!f.isBlock(t);){if(t.parentNode.lastChild!==t||t===n)return;t=t.parentNode}f.add(t,"br",{"data-mce-bogus":1})}})}),u.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}"),he.mac&&u.on("keydown",function(e){!ch.metaKeyPressed(e)||e.shiftKey||37!==e.keyCode&&39!==e.keyCode||(e.preventDefault(),u.selection.getSel().modify("move",37===e.keyCode?"backward":"forward","lineboundary"))}),y()),{refreshContentEditable:function(){},isHidden:function(){var e;return!i||u.removed?0:!(e=u.selection.getSel())||!e.rangeCount||0===e.rangeCount}}}var pw=function(e){return Bo.isElement(e)&&go(or.fromDom(e))},vw=function(t){t.on("click",function(e){3<=e.detail&&function(e){var t=e.selection.getRng(),n=yu.fromRangeStart(t),r=yu.fromRangeEnd(t);if(yu.isElementPosition(n)){var o=n.container();pw(o)&&Zs.firstPositionIn(o).each(function(e){return t.setStart(e.container(),e.offset())})}yu.isElementPosition(r)&&(o=n.container(),pw(o)&&Zs.lastPositionIn(o).each(function(e){return t.setEnd(e.container(),e.offset())})),e.selection.setRng(gg(t))}(t)})},bw=function(e){var t,n;(t=e).on("click",function(e){t.dom.getParent(e.target,"details")&&e.preventDefault()}),(n=e).parser.addNodeFilter("details",function(e){L(e,function(e){e.attr("data-mce-open",e.attr("open")),e.attr("open","open")})}),n.serializer.addNodeFilter("details",function(e){L(e,function(e){var t=e.attr("data-mce-open");e.attr("open",A(t)?t:null),e.attr("data-mce-open",null)})})},yw=vi.DOM,Cw=function(e){var t;e.bindPendingEventDelegates(),e.initialized=!0,e.fire("init"),e.focus(!0),e.nodeChanged({initial:!0}),e.execCallback("init_instance_callback",e),(t=e).settings.auto_focus&&be.setEditorTimeout(t,function(){var e;(e=!0===t.settings.auto_focus?t:t.editorManager.get(t.settings.auto_focus)).destroyed||e.focus()},100)},ww=function(t,e){var n,r,u,o,i,a,s,l,c,f=t.settings,d=t.getElement(),h=t.getDoc();f.inline||(t.getElement().style.visibility=t.orgVisibility),e||t.inline||(h.open(),h.write(t.iframeHTML),h.close()),t.inline&&(t.on("remove",function(){var e=this.getBody();yw.removeClass(e,"mce-content-body"),yw.removeClass(e,"mce-edit-focus"),yw.setAttrib(e,"contentEditable",null)}),yw.addClass(d,"mce-content-body"),t.contentDocument=h=f.content_document||j.document,t.contentWindow=f.content_window||j.window,t.bodyElement=d,t.contentAreaContainer=d,f.content_document=f.content_window=null,f.root_name=d.nodeName.toLowerCase()),(n=t.getBody()).disabled=!0,t.readonly=f.readonly,t.readonly||(t.inline&&"static"===yw.getStyle(n,"position",!0)&&(n.style.position="relative"),n.contentEditable=t.getParam("content_editable_state",!0)),n.disabled=!1,t.editorUpload=dd(t),t.schema=oi(f),t.dom=vi(h,{keep_values:!0,url_converter:t.convertURL,url_converter_scope:t,hex_colors:f.force_hex_style_colors,class_filter:f.class_filter,update_styles:!0,root_element:t.inline?t.getBody():null,collect:function(){return t.inline},schema:t.schema,contentCssCors:bc(t),onSetAttrib:function(e){t.fire("SetAttrib",e)}}),t.parser=((o=Wg((u=t).settings,u.schema)).addAttributeFilter("src,href,style,tabindex",function(e,t){for(var n,r,o,i=e.length,a=u.dom;i--;)if(r=(n=e[i]).attr(t),o="data-mce-"+t,!n.attributes.map[o]){if(0===r.indexOf("data:")||0===r.indexOf("blob:"))continue;"style"===t?((r=a.serializeStyle(a.parseStyle(r),n.name)).length||(r=null),n.attr(o,r),n.attr(t,r)):"tabindex"===t?(n.attr(o,r),n.attr(t,null)):n.attr(o,u.convertURL(r,t,n.name))}}),o.addNodeFilter("script",function(e){for(var t,n,r=e.length;r--;)0!==(n=(t=e[r]).attr("type")||"no/type").indexOf("mce-")&&t.attr("type","mce-"+n)}),o.addNodeFilter("#cdata",function(e){for(var t,n=e.length;n--;)(t=e[n]).type=8,t.name="#comment",t.value="[CDATA["+t.value+"]]"}),o.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",function(e){for(var t,n=e.length,r=u.schema.getNonEmptyElements();n--;)(t=e[n]).isEmpty(r)&&0===t.getAll("br").length&&(t.append(new Fl("br",1)).shortEnded=!0)}),o),t.serializer=Jg(f,t),t.selection=ev(t.dom,t.getWin(),t.serializer,t),t.annotator=_l(t),t.formatter=Rg(t),t.undoManager=qh(t),t._nodeChangeDispatcher=new Td(t),t._selectionOverrides=kh(t),bw(t),vw(t),mw(t),wd(t),t.fire("PreInit"),f.browser_spellcheck||f.gecko_spellcheck||(h.body.spellcheck=!1,yw.setAttrib(n,"spellcheck","false")),t.quirks=gw(t),t.fire("PostRender"),f.directionality&&(n.dir=f.directionality),f.protect&&t.on("BeforeSetContent",function(t){Gt.each(f.protect,function(e){t.content=t.content.replace(e,function(e){return"\x3c!--mce:protected "+escape(e)+"--\x3e"})})}),t.on("SetContent",function(){t.addVisual(t.getBody())}),t.load({initial:!0,format:"html"}),t.startContent=t.getContent({format:"raw"}),t.on("compositionstart compositionend",function(e){t.composing="compositionstart"===e.type}),0<t.contentStyles.length&&(r="",Gt.each(t.contentStyles,function(e){r+=e+"\r\n"}),t.dom.addStyle(r)),(i=t,i.inline?yw.styleSheetLoader:i.dom.styleSheetLoader).loadAll(t.contentCSS,function(e){Cw(t)},function(e){Cw(t)}),f.content_style&&(a=t,s=f.content_style,l=or.fromDom(a.getDoc().head),c=or.fromTag("style"),yr(c,"type","text/css"),Bi(c,or.fromText(s)),Bi(l,c))},xw=vi.DOM,Nw=function(e,t){var n,r,o,i,a=e.editorManager.translate("Rich Text Area. Press ALT-0 for help."),u=(n=e.id,r=a,t.height,o=Yl(e),i=or.fromTag("iframe"),Cr(i,o),Cr(i,{id:n+"_ifr",frameBorder:"0",allowTransparency:"true",title:r}),ji(i,"tox-edit-area__iframe"),i).dom();u.onload=function(){u.onload=null,e.fire("load")};var s,l,c,f,d=function(e,t){if(j.document.domain!==j.window.location.hostname&&he.ie&&he.ie<12){var n=fd.uuid("mce");e[n]=function(){ww(e)};var r='javascript:(function(){document.open();document.domain="'+j.document.domain+'";var ed = window.parent.tinymce.get("'+e.id+'");document.write(ed.iframeHTML);document.close();ed.'+n+"(true);})()";return xw.setAttrib(t,"src",r),!0}return!1}(e,u);return e.contentAreaContainer=t.iframeContainer,e.iframeElement=u,e.iframeHTML=(f=Gl(s=e)+"<html><head>",Jl(s)!==s.documentBaseUrl&&(f+='<base href="'+s.documentBaseURI.getURI()+'" />'),f+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />',l=Ql(s),c=Zl(s),ec(s)&&(f+='<meta http-equiv="Content-Security-Policy" content="'+ec(s)+'" />'),f+='</head><body id="'+l+'" class="mce-content-body '+c+'" data-id="'+s.id+'"><br></body></html>'),xw.add(t.iframeContainer,u),d},Ew=function(e,t){var n=Nw(e,t);t.editorContainer&&(xw.get(t.editorContainer).style.display=e.orgDisplay,e.hidden=xw.isHidden(t.editorContainer)),e.getElement().style.display="none",xw.setAttrib(e.id,"aria-hidden","true"),n||ww(e)},zw=function(e){var t,n,r,o,i;e.contentCSS=e.contentCSS.concat((n=Nc(t=e),r=t.editorManager.baseURL+"/skins/content",o="content"+t.editorManager.suffix+".css",i=!0===t.inline,K(n,function(e){return/^[a-z0-9\-]+$/i.test(e)&&!i?r+"/"+e+"/"+o:t.documentBaseURI.toAbsolute(e)})))},Sw=vi.DOM,kw=function(t,n,e){var r=Gf.get(e),o=Gf.urls[e]||t.documentBaseUrl.replace(/\/$/,"");if(e=Gt.trim(e),r&&-1===Gt.inArray(n,e)){if(Gt.each(Gf.dependencies(e),function(e){kw(t,n,e)}),t.plugins[e])return;try{var i=new r(t,o,t.$);(t.plugins[e]=i).init&&(i.init(t,o),n.push(e))}catch($N){Xf.pluginInitError(t,e,$N)}}},Tw=function(e){return e.replace(/^\-/,"")},Aw=function(e){return{editorContainer:e,iframeContainer:e}},Rw=function(e){var t,n,r=e.getElement();return e.inline?Aw(null):(t=r,n=Sw.create("div"),Sw.insertAfter(n,t),Aw(n))},Dw=function(e){var n,t,r,o,i;e.fire("ScriptsLoaded"),n=e,t=Gt.trim(n.settings.icons),r=sa({},{"accessibility-check":'<svg width="24" height="24"><path d="M12 2a2 2 0 0 1 2 2 2 2 0 0 1-2 2 2 2 0 0 1-2-2c0-1.1.9-2 2-2zm8 7h-5v12c0 .6-.4 1-1 1a1 1 0 0 1-1-1v-5c0-.6-.4-1-1-1a1 1 0 0 0-1 1v5c0 .6-.4 1-1 1a1 1 0 0 1-1-1V9H4a1 1 0 1 1 0-2h16c.6 0 1 .4 1 1s-.4 1-1 1z" fill-rule="nonzero"/></svg>',"align-center":'<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm3 4h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 1 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 0 1 0-2zm-3-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"/></svg>',"align-justify":'<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm0 4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm0 4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2zm0 4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"/></svg>',"align-left":'<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm0 4h8c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2zm0-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"/></svg>',"align-none":'<svg width="24" height="24"><path d="M14.2 5L13 7H5a1 1 0 1 1 0-2h9.2zm4 0h.8a1 1 0 0 1 0 2h-2l1.2-2zm-6.4 4l-1.2 2H5a1 1 0 0 1 0-2h6.8zm4 0H19a1 1 0 0 1 0 2h-4.4l1.2-2zm-6.4 4l-1.2 2H5a1 1 0 0 1 0-2h4.4zm4 0H19a1 1 0 0 1 0 2h-6.8l1.2-2zM7 17l-1.2 2H5a1 1 0 0 1 0-2h2zm4 0h8a1 1 0 0 1 0 2H9.8l1.2-2zm5.2-13.5l1.3.7-9.7 16.3-1.3-.7 9.7-16.3z" fill-rule="evenodd"/></svg>',"align-right":'<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm6 4h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm-6-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"/></svg>',"arrow-left":'<svg width="24" height="24"><path d="M5.6 13l12 6a1 1 0 0 0 1.4-1V6a1 1 0 0 0-1.4-.9l-12 6a1 1 0 0 0 0 1.8z" fill-rule="evenodd"/></svg>',"arrow-right":'<svg width="24" height="24"><path d="M18.5 13l-12 6A1 1 0 0 1 5 18V6a1 1 0 0 1 1.4-.9l12 6a1 1 0 0 1 0 1.8z" fill-rule="evenodd"/></svg>',bold:'<svg width="24" height="24"><path d="M7.8 19c-.3 0-.5 0-.6-.2l-.2-.5V5.7c0-.2 0-.4.2-.5l.6-.2h5c1.5 0 2.7.3 3.5 1 .7.6 1.1 1.4 1.1 2.5a3 3 0 0 1-.6 1.9c-.4.6-1 1-1.6 1.2.4.1.9.3 1.3.6s.8.7 1 1.2c.4.4.5 1 .5 1.6 0 1.3-.4 2.3-1.3 3-.8.7-2.1 1-3.8 1H7.8zm5-8.3c.6 0 1.2-.1 1.6-.5.4-.3.6-.7.6-1.3 0-1.1-.8-1.7-2.3-1.7H9.3v3.5h3.4zm.5 6c.7 0 1.3-.1 1.7-.4.4-.4.6-.9.6-1.5s-.2-1-.7-1.4c-.4-.3-1-.4-2-.4H9.4v3.8h4z" fill-rule="evenodd"/></svg>',bookmark:'<svg width="24" height="24"><path d="M6 4v17l6-4 6 4V4c0-.6-.4-1-1-1H7a1 1 0 0 0-1 1z" fill-rule="nonzero"/></svg>',"border-width":'<svg width="24" height="24"><path d="M5 14.8h14a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2zm-.5 3.7h15c.3 0 .5.2.5.5s-.2.5-.5.5h-15a.5.5 0 1 1 0-1zm.5-8.3h14c.6 0 1 .4 1 1v1c0 .5-.4 1-1 1H5a1 1 0 0 1-1-1v-1c0-.6.4-1 1-1zm0-5.7h14c.6 0 1 .4 1 1v2c0 .6-.4 1-1 1H5a1 1 0 0 1-1-1v-2c0-.6.4-1 1-1z" fill-rule="evenodd"/></svg>',brightness:'<svg width="24" height="24"><path d="M12 17c.3 0 .5.1.7.3.2.2.3.4.3.7v1c0 .3-.1.5-.3.7a1 1 0 0 1-.7.3 1 1 0 0 1-.7-.3 1 1 0 0 1-.3-.7v-1c0-.3.1-.5.3-.7.2-.2.4-.3.7-.3zm0-10a1 1 0 0 1-.7-.3A1 1 0 0 1 11 6V5c0-.3.1-.5.3-.7.2-.2.4-.3.7-.3.3 0 .5.1.7.3.2.2.3.4.3.7v1c0 .3-.1.5-.3.7a1 1 0 0 1-.7.3zm7 4c.3 0 .5.1.7.3.2.2.3.4.3.7 0 .3-.1.5-.3.7a1 1 0 0 1-.7.3h-1a1 1 0 0 1-.7-.3 1 1 0 0 1-.3-.7c0-.3.1-.5.3-.7.2-.2.4-.3.7-.3h1zM7 12c0 .3-.1.5-.3.7a1 1 0 0 1-.7.3H5a1 1 0 0 1-.7-.3A1 1 0 0 1 4 12c0-.3.1-.5.3-.7.2-.2.4-.3.7-.3h1c.3 0 .5.1.7.3.2.2.3.4.3.7zm10 3.5l.7.8c.2.1.3.4.3.6 0 .3-.1.6-.3.8a1 1 0 0 1-.8.3 1 1 0 0 1-.6-.3l-.8-.7a1 1 0 0 1-.3-.8c0-.2.1-.5.3-.7a1 1 0 0 1 1.4 0zm-10-7l-.7-.8a1 1 0 0 1-.3-.6c0-.3.1-.6.3-.8.2-.2.5-.3.8-.3.2 0 .5.1.7.3l.7.7c.2.2.3.5.3.8 0 .2-.1.5-.3.7a1 1 0 0 1-.7.3 1 1 0 0 1-.8-.3zm10 0a1 1 0 0 1-.8.3 1 1 0 0 1-.7-.3 1 1 0 0 1-.3-.7c0-.3.1-.6.3-.8l.8-.7c.1-.2.4-.3.6-.3.3 0 .6.1.8.3.2.2.3.5.3.8 0 .2-.1.5-.3.7l-.7.7zm-10 7c.2-.2.5-.3.8-.3.2 0 .5.1.7.3a1 1 0 0 1 0 1.4l-.8.8a1 1 0 0 1-.6.3 1 1 0 0 1-.8-.3 1 1 0 0 1-.3-.8c0-.2.1-.5.3-.6l.7-.8zM12 8a4 4 0 0 1 3.7 2.4 4 4 0 0 1 0 3.2A4 4 0 0 1 12 16a4 4 0 0 1-3.7-2.4 4 4 0 0 1 0-3.2A4 4 0 0 1 12 8zm0 6.5c.7 0 1.3-.2 1.8-.7.5-.5.7-1.1.7-1.8s-.2-1.3-.7-1.8c-.5-.5-1.1-.7-1.8-.7s-1.3.2-1.8.7c-.5.5-.7 1.1-.7 1.8s.2 1.3.7 1.8c.5.5 1.1.7 1.8.7z" fill-rule="evenodd"/></svg>',browse:'<svg width="24" height="24"><path d="M19 4a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-4v-2h4V8H5v10h4v2H5a2 2 0 0 1-2-2V6c0-1.1.9-2 2-2h14zm-8 9.4l-2.3 2.3a1 1 0 1 1-1.4-1.4l4-4a1 1 0 0 1 1.4 0l4 4a1 1 0 0 1-1.4 1.4L13 13.4V20a1 1 0 0 1-2 0v-6.6z" fill-rule="nonzero"/></svg>',cancel:'<svg width="24" height="24"><path d="M12 4.6a7.4 7.4 0 1 1 0 14.8 7.4 7.4 0 0 1 0-14.8zM12 3a9 9 0 1 0 0 18 9 9 0 0 0 0-18zm0 8L14.8 8l1 1.1-2.7 2.8 2.7 2.7-1.1 1.1-2.7-2.7-2.7 2.7-1-1.1 2.6-2.7-2.7-2.7 1-1.1 2.8 2.7z" fill-rule="nonzero"/></svg>',"change-case":'<svg width="24" height="24"><path d="M18.4 18.2v-.6c-.5.8-1.3 1.2-2.4 1.2-2.2 0-3.3-1.6-3.3-4.8 0-3.1 1-4.7 3.3-4.7 1.1 0 1.8.3 2.4 1.1v-.6c0-.5.4-.8.8-.8s.8.3.8.8v8.4c0 .5-.4.8-.8.8a.8.8 0 0 1-.8-.8zm-2-7.4c-1.3 0-1.8.9-1.8 3.2 0 2.4.5 3.3 1.7 3.3 1.3 0 1.8-.9 1.8-3.2 0-2.4-.5-3.3-1.7-3.3zM10 15.7H5.5l-.8 2.6a1 1 0 0 1-1 .7h-.2a.7.7 0 0 1-.7-1l4-12a1 1 0 1 1 2 0l4 12a.7.7 0 0 1-.8 1h-.2a1 1 0 0 1-1-.7l-.8-2.6zm-.3-1.5l-2-6.5-1.9 6.5h3.9z" fill-rule="evenodd"/></svg>',"character-count":'<svg width="24" height="24"><path d="M4 11.5h16v1H4v-1zm4.8-6.8V10H7.7V5.8h-1v-1h2zM11 8.3V9h2v1h-3V7.7l2-1v-.9h-2v-1h3v2.4l-2 1zm6.3-3.4V10h-3.1V9h2.1V8h-2.1V6.8h2.1v-1h-2.1v-1h3.1zM5.8 16.4c0-.5.2-.8.5-1 .2-.2.6-.3 1.2-.3l.8.1c.2 0 .4.2.5.3l.4.4v2.8l.2.3H8.2v-.1-.2l-.6.3H7c-.4 0-.7 0-1-.2a1 1 0 0 1-.3-.9c0-.3 0-.6.3-.8.3-.2.7-.4 1.2-.4l.6-.2h.3v-.2l-.1-.2a.8.8 0 0 0-.5-.1 1 1 0 0 0-.4 0l-.3.4h-1zm2.3.8h-.2l-.2.1-.4.1a1 1 0 0 0-.4.2l-.2.2.1.3.5.1h.4l.4-.4v-.6zm2-3.4h1.2v1.7l.5-.3h.5c.5 0 .9.1 1.2.5.3.4.5.8.5 1.4 0 .6-.2 1.1-.5 1.5-.3.4-.7.6-1.3.6l-.6-.1-.4-.4v.4h-1.1v-5.4zm1.1 3.3c0 .3 0 .6.2.8a.7.7 0 0 0 1.2 0l.2-.8c0-.4 0-.6-.2-.8a.7.7 0 0 0-.6-.3l-.6.3-.2.8zm6.1-.5c0-.2 0-.3-.2-.4a.8.8 0 0 0-.5-.2c-.3 0-.5.1-.6.3l-.2.9c0 .3 0 .6.2.8.1.2.3.3.6.3.2 0 .4 0 .5-.2l.2-.4h1.1c0 .5-.3.8-.6 1.1a2 2 0 0 1-1.3.4c-.5 0-1-.2-1.3-.6a2 2 0 0 1-.5-1.4c0-.6.1-1.1.5-1.5.3-.4.8-.5 1.4-.5.5 0 1 0 1.2.3.4.3.5.7.5 1.2h-1v-.1z" fill-rule="evenodd"/></svg>',checklist:'<svg width="24" height="24"><path d="M11 17h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm0-6h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm0-6h8a1 1 0 0 1 0 2h-8a1 1 0 0 1 0-2zM7.2 16c.2-.4.6-.5.9-.3.3.2.4.6.2 1L6 20c-.2.3-.7.4-1 0l-1.3-1.3a.7.7 0 0 1 0-1c.3-.2.7-.2 1 0l.7.9 1.7-2.8zm0-6c.2-.4.6-.5.9-.3.3.2.4.6.2 1L6 14c-.2.3-.7.4-1 0l-1.3-1.3a.7.7 0 0 1 0-1c.3-.2.7-.2 1 0l.7.9 1.7-2.8zm0-6c.2-.4.6-.5.9-.3.3.2.4.6.2 1L6 8c-.2.3-.7.4-1 0L3.8 6.9a.7.7 0 0 1 0-1c.3-.2.7-.2 1 0l.7.9 1.7-2.8z" fill-rule="evenodd"/></svg>',checkmark:'<svg width="24" height="24"><path d="M18.2 5.4a1 1 0 0 1 1.6 1.2l-8 12a1 1 0 0 1-1.5.1l-5-5a1 1 0 1 1 1.4-1.4l4.1 4.1 7.4-11z" fill-rule="nonzero"/></svg>',"chevron-down":'<svg width="10" height="10"><path d="M8.7 2.2c.3-.3.8-.3 1 0 .4.4.4.9 0 1.2L5.7 7.8c-.3.3-.9.3-1.2 0L.2 3.4a.8.8 0 0 1 0-1.2c.3-.3.8-.3 1.1 0L5 6l3.7-3.8z" fill-rule="nonzero"/></svg>',"chevron-left":'<svg width="10" height="10"><path d="M7.8 1.3L4 5l3.8 3.7c.3.3.3.8 0 1-.4.4-.9.4-1.2 0L2.2 5.7a.8.8 0 0 1 0-1.2L6.6.2C7 0 7.4 0 7.8.2c.3.3.3.8 0 1.1z" fill-rule="nonzero"/></svg>',"chevron-right":'<svg width="10" height="10"><path d="M2.2 1.3a.8.8 0 0 1 0-1c.4-.4.9-.4 1.2 0l4.4 4.1c.3.4.3.9 0 1.2L3.4 9.8c-.3.3-.8.3-1.2 0a.8.8 0 0 1 0-1.1L6 5 2.2 1.3z" fill-rule="nonzero"/></svg>',"chevron-up":'<svg width="10" height="10"><path d="M8.7 7.8L5 4 1.3 7.8c-.3.3-.8.3-1 0a.8.8 0 0 1 0-1.2l4.1-4.4c.3-.3.9-.3 1.2 0l4.2 4.4c.3.3.3.9 0 1.2-.3.3-.8.3-1.1 0z" fill-rule="nonzero"/></svg>',close:'<svg width="24" height="24"><path d="M17.3 8.2L13.4 12l3.9 3.8a1 1 0 0 1-1.5 1.5L12 13.4l-3.8 3.9a1 1 0 0 1-1.5-1.5l3.9-3.8-3.9-3.8a1 1 0 0 1 1.5-1.5l3.8 3.9 3.8-3.9a1 1 0 0 1 1.5 1.5z" fill-rule="evenodd"/></svg>',"code-sample":'<svg width="24" height="26"><path d="M7.1 11a2.8 2.8 0 0 1-.8 2 2.8 2.8 0 0 1 .8 2v1.7c0 .3.1.6.4.8.2.3.5.4.8.4.3 0 .4.2.4.4v.8c0 .2-.1.4-.4.4-.7 0-1.4-.3-2-.8-.5-.6-.8-1.3-.8-2V15c0-.3-.1-.6-.4-.8-.2-.3-.5-.4-.8-.4a.4.4 0 0 1-.4-.4v-.8c0-.2.2-.4.4-.4.3 0 .6-.1.8-.4.3-.2.4-.5.4-.8V9.3c0-.7.3-1.4.8-2 .6-.5 1.3-.8 2-.8.3 0 .4.2.4.4v.8c0 .2-.1.4-.4.4-.3 0-.6.1-.8.4-.3.2-.4.5-.4.8V11zm9.8 0V9.3c0-.3-.1-.6-.4-.8-.2-.3-.5-.4-.8-.4a.4.4 0 0 1-.4-.4V7c0-.2.1-.4.4-.4.7 0 1.4.3 2 .8.5.6.8 1.3.8 2V11c0 .3.1.6.4.8.2.3.5.4.8.4.2 0 .4.2.4.4v.8c0 .2-.2.4-.4.4-.3 0-.6.1-.8.4-.3.2-.4.5-.4.8v1.7c0 .7-.3 1.4-.8 2-.6.5-1.3.8-2 .8a.4.4 0 0 1-.4-.4v-.8c0-.2.1-.4.4-.4.3 0 .6-.1.8-.4.3-.2.4-.5.4-.8V15a2.8 2.8 0 0 1 .8-2 2.8 2.8 0 0 1-.8-2zm-3.3-.4c0 .4-.1.8-.5 1.1-.3.3-.7.5-1.1.5-.4 0-.8-.2-1.1-.5-.4-.3-.5-.7-.5-1.1 0-.5.1-.9.5-1.2.3-.3.7-.4 1.1-.4.4 0 .8.1 1.1.4.4.3.5.7.5 1.2zM12 13c.4 0 .8.1 1.1.5.4.3.5.7.5 1.1 0 1-.1 1.6-.5 2a3 3 0 0 1-1.1 1c-.4.3-.8.4-1.1.4a.5.5 0 0 1-.5-.5V17a3 3 0 0 0 1-.2l.6-.6c-.6 0-1-.2-1.3-.5-.2-.3-.3-.7-.3-1 0-.5.1-1 .5-1.2.3-.4.7-.5 1.1-.5z" fill-rule="evenodd"/></svg>',"color-levels":'<svg width="24" height="24"><path d="M17.5 11.4A9 9 0 0 1 18 14c0 .5 0 1-.2 1.4 0 .4-.3.9-.5 1.3a6.2 6.2 0 0 1-3.7 3 5.7 5.7 0 0 1-3.2 0A5.9 5.9 0 0 1 7.6 18a6.2 6.2 0 0 1-1.4-2.6 6.7 6.7 0 0 1 0-2.8c0-.4.1-.9.3-1.3a13.6 13.6 0 0 1 2.3-4A20 20 0 0 1 12 4a26.4 26.4 0 0 1 3.2 3.4 18.2 18.2 0 0 1 2.3 4zm-2 4.5c.4-.7.5-1.4.5-2a7.3 7.3 0 0 0-1-3.2c.2.6.2 1.2.2 1.9a4.5 4.5 0 0 1-1.3 3 5.3 5.3 0 0 1-2.3 1.5 4.9 4.9 0 0 1-2 .1 4.3 4.3 0 0 0 2.4.8 4 4 0 0 0 2-.6 4 4 0 0 0 1.5-1.5z" fill-rule="evenodd"/></svg>',"color-picker":'<svg width="24" height="24"><path d="M12 3a9 9 0 0 0 0 18 1.5 1.5 0 0 0 1.1-2.5c-.2-.3-.4-.6-.4-1 0-.8.7-1.5 1.5-1.5H16a5 5 0 0 0 5-5c0-4.4-4-8-9-8zm-5.5 9a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm3-4a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm3 4a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z" fill-rule="nonzero"/></svg>',"color-swatch-remove-color":'<svg width="24" height="24"><path stroke="#000" stroke-width="2" d="M21 3L3 21" fill-rule="evenodd"/></svg>',"color-swatch":'<svg width="24" height="24"><rect x="3" y="3" width="18" height="18" rx="1" fill-rule="evenodd"/></svg>',comment:'<svg width="24" height="24"><path d="M9 19l3-2h7c.6 0 1-.4 1-1V6c0-.6-.4-1-1-1H5a1 1 0 0 0-1 1v10c0 .6.4 1 1 1h4v2zm-2 4v-4H5a3 3 0 0 1-3-3V6a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3h-6.4L7 23z" fill-rule="nonzero"/></svg>',contrast:'<svg width="24" height="24"><path d="M12 4a7.8 7.8 0 0 1 5.7 2.3A8 8 0 1 1 12 4zm-6 8a6 6 0 0 0 6 6V6a6 6 0 0 0-6 6z" fill-rule="evenodd"/></svg>',copy:'<svg width="24" height="24"><path d="M16 3H6a2 2 0 0 0-2 2v11h2V5h10V3zm1 4a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2V9c0-1.2.9-2 2-2h7zm0 12V9h-7v10h7z" fill-rule="nonzero"/></svg>',crop:'<svg width="24" height="24"><path d="M17 8v7h2c.6 0 1 .4 1 1s-.4 1-1 1h-2v2c0 .6-.4 1-1 1a1 1 0 0 1-1-1v-2H7V9H5a1 1 0 1 1 0-2h2V5c0-.6.4-1 1-1s1 .4 1 1v2h7l3-3 1 1-3 3zM9 9v5l5-5H9zm1 6h5v-5l-5 5z" fill-rule="evenodd"/></svg>',cut:'<svg width="24" height="24"><path d="M18 15c.6.7 1 1.4 1 2.3 0 .8-.2 1.5-.7 2l-.8.5-1 .2c-.4 0-.8 0-1.2-.3a3.9 3.9 0 0 1-2.1-2.2c-.2-.5-.3-1-.2-1.5l-1-1-1 1c0 .5 0 1-.2 1.5-.1.5-.4 1-.9 1.4-.3.4-.7.6-1.2.8l-1.2.3c-.4 0-.7 0-1-.2-.3 0-.6-.3-.8-.5-.5-.5-.8-1.2-.7-2 0-.9.4-1.6 1-2.2A3.7 3.7 0 0 1 8.6 14H9l1-1-4-4-.5-1a3.3 3.3 0 0 1 0-2c0-.4.3-.7.5-1l6 6 6-6 .5 1a3.3 3.3 0 0 1 0 2c0 .4-.3.7-.5 1l-4 4 1 1h.5c.4 0 .8 0 1.2.3.5.2.9.4 1.2.8zm-8.5 2.2l.1-.4v-.3-.4a1 1 0 0 0-.2-.5 1 1 0 0 0-.4-.2 1.6 1.6 0 0 0-.8 0 2.6 2.6 0 0 0-.8.3 2.5 2.5 0 0 0-.9 1.1l-.1.4v.7l.2.5.5.2h.7a2.5 2.5 0 0 0 .8-.3 2.8 2.8 0 0 0 1-1zm2.5-2.8c.4 0 .7-.1 1-.4.3-.3.4-.6.4-1s-.1-.7-.4-1c-.3-.3-.6-.4-1-.4s-.7.1-1 .4c-.3.3-.4.6-.4 1s.1.7.4 1c.3.3.6.4 1 .4zm5.4 4l.2-.5v-.4-.3a2.6 2.6 0 0 0-.3-.8 2.4 2.4 0 0 0-.7-.7 2.5 2.5 0 0 0-.8-.3 1.5 1.5 0 0 0-.8 0 1 1 0 0 0-.4.2 1 1 0 0 0-.2.5 1.5 1.5 0 0 0 0 .7v.4l.3.4.3.4a2.8 2.8 0 0 0 .8.5l.4.1h.7l.5-.2z" fill-rule="evenodd"/></svg>',"document-properties":'<svg width="24" height="24"><path d="M14.4 3H7a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h10a2 2 0 0 0 2-2V7.6L14.4 3zM17 19H7V5h6v4h4v10z" fill-rule="nonzero"/></svg>',drag:'<svg width="24" height="24"><path d="M13 5h2v2h-2V5zm0 4h2v2h-2V9zM9 9h2v2H9V9zm4 4h2v2h-2v-2zm-4 0h2v2H9v-2zm0 4h2v2H9v-2zm4 0h2v2h-2v-2zM9 5h2v2H9V5z" fill-rule="evenodd"/></svg>',duplicate:'<svg width="24" height="24"><g fill-rule="nonzero"><path d="M16 3v2H6v11H4V5c0-1.1.9-2 2-2h10zm3 8h-2V9h-7v10h9a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2V9c0-1.2.9-2 2-2h7a2 2 0 0 1 2 2v2z"/><path d="M17 14h1a1 1 0 0 1 0 2h-1v1a1 1 0 0 1-2 0v-1h-1a1 1 0 0 1 0-2h1v-1a1 1 0 0 1 2 0v1z"/></g></svg>',"edit-image":'<svg width="24" height="24"><path d="M18 16h2V7a2 2 0 0 0-2-2H7v2h11v9zM6 17h15a1 1 0 0 1 0 2h-1v1a1 1 0 0 1-2 0v-1H6a2 2 0 0 1-2-2V7H3a1 1 0 1 1 0-2h1V4a1 1 0 1 1 2 0v13zm3-5.3l1.3 2 3-4.7 3.7 6H7l2-3.3z" fill-rule="nonzero"/></svg>',"embed-page":'<svg width="24" height="24"><path d="M19 6V5H5v14h2A13 13 0 0 1 19 6zm0 1.4c-.8.8-1.6 2.4-2.2 4.6H19V7.4zm0 5.6h-2.4c-.4 1.8-.6 3.8-.6 6h3v-6zm-4 6c0-2.2.2-4.2.6-6H13c-.7 1.8-1.1 3.8-1.1 6h3zm-4 0c0-2.2.4-4.2 1-6H9.6A12 12 0 0 0 8 19h3zM4 3h16c.6 0 1 .4 1 1v16c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V4c0-.6.4-1 1-1zm11.8 9c.4-1.9 1-3.4 1.8-4.5a9.2 9.2 0 0 0-4 4.5h2.2zm-3.4 0a12 12 0 0 1 2.8-4 12 12 0 0 0-5 4h2.2z" fill-rule="nonzero"/></svg>',embed:'<svg width="24" height="24"><path d="M4 3h16c.6 0 1 .4 1 1v16c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V4c0-.6.4-1 1-1zm1 2v14h14V5H5zm4.8 2.6l5.6 4a.5.5 0 0 1 0 .8l-5.6 4A.5.5 0 0 1 9 16V8a.5.5 0 0 1 .8-.4z" fill-rule="nonzero"/></svg>',emoji:'<svg width="24" height="24"><path d="M9 11c.6 0 1-.4 1-1s-.4-1-1-1a1 1 0 0 0-1 1c0 .6.4 1 1 1zm6 0c.6 0 1-.4 1-1s-.4-1-1-1a1 1 0 0 0-1 1c0 .6.4 1 1 1zm-3 5.5c2.1 0 4-1.5 4.4-3.5H7.6c.5 2 2.3 3.5 4.4 3.5zM12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16zm0 14.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13z" fill-rule="nonzero"/></svg>',fill:'<svg width="24" height="26"><path d="M16.6 12l-9-9-1.4 1.4 2.4 2.4-5.2 5.1c-.5.6-.5 1.6 0 2.2L9 19.6a1.5 1.5 0 0 0 2.2 0l5.5-5.5c.5-.6.5-1.6 0-2.2zM5.2 13L10 8.2l4.8 4.8H5.2zM19 14.5s-2 2.2-2 3.5c0 1.1.9 2 2 2a2 2 0 0 0 2-2c0-1.3-2-3.5-2-3.5z" fill-rule="nonzero"/></svg>',"flip-horizontally":'<svg width="24" height="24"><path d="M14 19h2v-2h-2v2zm4-8h2V9h-2v2zM4 7v10c0 1.1.9 2 2 2h3v-2H6V7h3V5H6a2 2 0 0 0-2 2zm14-2v2h2a2 2 0 0 0-2-2zm-7 16h2V3h-2v18zm7-6h2v-2h-2v2zm-4-8h2V5h-2v2zm4 12a2 2 0 0 0 2-2h-2v2z" fill-rule="nonzero"/></svg>',"flip-vertically":'<svg width="24" height="24"><path d="M5 14v2h2v-2H5zm8 4v2h2v-2h-2zm4-14H7a2 2 0 0 0-2 2v3h2V6h10v3h2V6a2 2 0 0 0-2-2zm2 14h-2v2a2 2 0 0 0 2-2zM3 11v2h18v-2H3zm6 7v2h2v-2H9zm8-4v2h2v-2h-2zM5 18c0 1.1.9 2 2 2v-2H5z" fill-rule="nonzero"/></svg>',"format-painter":'<svg width="24" height="24"><path d="M18 5V4c0-.5-.4-1-1-1H5a1 1 0 0 0-1 1v4c0 .6.5 1 1 1h12c.6 0 1-.4 1-1V7h1v4H9v9c0 .6.4 1 1 1h2c.6 0 1-.4 1-1v-7h8V5h-3z" fill-rule="nonzero"/></svg>',fullscreen:'<svg width="24" height="24"><path d="M15.3 10l-1.2-1.3 2.9-3h-2.3a.9.9 0 1 1 0-1.7H19c.5 0 .9.4.9.9v4.4a.9.9 0 1 1-1.8 0V7l-2.9 3zm0 4l3 3v-2.3a.9.9 0 1 1 1.7 0V19c0 .5-.4.9-.9.9h-4.4a.9.9 0 1 1 0-1.8H17l-3-2.9 1.3-1.2zM10 15.4l-2.9 3h2.3a.9.9 0 1 1 0 1.7H5a.9.9 0 0 1-.9-.9v-4.4a.9.9 0 1 1 1.8 0V17l2.9-3 1.2 1.3zM8.7 10L5.7 7v2.3a.9.9 0 0 1-1.7 0V5c0-.5.4-.9.9-.9h4.4a.9.9 0 0 1 0 1.8H7l3 2.9-1.3 1.2z" fill-rule="nonzero"/></svg>',gamma:'<svg width="24" height="24"><path d="M4 3h16c.6 0 1 .4 1 1v16c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V4c0-.6.4-1 1-1zm1 2v14h14V5H5zm6.5 11.8V14L9.2 8.7a5.1 5.1 0 0 0-.4-.8l-.1-.2H8 8v-1l.3-.1.3-.1h.7a1 1 0 0 1 .6.5l.1.3a8.5 8.5 0 0 1 .3.6l1.9 4.6 2-5.2a1 1 0 0 1 1-.6.5.5 0 0 1 .5.6L13 14v2.8a.7.7 0 0 1-1.4 0z" fill-rule="nonzero"/></svg>',help:'<svg width="24" height="24"><g fill-rule="evenodd"><path d="M12 5.5a6.5 6.5 0 0 0-6 9 6.3 6.3 0 0 0 1.4 2l1 1a6.3 6.3 0 0 0 3.6 1 6.5 6.5 0 0 0 6-9 6.3 6.3 0 0 0-1.4-2l-1-1a6.3 6.3 0 0 0-3.6-1zM12 4a7.8 7.8 0 0 1 5.7 2.3A8 8 0 1 1 12 4z"/><path d="M9.6 9.7a.7.7 0 0 1-.7-.8c0-1.1 1.5-1.8 3.2-1.8 1.8 0 3.2.8 3.2 2.4 0 1.4-.4 2.1-1.5 2.8-.2 0-.3.1-.3.2a2 2 0 0 0-.8.8.8.8 0 0 1-1.4-.6c.3-.7.8-1 1.3-1.5l.4-.2c.7-.4.8-.6.8-1.5 0-.5-.6-.9-1.7-.9-.5 0-1 .1-1.4.3-.2 0-.3.1-.3.2v-.2c0 .4-.4.8-.8.8z" fill-rule="nonzero"/><circle cx="12" cy="16" r="1"/></g></svg>',"highlight-bg-color":'<svg width="24" height="24"><g fill-rule="evenodd"><path id="tox-icon-highlight-bg-color__color" d="M3 18h18v3H3z"/><path fill-rule="nonzero" d="M7.7 16.7H3l3.3-3.3-.7-.8L10.2 8l4 4.1-4 4.2c-.2.2-.6.2-.8 0l-.6-.7-1.1 1.1zm5-7.5L11 7.4l3-2.9a2 2 0 0 1 2.6 0L18 6c.7.7.7 2 0 2.7l-2.9 2.9-1.8-1.8-.5-.6"/></g></svg>',home:'<svg width="24" height="24"><path fill-rule="nonzero" d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>',"horizontal-rule":'<svg width="24" height="24"><path d="M4 11h16v2H4z" fill-rule="evenodd"/></svg>',"image-options":'<svg width="24" height="24"><path d="M6 10a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2zm12 0a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2zm-6 0a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2z" fill-rule="nonzero"/></svg>',image:'<svg width="24" height="24"><path d="M5 15.7l3.3-3.2c.3-.3.7-.3 1 0L12 15l4.1-4c.3-.4.8-.4 1 0l2 1.9V5H5v10.7zM5 18V19h3l2.8-2.9-2-2L5 17.9zm14-3l-2.5-2.4-6.4 6.5H19v-4zM4 3h16c.6 0 1 .4 1 1v16c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V4c0-.6.4-1 1-1zm6 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" fill-rule="nonzero"/></svg>',indent:'<svg width="24" height="24"><path d="M7 5h12c.6 0 1 .4 1 1s-.4 1-1 1H7a1 1 0 1 1 0-2zm5 4h7c.6 0 1 .4 1 1s-.4 1-1 1h-7a1 1 0 0 1 0-2zm0 4h7c.6 0 1 .4 1 1s-.4 1-1 1h-7a1 1 0 0 1 0-2zm-5 4h12a1 1 0 0 1 0 2H7a1 1 0 0 1 0-2zm-2.6-3.8L6.2 12l-1.8-1.2a1 1 0 0 1 1.2-1.6l3 2a1 1 0 0 1 0 1.6l-3 2a1 1 0 1 1-1.2-1.6z" fill-rule="evenodd"/></svg>',indeterminate:'<svg width="24" height="24"><path d="M12 21a9 9 0 1 1 0-18 9 9 0 0 1 0 18zM9 11a1 1 0 0 0 0 2h6a1 1 0 0 0 0-2H9z" fill-rule="evenodd"/></svg>',info:'<svg width="24" height="24"><path d="M12 4a7.8 7.8 0 0 1 5.7 2.3A8 8 0 1 1 12 4zm-1 3v2h2V7h-2zm3 10v-1h-1v-5h-3v1h1v4h-1v1h4z" fill-rule="evenodd"/></svg>',"insert-character":'<svg width="24" height="24"><path d="M15 18h4l1-2v4h-6v-3.3l1.4-1a6 6 0 0 0 1.8-2.9 6.3 6.3 0 0 0-.1-4.1 5.8 5.8 0 0 0-3-3.2c-.6-.3-1.3-.5-2.1-.5a5.1 5.1 0 0 0-3.9 1.8 6.3 6.3 0 0 0-1.3 6 6.2 6.2 0 0 0 1.8 3l1.4.9V20H4v-4l1 2h4v-.5l-2-1L5.4 15A6.5 6.5 0 0 1 4 11c0-1 .2-1.9.6-2.7A7 7 0 0 1 6.3 6C7.1 5.4 8 5 9 4.5c1-.3 2-.5 3.1-.5a8.8 8.8 0 0 1 5.7 2 7 7 0 0 1 1.7 2.3 6 6 0 0 1 .2 4.8c-.2.7-.6 1.3-1 1.9a7.6 7.6 0 0 1-3.6 2.5v.5z" fill-rule="evenodd"/></svg>',"insert-time":'<svg width="24" height="24"><g fill-rule="nonzero"><path d="M19 2H5a3 3 0 0 0-3 3v14a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V5a3 3 0 0 0-3-3zm-7 18a8 8 0 1 1 0-16 8 8 0 0 1 0 16z"/><path d="M15 12h-3V7a.5.5 0 0 0-1 0v6h4a.5.5 0 0 0 0-1z"/></g></svg>',invert:'<svg width="24" height="24"><path d="M18 19.3L16.5 18a5.8 5.8 0 0 1-3.1 1.9 6.1 6.1 0 0 1-5.5-1.6A5.8 5.8 0 0 1 6 14v-.3l.1-1.2A13.9 13.9 0 0 1 7.7 9l-3-3 .7-.8 2.8 2.9 9 8.9 1.5 1.6-.7.6zm0-5.5v.3l-.1 1.1-.4 1-1.2-1.2a4.3 4.3 0 0 0 .2-1v-.2c0-.4 0-.8-.2-1.3l-.5-1.4a14.8 14.8 0 0 0-3-4.2L12 6a26.1 26.1 0 0 0-2.2 2.5l-1-1a20.9 20.9 0 0 1 2.9-3.3L12 4l1 .8a22.2 22.2 0 0 1 4 5.4c.6 1.2 1 2.4 1 3.6z" fill-rule="evenodd"/></svg>',italic:'<svg width="24" height="24"><path d="M16.7 4.7l-.1.9h-.3c-.6 0-1 0-1.4.3-.3.3-.4.6-.5 1.1l-2.1 9.8v.6c0 .5.4.8 1.4.8h.2l-.2.8H8l.2-.8h.2c1.1 0 1.8-.5 2-1.5l2-9.8.1-.5c0-.6-.4-.8-1.4-.8h-.3l.2-.9h5.8z" fill-rule="evenodd"/></svg>',line:'<svg width="24" height="24"><path d="M15 9l-8 8H4v-3l8-8 3 3zm1-1l-3-3 1-1h1c-.2 0 0 0 0 0l2 2s0 .2 0 0v1l-1 1zM4 18h16v2H4v-2z" fill-rule="evenodd"/></svg>',link:'<svg width="24" height="24"><path d="M6.2 12.3a1 1 0 0 1 1.4 1.4l-2.1 2a2 2 0 1 0 2.7 2.8l4.8-4.8a1 1 0 0 0 0-1.4 1 1 0 1 1 1.4-1.3 2.9 2.9 0 0 1 0 4L9.6 20a3.9 3.9 0 0 1-5.5-5.5l2-2zm11.6-.6a1 1 0 0 1-1.4-1.4l2-2a2 2 0 1 0-2.6-2.8L11 10.3a1 1 0 0 0 0 1.4A1 1 0 1 1 9.6 13a2.9 2.9 0 0 1 0-4L14.4 4a3.9 3.9 0 0 1 5.5 5.5l-2 2z" fill-rule="nonzero"/></svg>',"list-bull-circle":'<svg width="48" height="48"><g fill-rule="evenodd"><path d="M11 16a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 1a3 3 0 1 1 0-6 3 3 0 0 1 0 6zM11 26a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 1a3 3 0 1 1 0-6 3 3 0 0 1 0 6zM11 36a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 1a3 3 0 1 1 0-6 3 3 0 0 1 0 6z" fill-rule="nonzero"/><path opacity=".2" d="M18 12h22v4H18zM18 22h22v4H18zM18 32h22v4H18z"/></g></svg>',"list-bull-default":'<svg width="48" height="48"><g fill-rule="evenodd"><circle cx="11" cy="14" r="3"/><circle cx="11" cy="24" r="3"/><circle cx="11" cy="34" r="3"/><path opacity=".2" d="M18 12h22v4H18zM18 22h22v4H18zM18 32h22v4H18z"/></g></svg>',"list-bull-square":'<svg width="48" height="48"><g fill-rule="evenodd"><path d="M8 11h6v6H8zM8 21h6v6H8zM8 31h6v6H8z"/><path opacity=".2" d="M18 12h22v4H18zM18 22h22v4H18zM18 32h22v4H18z"/></g></svg>',"list-num-default":'<svg width="48" height="48"><g fill-rule="evenodd"><path opacity=".2" d="M18 12h22v4H18zM18 22h22v4H18zM18 32h22v4H18z"/><path d="M10 17v-4.8l-1.5 1v-1.1l1.6-1h1.2V17h-1.2zm3.6.1c-.4 0-.7-.3-.7-.7 0-.4.3-.7.7-.7.5 0 .7.3.7.7 0 .4-.2.7-.7.7zm-5 5.7c0-1.2.8-2 2.1-2s2.1.8 2.1 1.8c0 .7-.3 1.2-1.4 2.2l-1.1 1v.2h2.6v1H8.6v-.9l2-1.9c.8-.8 1-1.1 1-1.5 0-.5-.4-.8-1-.8-.5 0-.9.3-.9.9H8.5zm6.3 4.3c-.5 0-.7-.3-.7-.7 0-.4.2-.7.7-.7.4 0 .7.3.7.7 0 .4-.3.7-.7.7zM10 34.4v-1h.7c.6 0 1-.3 1-.8 0-.4-.4-.7-1-.7s-1 .3-1 .8H8.6c0-1.1 1-1.8 2.2-1.8 1.3 0 2.1.6 2.1 1.6 0 .7-.4 1.2-1 1.3v.1c.8.1 1.3.7 1.3 1.4 0 1-1 1.9-2.4 1.9-1.3 0-2.2-.8-2.3-2h1.2c0 .6.5 1 1.1 1 .7 0 1-.4 1-1 0-.5-.3-.8-1-.8h-.7zm4.7 2.7c-.4 0-.7-.3-.7-.7 0-.4.3-.7.7-.7.5 0 .8.3.8.7 0 .4-.3.7-.8.7z"/></g></svg>',"list-num-lower-alpha":'<svg width="48" height="48"><g fill-rule="evenodd"><path opacity=".2" d="M18 12h22v4H18zM18 22h22v4H18zM18 32h22v4H18z"/><path d="M10.3 15.2c.5 0 1-.4 1-.9V14h-1c-.5.1-.8.3-.8.6 0 .4.3.6.8.6zm-.4.9c-1 0-1.5-.6-1.5-1.4 0-.8.6-1.3 1.7-1.4h1.1v-.4c0-.4-.2-.6-.7-.6-.5 0-.8.1-.9.4h-1c0-.8.8-1.4 2-1.4 1.1 0 1.8.6 1.8 1.6V16h-1.1v-.6h-.1c-.2.4-.7.7-1.3.7zm4.6 0c-.5 0-.7-.3-.7-.7 0-.4.2-.7.7-.7.4 0 .7.3.7.7 0 .4-.3.7-.7.7zm-3.2 10c-.6 0-1.2-.3-1.4-.8v.7H8.5v-6.3H10v2.5c.3-.5.8-.9 1.4-.9 1.2 0 1.9 1 1.9 2.4 0 1.5-.7 2.4-1.9 2.4zm-.4-3.7c-.7 0-1 .5-1 1.3s.3 1.4 1 1.4c.6 0 1-.6 1-1.4 0-.8-.4-1.3-1-1.3zm4 3.7c-.5 0-.7-.3-.7-.7 0-.4.2-.7.7-.7.4 0 .7.3.7.7 0 .4-.3.7-.7.7zm-2.2 7h-1.2c0-.5-.4-.8-.9-.8-.6 0-1 .5-1 1.4 0 1 .4 1.4 1 1.4.5 0 .8-.2 1-.7h1c0 1-.8 1.7-2 1.7-1.4 0-2.2-.9-2.2-2.4s.8-2.4 2.2-2.4c1.2 0 2 .7 2 1.7zm1.8 3c-.5 0-.8-.3-.8-.7 0-.4.3-.7.8-.7.4 0 .7.3.7.7 0 .4-.3.7-.7.7z"/></g></svg>',"list-num-lower-greek":'<svg width="48" height="48"><g fill-rule="evenodd"><path opacity=".2" d="M18 12h22v4H18zM18 22h22v4H18zM18 32h22v4H18z"/><path d="M10.5 15c.7 0 1-.5 1-1.3s-.3-1.3-1-1.3c-.5 0-.9.5-.9 1.3s.4 1.4 1 1.4zm-.3 1c-1.1 0-1.8-.8-1.8-2.3 0-1.5.7-2.4 1.8-2.4.7 0 1.1.4 1.3 1h.1v-.9h1.2v3.2c0 .4.1.5.4.5h.2v.9h-.6c-.6 0-1-.2-1.1-.7h-.1c-.2.4-.7.8-1.4.8zm5 .1c-.5 0-.8-.3-.8-.7 0-.4.3-.7.7-.7.5 0 .8.3.8.7 0 .4-.3.7-.8.7zm-4.9 7v-1h.3c.6 0 1-.2 1-.7 0-.5-.4-.8-1-.8-.5 0-.8.3-.8 1v2.2c0 .8.4 1.3 1.1 1.3.6 0 1-.4 1-1s-.5-1-1.3-1h-.3zM8.6 22c0-1.5.7-2.3 2-2.3 1.2 0 2 .6 2 1.6 0 .6-.3 1-.8 1.3.8.3 1.3.8 1.3 1.7 0 1.2-.8 1.9-1.9 1.9-.6 0-1.1-.3-1.3-.8v2.2H8.5V22zm6.2 4.2c-.4 0-.7-.3-.7-.7 0-.4.3-.7.7-.7.5 0 .7.3.7.7 0 .4-.2.7-.7.7zm-4.5 8.5L8 30h1.4l1.7 3.5 1.7-3.5h1.1l-2.2 4.6v.1c.5.8.7 1.4.7 1.8 0 .4-.1.8-.4 1-.2.2-.6.3-1 .3-.9 0-1.3-.4-1.3-1.2 0-.5.2-1 .5-1.7l.1-.2zm.7 1a2 2 0 0 0-.4.9c0 .3.1.4.4.4.3 0 .4-.1.4-.4 0-.2-.1-.6-.4-1zm4.5.5c-.5 0-.8-.3-.8-.7 0-.4.3-.7.8-.7.4 0 .7.3.7.7 0 .4-.3.7-.7.7z"/></g></svg>',"list-num-lower-roman":'<svg width="48" height="48"><g fill-rule="evenodd"><path opacity=".2" d="M18 12h22v4H18zM18 22h22v4H18zM18 32h22v4H18z"/><path d="M15.1 16v-1.2h1.3V16H15zm0 10v-1.2h1.3V26H15zm0 10v-1.2h1.3V36H15z"/><path fill-rule="nonzero" d="M12 21h1.5v5H12zM12 31h1.5v5H12zM9 21h1.5v5H9zM9 31h1.5v5H9zM6 31h1.5v5H6zM12 11h1.5v5H12zM12 19h1.5v1H12zM12 29h1.5v1H12zM9 19h1.5v1H9zM9 29h1.5v1H9zM6 29h1.5v1H6zM12 9h1.5v1H12z"/></g></svg>',"list-num-upper-alpha":'<svg width="48" height="48"><g fill-rule="evenodd"><path opacity=".2" d="M18 12h22v4H18zM18 22h22v4H18zM18 32h22v4H18z"/><path d="M12.6 17l-.5-1.4h-2L9.5 17H8.3l2-6H12l2 6h-1.3zM11 12.3l-.7 2.3h1.6l-.8-2.3zm4.7 4.8c-.4 0-.7-.3-.7-.7 0-.4.3-.7.7-.7.5 0 .7.3.7.7 0 .4-.2.7-.7.7zM11.4 27H8.7v-6h2.6c1.2 0 1.9.6 1.9 1.5 0 .6-.5 1.2-1 1.3.7.1 1.3.7 1.3 1.5 0 1-.8 1.7-2 1.7zM10 22v1.5h1c.6 0 1-.3 1-.8 0-.4-.4-.7-1-.7h-1zm0 4H11c.7 0 1.1-.3 1.1-.8 0-.6-.4-.9-1.1-.9H10V26zm5.4 1.1c-.5 0-.8-.3-.8-.7 0-.4.3-.7.8-.7.4 0 .7.3.7.7 0 .4-.3.7-.7.7zm-4.1 10c-1.8 0-2.8-1.1-2.8-3.1s1-3.1 2.8-3.1c1.4 0 2.5.9 2.6 2.2h-1.3c0-.7-.6-1.1-1.3-1.1-1 0-1.6.7-1.6 2s.6 2 1.6 2c.7 0 1.2-.4 1.4-1h1.2c-.1 1.3-1.2 2.2-2.6 2.2zm4.5 0c-.5 0-.8-.3-.8-.7 0-.4.3-.7.8-.7.4 0 .7.3.7.7 0 .4-.3.7-.7.7z"/></g></svg>',"list-num-upper-roman":'<svg width="48" height="48"><g fill-rule="evenodd"><path opacity=".2" d="M18 12h22v4H18zM18 22h22v4H18zM18 32h22v4H18z"/><path d="M15.1 17v-1.2h1.3V17H15zm0 10v-1.2h1.3V27H15zm0 10v-1.2h1.3V37H15z"/><path fill-rule="nonzero" d="M12 20h1.5v7H12zM12 30h1.5v7H12zM9 20h1.5v7H9zM9 30h1.5v7H9zM6 30h1.5v7H6zM12 10h1.5v7H12z"/></g></svg>',lock:'<svg width="24" height="24"><path d="M16.3 11c.2 0 .3 0 .5.2l.2.6v7.4c0 .3 0 .4-.2.6l-.6.2H7.8c-.3 0-.4 0-.6-.2a.7.7 0 0 1-.2-.6v-7.4c0-.3 0-.4.2-.6l.5-.2H8V8c0-.8.3-1.5.9-2.1.6-.6 1.3-.9 2.1-.9h2c.8 0 1.5.3 2.1.9.6.6.9 1.3.9 2.1v3h.3zM10 8v3h4V8a1 1 0 0 0-.3-.7A1 1 0 0 0 13 7h-2a1 1 0 0 0-.7.3 1 1 0 0 0-.3.7z" fill-rule="evenodd"/></svg>',ltr:'<svg width="24" height="24"><path d="M11 5h7a1 1 0 0 1 0 2h-1v11a1 1 0 0 1-2 0V7h-2v11a1 1 0 0 1-2 0v-6c-.5 0-1 0-1.4-.3A3.4 3.4 0 0 1 7.8 10a3.3 3.3 0 0 1 0-2.8 3.4 3.4 0 0 1 1.8-1.8L11 5zM4.4 16.2L6.2 15l-1.8-1.2a1 1 0 0 1 1.2-1.6l3 2a1 1 0 0 1 0 1.6l-3 2a1 1 0 1 1-1.2-1.6z" fill-rule="evenodd"/></svg>',"more-drawer":'<svg width="24" height="24"><path d="M6 10a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2zm12 0a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2zm-6 0a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2z" fill-rule="nonzero"/></svg>',"new-document":'<svg width="24" height="24"><path d="M14.4 3H7a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h10a2 2 0 0 0 2-2V7.6L14.4 3zM17 19H7V5h6v4h4v10z" fill-rule="nonzero"/></svg>',"new-tab":'<svg width="24" height="24"><path d="M15 13l2-2v8H5V7h8l-2 2H7v8h8v-4zm4-8v5.5l-2-2-5.6 5.5H10v-1.4L15.5 7l-2-2H19z" fill-rule="evenodd"/></svg>',"non-breaking":'<svg width="24" height="24"><path d="M11 11H8a1 1 0 1 1 0-2h3V6c0-.6.4-1 1-1s1 .4 1 1v3h3c.6 0 1 .4 1 1s-.4 1-1 1h-3v3c0 .6-.4 1-1 1a1 1 0 0 1-1-1v-3zm10 4v5H3v-5c0-.6.4-1 1-1s1 .4 1 1v3h14v-3c0-.6.4-1 1-1s1 .4 1 1z" fill-rule="evenodd"/></svg>',notice:'<svg width="24" height="24"><path d="M17.8 9.8L15.4 4 20 8.5v7L15.5 20h-7L4 15.5v-7L8.5 4h7l2.3 5.8zm0 0l2.2 5.7-2.3-5.8zM13 17v-2h-2v2h2zm0-4V7h-2v6h2z" fill-rule="evenodd"/></svg>',"ordered-list":'<svg width="24" height="24"><path d="M10 17h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm0-6h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm0-6h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 1 1 0-2zM6 4v3.5c0 .3-.2.5-.5.5a.5.5 0 0 1-.5-.5V5h-.5a.5.5 0 0 1 0-1H6zm-1 8.8l.2.2h1.3c.3 0 .5.2.5.5s-.2.5-.5.5H4.9a1 1 0 0 1-.9-1V13c0-.4.3-.8.6-1l1.2-.4.2-.3a.2.2 0 0 0-.2-.2H4.5a.5.5 0 0 1-.5-.5c0-.3.2-.5.5-.5h1.6c.5 0 .9.4.9 1v.1c0 .4-.3.8-.6 1l-1.2.4-.2.3zM7 17v2c0 .6-.4 1-1 1H4.5a.5.5 0 0 1 0-1h1.2c.2 0 .3-.1.3-.3 0-.2-.1-.3-.3-.3H4.4a.4.4 0 1 1 0-.8h1.3c.2 0 .3-.1.3-.3 0-.2-.1-.3-.3-.3H4.5a.5.5 0 1 1 0-1H6c.6 0 1 .4 1 1z" fill-rule="evenodd"/></svg>',orientation:'<svg width="24" height="24"><path d="M7.3 6.4L1 13l6.4 6.5 6.5-6.5-6.5-6.5zM3.7 13l3.6-3.7L11 13l-3.7 3.7-3.6-3.7zM12 6l2.8 2.7c.3.3.3.8 0 1-.3.4-.9.4-1.2 0L9.2 5.7a.8.8 0 0 1 0-1.2L13.6.2c.3-.3.9-.3 1.2 0 .3.3.3.8 0 1.1L12 4h1a9 9 0 1 1-4.3 16.9l1.5-1.5A7 7 0 1 0 13 6h-1z" fill-rule="nonzero"/></svg>',outdent:'<svg width="24" height="24"><path d="M7 5h12c.6 0 1 .4 1 1s-.4 1-1 1H7a1 1 0 1 1 0-2zm5 4h7c.6 0 1 .4 1 1s-.4 1-1 1h-7a1 1 0 0 1 0-2zm0 4h7c.6 0 1 .4 1 1s-.4 1-1 1h-7a1 1 0 0 1 0-2zm-5 4h12a1 1 0 0 1 0 2H7a1 1 0 0 1 0-2zm1.6-3.8a1 1 0 0 1-1.2 1.6l-3-2a1 1 0 0 1 0-1.6l3-2a1 1 0 0 1 1.2 1.6L6.8 12l1.8 1.2z" fill-rule="evenodd"/></svg>',"page-break":'<svg width="24" height="24"><g fill-rule="evenodd"><path d="M5 11c.6 0 1 .4 1 1s-.4 1-1 1a1 1 0 0 1 0-2zm3 0h1c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 0 1 0-2zm4 0c.6 0 1 .4 1 1s-.4 1-1 1a1 1 0 0 1 0-2zm3 0h1c.6 0 1 .4 1 1s-.4 1-1 1h-1a1 1 0 0 1 0-2zm4 0c.6 0 1 .4 1 1s-.4 1-1 1a1 1 0 0 1 0-2zM7 3v5h10V3c0-.6.4-1 1-1s1 .4 1 1v7H5V3c0-.6.4-1 1-1s1 .4 1 1zM6 22a1 1 0 0 1-1-1v-7h14v7c0 .6-.4 1-1 1a1 1 0 0 1-1-1v-5H7v5c0 .6-.4 1-1 1z"/></g></svg>',paragraph:'<svg width="24" height="24"><path d="M10 5h7a1 1 0 0 1 0 2h-1v11a1 1 0 0 1-2 0V7h-2v11a1 1 0 0 1-2 0v-6c-.5 0-1 0-1.4-.3A3.4 3.4 0 0 1 6.8 10a3.3 3.3 0 0 1 0-2.8 3.4 3.4 0 0 1 1.8-1.8L10 5z" fill-rule="evenodd"/></svg>',"paste-text":'<svg width="24" height="24"><path d="M18 9V5h-2v1c0 .6-.4 1-1 1H9a1 1 0 0 1-1-1V5H6v13h3V9h9zM9 20H6a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.2A3 3 0 0 1 12 1a3 3 0 0 1 2.8 2H18a2 2 0 0 1 2 2v4h1v12H9v-1zm1.5-9.5v9h9v-9h-9zM12 3a1 1 0 0 0-1 1c0 .5.4 1 1 1s1-.5 1-1-.4-1-1-1zm0 9h6v2h-.5l-.5-1h-1v4h.8v1h-3.6v-1h.8v-4h-1l-.5 1H12v-2z" fill-rule="nonzero"/></svg>',paste:'<svg width="24" height="24"><path d="M18 9V5h-2v1c0 .6-.4 1-1 1H9a1 1 0 0 1-1-1V5H6v13h3V9h9zM9 20H6a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.2A3 3 0 0 1 12 1a3 3 0 0 1 2.8 2H18a2 2 0 0 1 2 2v4h1v12H9v-1zm1.5-9.5v9h9v-9h-9zM12 3a1 1 0 0 0-1 1c0 .5.4 1 1 1s1-.5 1-1-.4-1-1-1z" fill-rule="nonzero"/></svg>',"permanent-pen":'<svg width="24" height="24"><path d="M10.5 17.5L8 20H3v-3l3.5-3.5a2 2 0 0 1 0-3L14 3l1 1-7.3 7.3a1 1 0 0 0 0 1.4l3.6 3.6c.4.4 1 .4 1.4 0L20 9l1 1-7.6 7.6a2 2 0 0 1-2.8 0l-.1-.1z" fill-rule="nonzero"/></svg>',plus:'<svg width="24" height="24"><g fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round" stroke="#000" stroke-width="2"><path d="M12 5v14M5 12h14"/></g></svg>',preferences:'<svg width="24" height="24"><path d="M20.1 13.5l-1.9.2a5.8 5.8 0 0 1-.6 1.5l1.2 1.5c.4.4.3 1 0 1.4l-.7.7a1 1 0 0 1-1.4 0l-1.5-1.2a6.2 6.2 0 0 1-1.5.6l-.2 1.9c0 .5-.5.9-1 .9h-1a1 1 0 0 1-1-.9l-.2-1.9a5.8 5.8 0 0 1-1.5-.6l-1.5 1.2a1 1 0 0 1-1.4 0l-.7-.7a1 1 0 0 1 0-1.4l1.2-1.5a6.2 6.2 0 0 1-.6-1.5l-1.9-.2a1 1 0 0 1-.9-1v-1c0-.5.4-1 .9-1l1.9-.2a5.8 5.8 0 0 1 .6-1.5L5.2 7.3a1 1 0 0 1 0-1.4l.7-.7a1 1 0 0 1 1.4 0l1.5 1.2a6.2 6.2 0 0 1 1.5-.6l.2-1.9c0-.5.5-.9 1-.9h1c.5 0 1 .4 1 .9l.2 1.9a5.8 5.8 0 0 1 1.5.6l1.5-1.2a1 1 0 0 1 1.4 0l.7.7c.3.4.4 1 0 1.4l-1.2 1.5a6.2 6.2 0 0 1 .6 1.5l1.9.2c.5 0 .9.5.9 1v1c0 .5-.4 1-.9 1zM12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6z" fill-rule="evenodd"/></svg>',preview:'<svg width="24" height="24"><path d="M3.5 12.5c.5.8 1.1 1.6 1.8 2.3 2 2 4.2 3.2 6.7 3.2s4.7-1.2 6.7-3.2a16.2 16.2 0 0 0 2.1-2.8 15.7 15.7 0 0 0-2.1-2.8c-2-2-4.2-3.2-6.7-3.2a9.3 9.3 0 0 0-6.7 3.2A16.2 16.2 0 0 0 3.2 12c0 .2.2.3.3.5zm-2.4-1l.7-1.2L4 7.8C6.2 5.4 8.9 4 12 4c3 0 5.8 1.4 8.1 3.8a18.2 18.2 0 0 1 2.8 3.7v1l-.7 1.2-2.1 2.5c-2.3 2.4-5 3.8-8.1 3.8-3 0-5.8-1.4-8.1-3.8a18.2 18.2 0 0 1-2.8-3.7 1 1 0 0 1 0-1zm12-3.3a2 2 0 1 0 2.7 2.6 4 4 0 1 1-2.6-2.6z" fill-rule="nonzero"/></svg>',print:'<svg width="24" height="24"><path d="M18 8H6a3 3 0 0 0-3 3v6h2v3h14v-3h2v-6a3 3 0 0 0-3-3zm-1 10H7v-4h10v4zm.5-5c-.8 0-1.5-.7-1.5-1.5s.7-1.5 1.5-1.5 1.5.7 1.5 1.5-.7 1.5-1.5 1.5zm.5-8H6v2h12V5z" fill-rule="nonzero"/></svg>',quote:'<svg width="24" height="24"><path d="M7.5 17h.9c.4 0 .7-.2.9-.6L11 13V8c0-.6-.4-1-1-1H6a1 1 0 0 0-1 1v4c0 .6.4 1 1 1h2l-1.3 2.7a1 1 0 0 0 .8 1.3zm8 0h.9c.4 0 .7-.2.9-.6L19 13V8c0-.6-.4-1-1-1h-4a1 1 0 0 0-1 1v4c0 .6.4 1 1 1h2l-1.3 2.7a1 1 0 0 0 .8 1.3z" fill-rule="nonzero"/></svg>',redo:'<svg width="24" height="24"><path d="M17.6 10H12c-2.8 0-4.4 1.4-4.9 3.5-.4 2 .3 4 1.4 4.6a1 1 0 1 1-1 1.8c-2-1.2-2.9-4.1-2.3-6.8.6-3 3-5.1 6.8-5.1h5.6l-3.3-3.3a1 1 0 1 1 1.4-1.4l5 5a1 1 0 0 1 0 1.4l-5 5a1 1 0 0 1-1.4-1.4l3.3-3.3z" fill-rule="nonzero"/></svg>',reload:'<svg width="24" height="24"><g fill-rule="nonzero"><path d="M5 22.1l-1.2-4.7v-.2a1 1 0 0 1 1-1l5 .4a1 1 0 1 1-.2 2l-2.2-.2a7.8 7.8 0 0 0 8.4.2 7.5 7.5 0 0 0 3.5-6.4 1 1 0 1 1 2 0 9.5 9.5 0 0 1-4.5 8 9.9 9.9 0 0 1-10.2 0l.4 1.4a1 1 0 1 1-2 .5zM13.6 7.4c0-.5.5-1 1-.9l2.8.2a8 8 0 0 0-9.5-1 7.5 7.5 0 0 0-3.6 7 1 1 0 0 1-2 0 9.5 9.5 0 0 1 4.5-8.6 10 10 0 0 1 10.9.3l-.3-1a1 1 0 0 1 2-.5l1.1 4.8a1 1 0 0 1-1 1.2l-5-.4a1 1 0 0 1-.9-1z"/></g></svg>',"remove-formatting":'<svg width="24" height="24"><path d="M13.2 6a1 1 0 0 1 0 .2l-2.6 10a1 1 0 0 1-1 .8h-.2a.8.8 0 0 1-.8-1l2.6-10H8a1 1 0 1 1 0-2h9a1 1 0 0 1 0 2h-3.8zM5 18h7a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2zm13 1.5L16.5 18 15 19.5a.7.7 0 0 1-1-1l1.5-1.5-1.5-1.5a.7.7 0 0 1 1-1l1.5 1.5 1.5-1.5a.7.7 0 0 1 1 1L17.5 17l1.5 1.5a.7.7 0 0 1-1 1z" fill-rule="evenodd"/></svg>',remove:'<svg width="24" height="24"><path d="M16 7h3a1 1 0 0 1 0 2h-1v9a3 3 0 0 1-3 3H9a3 3 0 0 1-3-3V9H5a1 1 0 1 1 0-2h3V6a3 3 0 0 1 3-3h2a3 3 0 0 1 3 3v1zm-2 0V6c0-.6-.4-1-1-1h-2a1 1 0 0 0-1 1v1h4zm2 2H8v9c0 .6.4 1 1 1h6c.6 0 1-.4 1-1V9zm-7 3a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4zm4 0a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4z" fill-rule="nonzero"/></svg>',"resize-handle":'<svg width="10" height="10"><g fill-rule="nonzero"><path d="M8.1 1.1A.5.5 0 1 1 9 2l-7 7A.5.5 0 1 1 1 8l7-7zM8.1 5.1A.5.5 0 1 1 9 6l-3 3A.5.5 0 1 1 5 8l3-3z"/></g></svg>',resize:'<svg width="24" height="24"><path d="M4 5c0-.3.1-.5.3-.7.2-.2.4-.3.7-.3h6c.3 0 .5.1.7.3.2.2.3.4.3.7 0 .3-.1.5-.3.7a1 1 0 0 1-.7.3H7.4L18 16.6V13c0-.3.1-.5.3-.7.2-.2.4-.3.7-.3.3 0 .5.1.7.3.2.2.3.4.3.7v6c0 .3-.1.5-.3.7a1 1 0 0 1-.7.3h-6a1 1 0 0 1-.7-.3 1 1 0 0 1-.3-.7c0-.3.1-.5.3-.7.2-.2.4-.3.7-.3h3.6L6 7.4V11c0 .3-.1.5-.3.7a1 1 0 0 1-.7.3 1 1 0 0 1-.7-.3A1 1 0 0 1 4 11V5z" fill-rule="evenodd"/></svg>',"restore-draft":'<svg width="24" height="24"><g fill-rule="evenodd"><path d="M17 13c0 .6-.4 1-1 1h-4V8c0-.6.4-1 1-1s1 .4 1 1v4h2c.6 0 1 .4 1 1z"/><path d="M4.7 10H9a1 1 0 0 1 0 2H3a1 1 0 0 1-1-1V5a1 1 0 1 1 2 0v3l2.5-2.4a9.2 9.2 0 0 1 10.8-1.5A9 9 0 0 1 13.4 21c-2.4.1-4.7-.7-6.5-2.2a1 1 0 1 1 1.3-1.5 7.2 7.2 0 0 0 11.6-3.7 7 7 0 0 0-3.5-7.7A7.2 7.2 0 0 0 8 7L4.7 10z" fill-rule="nonzero"/></g></svg>',"rotate-left":'<svg width="24" height="24"><path d="M4.7 10H9a1 1 0 0 1 0 2H3a1 1 0 0 1-1-1V5a1 1 0 1 1 2 0v3l2.5-2.4a9.2 9.2 0 0 1 10.8-1.5A9 9 0 0 1 13.4 21c-2.4.1-4.7-.7-6.5-2.2a1 1 0 1 1 1.3-1.5 7.2 7.2 0 0 0 11.6-3.7 7 7 0 0 0-3.5-7.7A7.2 7.2 0 0 0 8 7L4.7 10z" fill-rule="nonzero"/></svg>',"rotate-right":'<svg width="24" height="24"><path d="M20 8V5a1 1 0 0 1 2 0v6c0 .6-.4 1-1 1h-6a1 1 0 0 1 0-2h4.3L16 7A7.2 7.2 0 0 0 7.7 6a7 7 0 0 0 3 13.1c1.9.1 3.7-.5 5-1.7a1 1 0 0 1 1.4 1.5A9.2 9.2 0 0 1 2.2 14c-.9-3.9 1-8 4.5-9.9 3.5-1.9 8-1.3 10.8 1.5L20 8z" fill-rule="nonzero"/></svg>',rtl:'<svg width="24" height="24"><path d="M8 5h8v2h-2v12h-2V7h-2v12H8v-7c-.5 0-1 0-1.4-.3A3.4 3.4 0 0 1 4.8 10a3.3 3.3 0 0 1 0-2.8 3.4 3.4 0 0 1 1.8-1.8L8 5zm12 11.2a1 1 0 1 1-1 1.6l-3-2a1 1 0 0 1 0-1.6l3-2a1 1 0 1 1 1 1.6L18.4 15l1.8 1.2z" fill-rule="evenodd"/></svg>',save:'<svg width="24" height="24"><path d="M5 16h14a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-2c0-1.1.9-2 2-2zm0 2v2h14v-2H5zm10 0h2v2h-2v-2zm-4-6.4L8.7 9.3a1 1 0 1 0-1.4 1.4l4 4c.4.4 1 .4 1.4 0l4-4a1 1 0 1 0-1.4-1.4L13 11.6V4a1 1 0 0 0-2 0v7.6z" fill-rule="nonzero"/></svg>',search:'<svg width="24" height="24"><path d="M16 17.3a8 8 0 1 1 1.4-1.4l4.3 4.4a1 1 0 0 1-1.4 1.4l-4.4-4.3zm-5-.3a6 6 0 1 0 0-12 6 6 0 0 0 0 12z" fill-rule="nonzero"/></svg>',"select-all":'<svg width="24" height="24"><path d="M3 5h2V3a2 2 0 0 0-2 2zm0 8h2v-2H3v2zm4 8h2v-2H7v2zM3 9h2V7H3v2zm10-6h-2v2h2V3zm6 0v2h2a2 2 0 0 0-2-2zM5 21v-2H3c0 1.1.9 2 2 2zm-2-4h2v-2H3v2zM9 3H7v2h2V3zm2 18h2v-2h-2v2zm8-8h2v-2h-2v2zm0 8a2 2 0 0 0 2-2h-2v2zm0-12h2V7h-2v2zm0 8h2v-2h-2v2zm-4 4h2v-2h-2v2zm0-16h2V3h-2v2zM7 17h10V7H7v10zm2-8h6v6H9V9z" fill-rule="nonzero"/></svg>',selected:'<svg width="24" height="24"><path d="M12 21a9 9 0 1 1 0-18 9 9 0 0 1 0 18zm-2.4-6.1L7 12.3a.7.7 0 0 0-1 1L9.6 17 18 8.6a.7.7 0 0 0 0-1 .7.7 0 0 0-1 0l-7.4 7.3z" fill-rule="evenodd"/></svg>',settings:'<svg width="24" height="24"><path d="M11 6h8c.6 0 1 .4 1 1s-.4 1-1 1h-8v.3c0 .2 0 .3-.2.5l-.6.2H7.8c-.3 0-.4 0-.6-.2a.7.7 0 0 1-.2-.6V8H5a1 1 0 1 1 0-2h2v-.3c0-.2 0-.3.2-.5l.5-.2h2.5c.3 0 .4 0 .6.2l.2.5V6zM8 8h2V6H8v2zm9 2.8v.2h2c.6 0 1 .4 1 1s-.4 1-1 1h-2v.3c0 .2 0 .3-.2.5l-.6.2h-2.4c-.3 0-.4 0-.6-.2a.7.7 0 0 1-.2-.6V13H5a1 1 0 0 1 0-2h8v-.3c0-.2 0-.3.2-.5l.6-.2h2.4c.3 0 .4 0 .6.2l.2.6zM14 13h2v-2h-2v2zm-3 2.8v.2h8c.6 0 1 .4 1 1s-.4 1-1 1h-8v.3c0 .2 0 .3-.2.5l-.6.2H7.8c-.3 0-.4 0-.6-.2a.7.7 0 0 1-.2-.6V18H5a1 1 0 0 1 0-2h2v-.3c0-.2 0-.3.2-.5l.5-.2h2.5c.3 0 .4 0 .6.2l.2.6zM8 18h2v-2H8v2z" fill-rule="evenodd"/></svg>',sharpen:'<svg width="24" height="24"><path d="M16 6l4 4-8 9-8-9 4-4h8zm-4 10.2l5.5-6.2-.1-.1H12v-.3h5.1l-.2-.2H12V9h4.6l-.2-.2H12v-.3h4.1l-.2-.2H12V8h3.6l-.2-.2H8.7L6.5 10l.1.1H12v.3H6.9l.2.2H12v.3H7.3l.2.2H12v.3H7.7l.3.2h4v.3H8.2l.2.2H12v.3H8.6l.3.2H12v.3H9l.3.2H12v.3H9.5l.2.2H12v.3h-2l.2.2H12v.3h-1.6l.2.2H12v.3h-1.1l.2.2h.9v.3h-.7l.2.2h.5v.3h-.3l.3.2z" fill-rule="evenodd"/></svg>',sourcecode:'<svg width="24" height="24"><g fill-rule="nonzero"><path d="M9.8 15.7c.3.3.3.8 0 1-.3.4-.9.4-1.2 0l-4.4-4.1a.8.8 0 0 1 0-1.2l4.4-4.2c.3-.3.9-.3 1.2 0 .3.3.3.8 0 1.1L6 12l3.8 3.7zM14.2 15.7c-.3.3-.3.8 0 1 .4.4.9.4 1.2 0l4.4-4.1c.3-.3.3-.9 0-1.2l-4.4-4.2a.8.8 0 0 0-1.2 0c-.3.3-.3.8 0 1.1L18 12l-3.8 3.7z"/></g></svg>',"spell-check":'<svg width="24" height="24"><path d="M6 8v3H5V5c0-.3.1-.5.3-.7.2-.2.4-.3.7-.3h2c.3 0 .5.1.7.3.2.2.3.4.3.7v6H8V8H6zm0-3v2h2V5H6zm13 0h-3v5h3v1h-3a1 1 0 0 1-.7-.3 1 1 0 0 1-.3-.7V5c0-.3.1-.5.3-.7.2-.2.4-.3.7-.3h3v1zm-5 1.5l-.1.7c-.1.2-.3.3-.6.3.3 0 .5.1.6.3l.1.7V10c0 .3-.1.5-.3.7a1 1 0 0 1-.7.3h-3V4h3c.3 0 .5.1.7.3.2.2.3.4.3.7v1.5zM13 10V8h-2v2h2zm0-3V5h-2v2h2zm3 5l1 1-6.5 7L7 15.5l1.3-1 2.2 2.2L16 12z" fill-rule="evenodd"/></svg>',"strike-through":'<svg width="24" height="24"><g fill-rule="evenodd"><path d="M15.6 8.5c-.5-.7-1-1.1-1.3-1.3-.6-.4-1.3-.6-2-.6-2.7 0-2.8 1.7-2.8 2.1 0 1.6 1.8 2 3.2 2.3 4.4.9 4.6 2.8 4.6 3.9 0 1.4-.7 4.1-5 4.1A6.2 6.2 0 0 1 7 16.4l1.5-1.1c.4.6 1.6 2 3.7 2 1.6 0 2.5-.4 3-1.2.4-.8.3-2-.8-2.6-.7-.4-1.6-.7-2.9-1-1-.2-3.9-.8-3.9-3.6C7.6 6 10.3 5 12.4 5c2.9 0 4.2 1.6 4.7 2.4l-1.5 1.1z"/><path d="M5 11h14a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2z" fill-rule="nonzero"/></g></svg>',subscript:'<svg width="24" height="24"><path d="M10.4 10l4.6 4.6-1.4 1.4L9 11.4 4.4 16 3 14.6 7.6 10 3 5.4 4.4 4 9 8.6 13.6 4 15 5.4 10.4 10zM21 19h-5v-1l1-.8 1.7-1.6c.3-.4.5-.8.5-1.2 0-.3 0-.6-.2-.7-.2-.2-.5-.3-.9-.3a2 2 0 0 0-.8.2l-.7.3-.4-1.1 1-.6 1.2-.2c.8 0 1.4.3 *******.******* 1.5s-.2 1.1-.5 1.6a8 8 0 0 1-1.3 1.3l-.6.6h2.6V19z" fill-rule="nonzero"/></svg>',superscript:'<svg width="24" height="24"><path d="M15 9.4L10.4 14l4.6 4.6-1.4 1.4L9 15.4 4.4 20 3 18.6 7.6 14 3 9.4 4.4 8 9 12.6 13.6 8 15 9.4zm5.9 1.6h-5v-1l1-.8 1.7-1.6c.3-.5.5-.9.5-1.3 0-.3 0-.5-.2-.7-.2-.2-.5-.3-.9-.3l-.8.2-.7.4-.4-1.2c.2-.2.5-.4 1-.5.3-.2.8-.2 1.2-.2.8 0 1.4.2 *******.4.6 1 .6 1.6 0 .5-.2 1-.5 1.5l-1.3 1.4-.6.5h2.6V11z" fill-rule="nonzero"/></svg>',"table-cell-properties":'<svg width="24" height="24"><path d="M4 5h16v14H4V5zm10 10h-4v3h4v-3zm0-8h-4v3h4V7zM9 7H5v3h4V7zm-4 4v3h4v-3H5zm10 0v3h4v-3h-4zm0-1h4V7h-4v3zM5 15v3h4v-3H5zm10 3h4v-3h-4v3z" fill-rule="evenodd"/></svg>',"table-cell-select-all":'<svg width="24" height="24"><path d="M12.5 5.5v6h6v-6h-6zm-1 0h-6v6h6v-6zm1 13h6v-6h-6v6zm-1 0v-6h-6v6h6zm-7-14h15v15h-15v-15z" fill-rule="nonzero"/></svg>',"table-cell-select-inner":'<svg width="24" height="24"><g fill-rule="nonzero"><path d="M5.5 5.5v13h13v-13h-13zm-1-1h15v15h-15v-15z" opacity=".2"/><path d="M11.5 11.5v-7h1v7h7v1h-7v7h-1v-7h-7v-1h7z"/></g></svg>',"table-delete-column":'<svg width="24" height="24"><path d="M9 11.2l1 1v.2l-1 1v-2.2zm5 1l1-1v2.2l-1-1v-.2zM20 5v14H4V5h16zm-1 2h-4v.8l-.2-.2-.8.8V7h-4v1.4l-.8-.8-.2.2V7H5v11h4v-1.8l.5.5.5-.4V18h4v-1.8l.8.8.2-.3V18h4V7zm-3.9 3.4l-1.8 1.9 1.8 1.9c.4.3.4.9 0 1.2-.3.3-.8.3-1.2 0L12 13.5l-1.8 1.9a.8.8 0 0 1-1.2 0 .9.9 0 0 1 0-1.2l1.8-1.9-1.9-2a.9.9 0 0 1 1.2-1.2l2 2 1.8-1.8c.3-.4.9-.4 1.2 0a.8.8 0 0 1 0 1.1z" fill-rule="evenodd"/></svg>',"table-delete-row":'<svg width="24" height="24"><path d="M16.7 8.8l1.1 1.2-2.4 2.5L18 15l-1.2 1.2-2.5-2.5-2.4 2.5-1.3-1.2 2.5-2.5-2.5-2.5 1.2-1.3 2.6 2.6 2.4-2.5zM4 5h16v14H4V5zm15 5V7H5v3h4.8l1 1H5v3h5.8l-1 1H5v3h14v-3h-.4l-1-1H19v-3h-1.3l1-1h.3z" fill-rule="evenodd"/></svg>',"table-delete-table":'<svg width="24" height="26"><path d="M4 6h16v14H4V6zm1 2v11h14V8H5zm11.7 8.7l-1.5 1.5L12 15l-3.3 3.2-1.4-1.5 3.2-3.2-3.3-3.2 1.5-1.5L12 12l3.2-3.2 1.5 1.5-3.2 3.2 3.2 3.2z" fill-rule="evenodd"/></svg>',"table-insert-column-after":'<svg width="24" height="24"><path d="M14.3 9c.4 0 .7.3.7.6v2.2h2.1c.4 0 .7.3.7.7 0 .4-.3.7-.7.7H15v2.2c0 .3-.3.6-.7.6a.7.7 0 0 1-.6-.6v-2.2h-2.2a.7.7 0 0 1 0-1.4h2.2V9.6c0-.3.3-.6.6-.6zM4 5h16v14H4V5zm5 13v-3H5v3h4zm0-4v-3H5v3h4zm0-4V7H5v3h4zm10 8V7h-9v11h9z" fill-rule="evenodd"/></svg>',"table-insert-column-before":'<svg width="24" height="24"><path d="M9.7 16a.7.7 0 0 1-.7-.6v-2.2H6.9a.7.7 0 0 1 0-1.4H9V9.6c0-.3.3-.6.7-.6.3 0 .6.3.6.6v2.2h2.2c.4 0 .8.3.8.7 0 .4-.4.7-.8.7h-2.2v2.2c0 .3-.3.6-.6.6zM4 5h16v14H4V5zm10 13V7H5v11h9zm5 0v-3h-4v3h4zm0-4v-3h-4v3h4zm0-4V7h-4v3h4z" fill-rule="evenodd"/></svg>',"table-insert-row-above":'<svg width="24" height="24"><path d="M14.8 10.5c0 .3-.2.5-.5.5h-1.8v1.8c0 .3-.2.5-.5.5a.5.5 0 0 1-.5-.6V11H9.7a.5.5 0 0 1 0-1h1.8V8.3c0-.3.2-.6.5-.6s.5.3.5.6V10h1.8c.3 0 .5.2.5.5zM4 5h16v14H4V5zm5 13v-3H5v3h4zm5 0v-3h-4v3h4zm5 0v-3h-4v3h4zm0-4V7H5v7h14z" fill-rule="evenodd"/></svg>',"table-insert-row-after":'<svg width="24" height="24"><path d="M9.2 14.5c0-.3.2-.5.5-.5h1.8v-1.8c0-.3.2-.5.5-.5s.5.2.5.6V14h1.8c.3 0 .5.2.5.5s-.2.5-.5.5h-1.8v1.7c0 .3-.2.6-.5.6a.5.5 0 0 1-.5-.6V15H9.7a.5.5 0 0 1-.5-.5zM4 5h16v14H4V5zm6 2v3h4V7h-4zM5 7v3h4V7H5zm14 11v-7H5v7h14zm0-8V7h-4v3h4z" fill-rule="evenodd"/></svg>',"table-left-header":'<svg width="24" height="24"><path d="M4 5h16v13H4V5zm10 12v-3h-4v3h4zm0-4v-3h-4v3h4zm0-4V6h-4v3h4zm5 8v-3h-4v3h4zm0-4v-3h-4v3h4zm0-4V6h-4v3h4z" fill-rule="evenodd"/></svg>',"table-merge-cells":'<svg width="24" height="24"><path d="M4 5h16v14H4V5zm6 13h9v-7h-9v7zm4-11h-4v3h4V7zM9 7H5v3h4V7zm-4 4v3h4v-3H5zm10-1h4V7h-4v3zM5 15v3h4v-3H5z" fill-rule="evenodd"/></svg>',"table-row-properties":'<svg width="24" height="24"><path d="M4 5h16v14H4V5zm10 10h-4v3h4v-3zm0-8h-4v3h4V7zM9 7H5v3h4V7zm6 3h4V7h-4v3zM5 15v3h4v-3H5zm10 3h4v-3h-4v3z" fill-rule="evenodd"/></svg>',"table-split-cells":'<svg width="24" height="24"><path d="M4 5h16v14H4V5zm6 2v3h4V7h-4zM9 18v-3H5v3h4zm0-4v-3H5v3h4zm0-4V7H5v3h4zm10 8v-7h-9v7h9zm0-8V7h-4v3h4zm-3.5 4.5l1.5 1.6c.3.2.3.7 0 1-.2.2-.7.2-1 0l-1.5-1.6-1.6 1.5c-.2.3-.7.3-1 0a.7.7 0 0 1 0-1l1.6-1.5-1.5-1.6a.7.7 0 0 1 1-1l1.5 1.6 1.6-1.5c.2-.3.7-.3 1 0 .2.2.2.7 0 1l-1.6 1.5z" fill-rule="evenodd"/></svg>',"table-top-header":'<svg width="24" height="24"><path d="M4 5h16v13H4V5zm5 12v-3H5v3h4zm0-4v-3H5v3h4zm5 4v-3h-4v3h4zm0-4v-3h-4v3h4zm5 4v-3h-4v3h4zm0-4v-3h-4v3h4z" fill-rule="evenodd"/></svg>',table:'<svg width="24" height="24"><path d="M4 5h16v14H4V5zm6 9h4v-3h-4v3zm4 1h-4v3h4v-3zm0-8h-4v3h4V7zM9 7H5v3h4V7zm-4 4v3h4v-3H5zm10 0v3h4v-3h-4zm0-1h4V7h-4v3zM5 15v3h4v-3H5zm10 3h4v-3h-4v3z" fill-rule="evenodd"/></svg>',template:'<svg width="24" height="24"><path d="M19 19v-1H5v1h14zM9 16v-4a5 5 0 1 1 6 0v4h4a2 2 0 0 1 2 2v3H3v-3c0-1.1.9-2 2-2h4zm4 0v-5l.8-.6a3 3 0 1 0-3.6 0l.8.6v5h2z" fill-rule="nonzero"/></svg>',"temporary-placeholder":'<svg width="24" height="24"><path d="M20.5 2.5c-.8 0-1.5.7-1.5 1.5a1.5 1.5 0 0 1-3 0 3 3 0 0 0-6 0v2H8.5c-.3 0-.5.2-.5.5v1a8 8 0 1 0 6 0v-1c0-.3-.2-.5-.5-.5H11V4a2 2 0 0 1 4 0 2.5 2.5 0 0 0 5 0c0-.3.2-.5.5-.5a.5.5 0 0 0 0-1zM8.1 10.9a5 5 0 0 0-1.2 7 .5.5 0 0 1-.8.5 6 6 0 0 1 1.5-8.3.5.5 0 1 1 .5.8z" fill-rule="nonzero"/></svg>',"text-color":'<svg width="24" height="24"><g fill-rule="evenodd"><path id="tox-icon-text-color__color" d="M3 18h18v3H3z"/><path d="M8.7 16h-.8a.5.5 0 0 1-.5-.6l2.7-9c.1-.3.3-.4.5-.4h2.8c.2 0 .4.1.5.4l2.7 9a.5.5 0 0 1-.5.6h-.8a.5.5 0 0 1-.4-.4l-.7-2.2c0-.3-.3-.4-.5-.4h-3.4c-.2 0-.4.1-.5.4l-.7 2.2c0 .3-.2.4-.4.4zm2.6-7.6l-.6 2a.5.5 0 0 0 .5.6h1.6a.5.5 0 0 0 .5-.6l-.6-2c0-.3-.3-.4-.5-.4h-.4c-.2 0-.4.1-.5.4z"/></g></svg>',toc:'<svg width="24" height="24"><path d="M5 5c.6 0 1 .4 1 1s-.4 1-1 1a1 1 0 1 1 0-2zm3 0h11c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 1 1 0-2zm-3 8c.6 0 1 .4 1 1s-.4 1-1 1a1 1 0 0 1 0-2zm3 0h11c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 0 1 0-2zm0-4c.6 0 1 .4 1 1s-.4 1-1 1a1 1 0 1 1 0-2zm3 0h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm-3 8c.6 0 1 .4 1 1s-.4 1-1 1a1 1 0 0 1 0-2zm3 0h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2z" fill-rule="evenodd"/></svg>',translate:'<svg width="24" height="24"><path d="M12.7 14.3l-.3.7-.4.7-2.2-2.2-3.1 3c-.3.4-.8.4-1 0a.7.7 0 0 1 0-1l3.1-3A12.4 12.4 0 0 1 6.7 9H8a10.1 10.1 0 0 0 1.7 2.4c.5-.5 1-1.1 1.4-1.8l.9-2H4.7a.7.7 0 1 1 0-1.5h4.4v-.7c0-.4.3-.8.7-.8.4 0 .7.4.7.8v.7H15c.4 0 .8.3.8.7 0 .4-.4.8-.8.8h-1.4a12.3 12.3 0 0 1-1 2.4 13.5 13.5 0 0 1-1.7 2.3l1.9 1.8zm4.3-3l2.7 7.3a.5.5 0 0 1-.4.7 1 1 0 0 1-1-.7l-.6-1.5h-3.4l-.6 1.5a1 1 0 0 1-1 .7.5.5 0 0 1-.4-.7l2.7-7.4a1 1 0 1 1 2 0zm-2.2 4.4h2.4L16 12.5l-1.2 3.2z" fill-rule="evenodd"/></svg>',underline:'<svg width="24" height="24"><path d="M16 5c.6 0 1 .4 1 1v5.5a4 4 0 0 1-.4 1.8l-1 1.4a5.3 5.3 0 0 1-5.5 1 5 5 0 0 1-1.6-1c-.5-.4-.8-.9-1.1-1.4a4 4 0 0 1-.4-1.8V6c0-.6.4-1 1-1s1 .4 1 1v5.5c0 .3 0 .6.2 1l.6.7a3.3 3.3 0 0 0 2.2.8 3.4 3.4 0 0 0 2.2-.8c.3-.2.4-.5.6-.8l.2-.9V6c0-.6.4-1 1-1zM8 17h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 0 1 0-2z" fill-rule="evenodd"/></svg>',undo:'<svg width="24" height="24"><path d="M6.4 8H12c3.7 0 6.2 2 6.8 5.1.6 2.7-.4 5.6-2.3 6.8a1 1 0 0 1-1-1.8c1.1-.6 1.8-2.7 1.4-4.6-.5-2.1-2.1-3.5-4.9-3.5H6.4l3.3 3.3a1 1 0 1 1-1.4 1.4l-5-5a1 1 0 0 1 0-1.4l5-5a1 1 0 0 1 1.4 1.4L6.4 8z" fill-rule="nonzero"/></svg>',unlink:'<svg width="24" height="24"><path d="M6.2 12.3a1 1 0 0 1 1.4 1.4l-2 2a2 2 0 1 0 2.6 2.8l4.8-4.8a1 1 0 0 0 0-1.4 1 1 0 1 1 1.4-1.3 2.9 2.9 0 0 1 0 4L9.6 20a3.9 3.9 0 0 1-5.5-5.5l2-2zm11.6-.6a1 1 0 0 1-1.4-1.4l2.1-2a2 2 0 1 0-2.7-2.8L11 10.3a1 1 0 0 0 0 1.4A1 1 0 1 1 9.6 13a2.9 2.9 0 0 1 0-4L14.4 4a3.9 3.9 0 0 1 5.5 5.5l-2 2zM7.6 6.3a.8.8 0 0 1-1 1.1L3.3 4.2a.7.7 0 1 1 1-1l3.2 3.1zM5.1 8.6a.8.8 0 0 1 0 1.5H3a.8.8 0 0 1 0-1.5H5zm5-3.5a.8.8 0 0 1-1.5 0V3a.8.8 0 0 1 1.5 0V5zm6 11.8a.8.8 0 0 1 1-1l3.2 3.2a.8.8 0 0 1-1 1L16 17zm-2.2 2a.8.8 0 0 1 1.5 0V21a.8.8 0 0 1-1.5 0V19zm5-3.5a.7.7 0 1 1 0-1.5H21a.8.8 0 0 1 0 1.5H19z" fill-rule="nonzero"/></svg>',unlock:'<svg width="24" height="24"><path d="M16 5c.8 0 1.5.3 2.1.9.6.6.9 1.3.9 2.1v3h-2V8a1 1 0 0 0-.3-.7A1 1 0 0 0 16 7h-2a1 1 0 0 0-.7.3 1 1 0 0 0-.3.7v3h.3c.2 0 .3 0 .5.2l.2.6v7.4c0 .3 0 .4-.2.6l-.6.2H4.8c-.3 0-.4 0-.6-.2a.7.7 0 0 1-.2-.6v-7.4c0-.3 0-.4.2-.6l.5-.2H11V8c0-.8.3-1.5.9-2.1.6-.6 1.3-.9 2.1-.9h2z" fill-rule="evenodd"/></svg>',"unordered-list":'<svg width="24" height="24"><path d="M11 5h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm0 6h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm0 6h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zM4.5 6c0-.4.1-.8.4-1 .3-.4.7-.5 1.1-.5.4 0 .8.1 1 .4.4.3.5.7.5 1.1 0 .4-.1.8-.4 1-.3.4-.7.5-1.1.5-.4 0-.8-.1-1-.4-.4-.3-.5-.7-.5-1.1zm0 6c0-.4.1-.8.4-1 .3-.4.7-.5 1.1-.5.4 0 .8.1 1 .4.4.3.5.7.5 1.1 0 .4-.1.8-.4 1-.3.4-.7.5-1.1.5-.4 0-.8-.1-1-.4-.4-.3-.5-.7-.5-1.1zm0 6c0-.4.1-.8.4-1 .3-.4.7-.5 1.1-.5.4 0 .8.1 1 .4.4.3.5.7.5 1.1 0 .4-.1.8-.4 1-.3.4-.7.5-1.1.5-.4 0-.8-.1-1-.4-.4-.3-.5-.7-.5-1.1z" fill-rule="evenodd"/></svg>',unselected:'<svg width="24" height="24"><path d="M12 21a9 9 0 1 1 0-18 9 9 0 0 1 0 18zm0-1a8 8 0 1 0 0-16 8 8 0 0 0 0 16z" fill-rule="evenodd"/></svg>',upload:'<svg width="24" height="24"><path d="M18 19v-2a1 1 0 0 1 2 0v3c0 .6-.4 1-1 1H5a1 1 0 0 1-1-1v-3a1 1 0 0 1 2 0v2h12zM11 6.4L8.7 8.7a1 1 0 0 1-1.4-1.4l4-4a1 1 0 0 1 1.4 0l4 4a1 1 0 1 1-1.4 1.4L13 6.4V16a1 1 0 0 1-2 0V6.4z" fill-rule="nonzero"/></svg>',user:'<svg width="24" height="24"><path d="M12 24a12 12 0 1 1 0-24 12 12 0 0 1 0 24zm-8.7-5.3a11 11 0 0 0 17.4 0C19.4 16.3 14.6 15 12 15c-2.6 0-7.4 1.3-8.7 3.7zM12 13c2.2 0 4-2 4-4.5S14.2 4 12 4 8 6 8 8.5 9.8 13 12 13z" fill-rule="nonzero"/></svg>',warning:'<svg width="24" height="24"><path d="M19.8 18.3c.2.5.3.9 0 1.2-.1.3-.5.5-1 .5H5.2c-.5 0-.9-.2-1-.5-.3-.3-.2-.7 0-1.2L11 4.7l.5-.5.5-.2c.2 0 .3 0 .5.2.2 0 .3.3.5.5l6.8 13.6zM12 18c.3 0 .5-.1.7-.3.2-.2.3-.4.3-.7a1 1 0 0 0-.3-.7 1 1 0 0 0-.7-.3 1 1 0 0 0-.7.3 1 1 0 0 0-.3.7c0 .3.1.5.3.7.2.2.4.3.7.3zm.7-3l.3-4a1 1 0 0 0-.3-.7 1 1 0 0 0-.7-.3 1 1 0 0 0-.7.3 1 1 0 0 0-.3.7l.3 4h1.4z" fill-rule="evenodd"/></svg>',"zoom-in":'<svg width="24" height="24"><path d="M16 17.3a8 8 0 1 1 1.4-1.4l4.3 4.4a1 1 0 0 1-1.4 1.4l-4.4-4.3zm-5-.3a6 6 0 1 0 0-12 6 6 0 0 0 0 12zm-1-9a1 1 0 0 1 2 0v6a1 1 0 0 1-2 0V8zm-2 4a1 1 0 0 1 0-2h6a1 1 0 0 1 0 2H8z" fill-rule="nonzero"/></svg>',"zoom-out":'<svg width="24" height="24"><path d="M16 17.3a8 8 0 1 1 1.4-1.4l4.3 4.4a1 1 0 0 1-1.4 1.4l-4.4-4.3zm-5-.3a6 6 0 1 0 0-12 6 6 0 0 0 0 12zm-3-5a1 1 0 0 1 0-2h6a1 1 0 0 1 0 2H8z" fill-rule="nonzero"/></svg>'},Yf.get(t).icons),mr(r,function(e,t){n.ui.registry.addIcon(t,e)}),function(e){var t=e.settings.theme;if(A(t)){e.settings.theme=Tw(t);var n=Jf.get(t);e.theme=new n(e,Jf.urls[t]),e.theme.init&&e.theme.init(e,Jf.urls[t]||e.documentBaseUrl.replace(/\/$/,""),e.$)}else e.theme={}}(e),o=e,i=[],Gt.each(o.settings.plugins.split(/[ ,]/),function(e){kw(o,i,Tw(e))});var a,u,s,l,c,f,d=(c=(a=e).settings,f=a.getElement(),a.orgDisplay=f.style.display,A(c.theme)?a.theme.renderUI():_(c.theme)?(s=(u=a).getElement(),(l=u.settings.theme(u,s)).editorContainer.nodeType&&(l.editorContainer.id=l.editorContainer.id||u.id+"_parent"),l.iframeContainer&&l.iframeContainer.nodeType&&(l.iframeContainer.id=l.iframeContainer.id||u.id+"_iframecontainer"),l.height=l.iframeHeight?l.iframeHeight:s.offsetHeight,l):Rw(a));return e.editorContainer=d.editorContainer?d.editorContainer:null,zw(e),e.inline?ww(e):Ew(e,d)},Mw=vi.DOM,Bw=function(e){return"-"===e.charAt(0)},_w=function(a,u){var s=xi.ScriptLoader;!function(e,t,n,r){var o=t.settings,i=o.theme;if(A(i)){if(!Bw(i)&&!Jf.urls.hasOwnProperty(i)){var a=o.theme_url;a?Jf.load(i,t.documentBaseURI.toAbsolute(a)):Jf.load(i,"themes/"+i+"/theme"+n+".js")}e.loadQueue(function(){Jf.waitFor(i,r)})}else r()}(s,a,u,function(){var e,t,n,r,o,i;e=s,n=yc(t=a),r=Cc(t),!1===ki.hasCode(n)&&"en"!==n&&(""!==r?e.add(r):e.add(t.editorManager.baseURL+"/langs/"+n+".js")),function(e,t){var n=e.icons;if(A(n)){var r=t.editorManager.baseURL+"/icons/"+Gt.trim(n)+"/icons.js";xi.ScriptLoader.add(r)}}(a.settings,a),o=a.settings,i=u,Gt.isArray(o.plugins)&&(o.plugins=o.plugins.join(" ")),Gt.each(o.external_plugins,function(e,t){Gf.load(t,e),o.plugins+=" "+t}),Gt.each(o.plugins.split(/[ ,]/),function(e){if((e=Gt.trim(e))&&!Gf.urls[e])if(Bw(e)){e=e.substr(1,e.length);var t=Gf.dependencies(e);Gt.each(t,function(e){var t={prefix:"plugins/",resource:e,suffix:"/plugin"+i+".js"};e=Gf.createUrl(t,e),Gf.load(e.resource,e)})}else Gf.load(e,{prefix:"plugins/",resource:e,suffix:"/plugin"+i+".js"})}),s.loadQueue(function(){a.removed||Dw(a)},a,function(e){Xf.pluginLoadError(a,e[0]),a.removed||Dw(a)})})},Ow=function(t){var e=t.settings,n=t.id;ki.setCode(yc(t));var r=function(){Mw.unbind(j.window,"ready",r),t.render()};if(ke.Event.domLoaded){if(t.getElement()&&he.contentEditable){e.inline?t.inline=!0:(t.orgVisibility=t.getElement().style.visibility,t.getElement().style.visibility="hidden");var o=t.getElement().form||Mw.getParent(n,"form");o&&(t.formElement=o,e.hidden_input&&!/TEXTAREA|INPUT/i.test(t.getElement().nodeName)&&(Mw.insertAfter(Mw.create("input",{type:"hidden",name:n}),n),t.hasHiddenInput=!0),t.formEventDelegate=function(e){t.fire(e.type,e)},Mw.bind(o,"submit reset",t.formEventDelegate),t.on("reset",function(){t.setContent(t.startContent,{format:"raw"})}),!e.submit_patch||o.submit.nodeType||o.submit.length||o._mceOldSubmit||(o._mceOldSubmit=o.submit,o.submit=function(){return t.editorManager.triggerSave(),t.setDirty(!1),o._mceOldSubmit(o)})),t.windowManager=Ff(t),t.notificationManager=If(t),"xml"===e.encoding&&t.on("GetContent",function(e){e.save&&(e.content=Mw.encode(e.content))}),e.add_form_submit_trigger&&t.on("submit",function(){t.initialized&&t.save()}),e.add_unload_trigger&&(t._beforeUnload=function(){!t.initialized||t.destroyed||t.isHidden()||t.save({format:"raw",no_events:!0,set_dirty:!1})},t.editorManager.on("BeforeUnload",t._beforeUnload)),t.editorManager.add(t),_w(t,t.suffix)}}else Mw.bind(j.window,"ready",r)},Hw=function(e,t,n){try{e.getDoc().execCommand(t,!1,n)}catch(r){}},Pw=function(e,t,n){var r,o;$i(e,t)&&!1===n?(o=t,Ii(r=e)?r.dom().classList.remove(o):Ui(r,o),qi(r)):n&&ji(e,t)},Lw=function(e,t){Pw(or.fromDom(e.getBody()),"mce-content-readonly",t),t?(e.selection.controlSelection.hideResizeRect(),e.readonly=!0,e.getBody().contentEditable="false"):(e.readonly=!1,e.getBody().contentEditable="true",Hw(e,"StyleWithCSS",!1),Hw(e,"enableInlineTableEditing",!1),Hw(e,"enableObjectResizing",!1),e.focus(),e.nodeChanged())},Vw=function(e){return e.readonly?"readonly":"design"},Iw=function(e){return Gt.grep(e.childNodes,function(e){return"LI"===e.nodeName})},Fw=function(e){return e&&e.firstChild&&e.firstChild===e.lastChild&&("\xa0"===(t=e.firstChild).data||Bo.isBr(t));var t},Uw=function(e){return 0<e.length&&(!(t=e[e.length-1]).firstChild||Fw(t))?e.slice(0,-1):e;var t},jw=function(e,t){var n=e.getParent(t,e.isBlock);return n&&"LI"===n.nodeName?n:null},qw=function(e,t){var n=xu.after(e),r=Us(t).prev(n);return r?r.toRange():null},$w=function(t,e,n){var r,o,i,a,u=t.parentNode;return Gt.each(e,function(e){u.insertBefore(e,t)}),r=t,o=n,i=xu.before(r),(a=Us(o).next(i))?a.toRange():null},Ww=function(e,t){var n,r,o,i,a,u,s=t.firstChild,l=t.lastChild;return s&&"meta"===s.name&&(s=s.next),l&&"mce_marker"===l.attr("id")&&(l=l.prev),r=l,u=(n=e).getNonEmptyElements(),r&&(r.isEmpty(u)||(o=r,n.getBlockElements()[o.name]&&(a=o).firstChild&&a.firstChild===a.lastChild&&("br"===(i=o.firstChild).name||"\xa0"===i.value)))&&(l=l.prev),!(!s||s!==l||"ul"!==s.name&&"ol"!==s.name)},Kw=function(e,o,i,t){var n,r,a,u,s,l,c,f,d,h,m,g,p,v,b,y,C,w,x,N=(n=o,r=t,l=e.serialize(r),c=n.createFragment(l),u=(a=c).firstChild,s=a.lastChild,u&&"META"===u.nodeName&&u.parentNode.removeChild(u),s&&"mce_marker"===s.id&&s.parentNode.removeChild(s),a),E=jw(o,i.startContainer),z=Uw(Iw(N.firstChild)),S=o.getRoot(),k=function(e){var t=xu.fromRangeStart(i),n=Us(o.getRoot()),r=1===e?n.prev(t):n.next(t);return!r||jw(o,r.getNode())!==E};return k(1)?$w(E,z,S):k(2)?(f=E,d=z,h=S,o.insertAfter(d.reverse(),f),qw(d[0],h)):(g=z,p=S,v=m=E,y=(b=i).cloneRange(),C=b.cloneRange(),y.setStartBefore(v),C.setEndAfter(v),w=[y.cloneContents(),C.cloneContents()],(x=m.parentNode).insertBefore(w[0],m),Gt.each(g,function(e){x.insertBefore(e,m)}),x.insertBefore(w[1],m),x.removeChild(m),qw(g[g.length-1],p))},Xw=function(e,t){return!!jw(e,t)},Yw=Bo.matchNodeNames("td th"),Gw=function(e,t){var n,r,o=e.selection.getRng(),i=o.startContainer,a=o.startOffset;o.collapsed&&(n=i,r=a,Bo.isText(n)&&"\xa0"===n.nodeValue[r-1])&&Bo.isText(i)&&(i.insertData(a-1," "),i.deleteData(a,1),o.setStart(i,a),o.setEnd(i,a),e.selection.setRng(o)),e.selection.setContent(t)},Jw=function(e,t,n){var r,o,i,a,u,s,l,c,f,d,h,m=e.selection,g=e.dom;if(/^ | $/.test(t)&&(t=function(e,t){var n,r;n=e.startContainer,r=e.startOffset;var o=function(e){return n[e]&&3===n[e].nodeType};return 3===n.nodeType&&(0<r?t=t.replace(/^&nbsp;/," "):o("previousSibling")||(t=t.replace(/^ /,"&nbsp;")),r<n.length?t=t.replace(/&nbsp;(<br>|)$/," "):o("nextSibling")||(t=t.replace(/(&nbsp;| )(<br>|)$/,"&nbsp;"))),t}(m.getRng(),t)),r=e.parser,h=n.merge,o=kc({validate:e.settings.validate},e.schema),d='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;&#x200B;</span>',s={content:t,format:"html",selection:!0,paste:n.paste},(s=e.fire("BeforeSetContent",s)).isDefaultPrevented())e.fire("SetContent",{content:s.content,format:"html",selection:!0,paste:n.paste});else{-1===(t=s.content).indexOf("{$caret}")&&(t+="{$caret}"),t=t.replace(/\{\$caret\}/,d);var p,v,b,y,C,w,x=(c=m.getRng()).startContainer||(c.parentElement?c.parentElement():null),N=e.getBody();x===N&&m.isCollapsed()&&g.isBlock(N.firstChild)&&(p=e,(v=N.firstChild)&&!p.schema.getShortEndedElements()[v.nodeName])&&g.isEmpty(N.firstChild)&&((c=g.createRng()).setStart(N.firstChild,0),c.setEnd(N.firstChild,0),m.setRng(c)),m.isCollapsed()||(e.selection.setRng(gg(e.selection.getRng())),e.getDoc().execCommand("Delete",!1,null),b=e.selection.getRng(),y=t,C=b.startContainer,w=b.startOffset,3===C.nodeType&&b.collapsed&&("\xa0"===C.data[w]?(C.deleteData(w,1),/[\u00a0| ]$/.test(y)||(y+=" ")):"\xa0"===C.data[w-1]&&(C.deleteData(w-1,1),/[\u00a0| ]$/.test(y)||(y=" "+y))),t=y);var E,z,S,k={context:(i=m.getNode()).nodeName.toLowerCase(),data:n.data,insert:!0};if(u=r.parse(t,k),!0===n.paste&&Ww(e.schema,u)&&Xw(g,i))return c=Kw(o,g,e.selection.getRng(),u),e.selection.setRng(c),void e.fire("SetContent",s);if(function(e){for(var t=e;t=t.walk();)1===t.type&&t.attr("data-mce-fragment","1")}(u),"mce_marker"===(f=u.lastChild).attr("id"))for(f=(l=f).prev;f;f=f.walk(!0))if(3===f.type||!g.isBlock(f.name)){e.schema.isValidChild(f.parent.name,"span")&&f.parent.insert(l,f,"br"===f.name);break}if(e._selectionOverrides.showBlockCaretContainer(i),k.invalid){for(Gw(e,d),i=m.getNode(),a=e.getBody(),9===i.nodeType?i=f=a:f=i;f!==a;)f=(i=f).parentNode;t=i===a?a.innerHTML:g.getOuterHTML(i),t=o.serialize(r.parse(t.replace(/<span (id="mce_marker"|id=mce_marker).+?<\/span>/i,function(){return o.serialize(u)}))),i===a?g.setHTML(a,t):g.setOuterHTML(i,t)}else!function(e,t,n){if("all"===n.getAttribute("data-mce-bogus"))n.parentNode.insertBefore(e.dom.createFragment(t),n);else{var r=n.firstChild,o=n.lastChild;!r||r===o&&"BR"===r.nodeName?e.dom.setHTML(n,t):Gw(e,t)}}(e,t=o.serialize(u),i);!function(e,t){var n=e.schema.getTextInlineElements(),r=e.dom;if(t){var o=e.getBody(),i=new Fm(r);Gt.each(r.select("*[data-mce-fragment]"),function(e){for(var t=e.parentNode;t&&t!==o;t=t.parentNode)n[e.nodeName.toLowerCase()]&&i.compare(t,e)&&r.remove(e,!0)})}}(e,h),function(n,e){var t,r,o,i,a,u=n.dom,s=n.selection;if(e){if(n.selection.scrollIntoView(e),t=function(e){for(var t=n.getBody();e&&e!==t;e=e.parentNode)if("false"===n.dom.getContentEditable(e))return e;return null}(e))return u.remove(e),s.select(t);var l=u.createRng();(i=e.previousSibling)&&3===i.nodeType?(l.setStart(i,i.nodeValue.length),he.ie||(a=e.nextSibling)&&3===a.nodeType&&(i.appendData(a.data),a.parentNode.removeChild(a))):(l.setStartBefore(e),l.setEndBefore(e)),r=u.getParent(e,u.isBlock),u.remove(e),r&&u.isEmpty(r)&&(n.$(r).empty(),l.setStart(r,0),l.setEnd(r,0),Yw(r)||r.getAttribute("data-mce-fragment")||!(o=function(e){var t=xu.fromRangeStart(e);if(t=Us(n.getBody()).next(t))return t.toRange()}(l))?u.add(r,u.create("br",{"data-mce-bogus":"1"})):(l=o,u.remove(r))),s.setRng(l)}}(e,g.get("mce_marker")),E=e.getBody(),Gt.each(E.getElementsByTagName("*"),function(e){e.removeAttribute("data-mce-fragment")}),z=e.dom,S=e.selection.getStart(),T.from(z.getParent(S,"td,th")).map(or.fromDom).each(Yh),e.fire("SetContent",s),e.addVisual()}},Qw=function(e,t){var n,r,o="string"!=typeof(n=t)?(r=Gt.extend({paste:n.paste,data:{paste:n.paste}},n),{content:n.content,details:r}):{content:n,details:{}};Jw(e,o.content,o.details)},Zw=function(e,t){e.getDoc().execCommand(t,!1,null)},ex=function(e){dy(e,!1)||gy(e,!1)||yy(e,!1)||Yb(e,!1)||Ky(e)||Zb(e,!1)||Ny(e,!1)||(Zw(e,"Delete"),_b(e))},tx=function(e){dy(e,!0)||gy(e,!0)||yy(e,!0)||Yb(e,!0)||Ky(e)||Zb(e,!0)||Ny(e,!0)||Zw(e,"ForwardDelete")},nx=function(s){return function(u,e){return T.from(e).map(or.fromDom).filter(cr).bind(function(e){return(r=s,o=u,i=e.dom(),a=function(e){return zr(e,r)},Ji(or.fromDom(i),function(e){return a(e).isSome()},function(e){return Or(or.fromDom(o),e)}).bind(a)).or((t=s,n=e.dom(),T.from(vi.DOM.getStyle(n,t,!0))));var t,n,r,o,i,a}).getOr("")}},rx={getFontSize:nx("font-size"),getFontFamily:q(function(e){return e.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")},nx("font-family")),toPt:function(e,t){return/[0-9.]+px$/.test(e)?(n=72*parseInt(e,10)/96,r=t||0,o=Math.pow(10,r),Math.round(n*o)/o+"pt"):e;var n,r,o}},ox=function(e){return Zs.firstPositionIn(e.getBody()).map(function(e){var t=e.container();return Bo.isText(t)?t.parentNode:t})},ix=function(o){return T.from(o.selection.getRng()).bind(function(e){var t,n,r=o.getBody();return n=r,(t=e).startContainer===n&&0===t.startOffset?T.none():T.from(o.selection.getStart(!0))})},ax=function(e,t){if(/^[0-9\.]+$/.test(t)){var n=parseInt(t,10);if(1<=n&&n<=7){var r=sc(e),o=lc(e);return o?o[n-1]||t:r[n-1]||t}return t}return t},ux=function(e){var t=parseInt(e,10);return isNaN(t)?0:t},sx=function(e,t){return(e||"table"===sr(t)?"margin":"padding")+("rtl"===Nr(t,"direction")?"-right":"-left")},lx=function(e){var r,t=fx(e);return!0!==e.readonly&&(1<t.length||(r=e,Q(t,function(e){var t=sx(wc(r),e),n=zr(e,t).map(ux).getOr(0);return"false"!==r.dom.getContentEditable(e.dom())&&0<n})))},cx=function(e){return po(e)||vo(e)},fx=function(e){return V(K(e.selection.getSelectedBlocks(),or.fromDom),function(e){return!cx(e)&&!Lr(e).map(cx).getOr(!1)&&Ji(e,function(e){return Bo.isContentEditableTrue(e.dom())||Bo.isContentEditableFalse(e.dom())}).exists(function(e){return Bo.isContentEditableTrue(e.dom())})})},dx=function(e,t){var n=e.dom,r=e.selection,o=e.formatter,i=xc(e),a=/[a-z%]+$/i.exec(i)[0],u=parseInt(i,10),s=wc(e),l=nc(e);e.queryCommandState("InsertUnorderedList")||e.queryCommandState("InsertOrderedList")||""!==l||n.getParent(r.getNode(),n.isBlock)||o.apply("div"),L(fx(e),function(e){!function(e,t,n,r,o,i){var a=sx(n,or.fromDom(i));if("outdent"===t){var u=Math.max(0,ux(i.style[a])-r);e.setStyle(i,a,u?u+o:"")}else u=ux(i.style[a])+r+o,e.setStyle(i,a,u)}(n,t,s,u,a,e.dom())})},hx=Gt.each,mx=Gt.extend,gx=Gt.map,px=Gt.inArray;function vx(s){var o,i,a,t,l={state:{},exec:{},value:{}};s.on("PreInit",function(){o=s.dom,i=s.selection,a=s.formatter});var e=function(e,n){n=n||"exec",hx(e,function(t,e){hx(e.toLowerCase().split(","),function(e){l[n][e]=t})})},n=function(e,t,n){e=e.toLowerCase(),l.value[e]=function(){return t.call(n||s)}};mx(this,{execCommand:function(t,n,r,e){var o,i,a=!1;if(!s.removed){if(/^(mceAddUndoLevel|mceEndUndoLevel|mceBeginUndoLevel|mceRepaint)$/.test(t)||e&&e.skip_focus?Xc(s):s.focus(),(e=s.fire("BeforeExecCommand",{command:t,ui:n,value:r})).isDefaultPrevented())return!1;if(i=t.toLowerCase(),o=l.exec[i])return o(i,n,r),s.fire("ExecCommand",{command:t,ui:n,value:r}),!0;if(hx(s.plugins,function(e){if(e.execCommand&&e.execCommand(t,n,r))return s.fire("ExecCommand",{command:t,ui:n,value:r}),!(a=!0)}),a)return a;if(s.theme&&s.theme.execCommand&&s.theme.execCommand(t,n,r))return s.fire("ExecCommand",{command:t,ui:n,value:r}),!0;try{a=s.getDoc().execCommand(t,n,r)}catch(u){}return!!a&&(s.fire("ExecCommand",{command:t,ui:n,value:r}),!0)}},queryCommandState:function(e){var t;if(!s.quirks.isHidden()&&!s.removed){if(e=e.toLowerCase(),t=l.state[e])return t(e);try{return s.getDoc().queryCommandState(e)}catch(n){}return!1}},queryCommandValue:function(e){var t;if(!s.quirks.isHidden()&&!s.removed){if(e=e.toLowerCase(),t=l.value[e])return t(e);try{return s.getDoc().queryCommandValue(e)}catch(n){}}},queryCommandSupported:function(e){if(e=e.toLowerCase(),l.exec[e])return!0;try{return s.getDoc().queryCommandSupported(e)}catch(t){}return!1},addCommands:e,addCommand:function(e,o,i){e=e.toLowerCase(),l.exec[e]=function(e,t,n,r){return o.call(i||s,t,n,r)}},addQueryStateHandler:function(e,t,n){e=e.toLowerCase(),l.state[e]=function(){return t.call(n||s)}},addQueryValueHandler:n,hasCustomCommand:function(e){return e=e.toLowerCase(),!!l.exec[e]}});var u=function(e,t,n){return t===undefined&&(t=!1),n===undefined&&(n=null),s.getDoc().execCommand(e,t,n)},r=function(e){return a.match(e)},c=function(e,t){a.toggle(e,t?{value:t}:undefined),s.nodeChanged()},f=function(e){t=i.getBookmark(e)},d=function(){i.moveToBookmark(t)};e({"mceResetDesignMode,mceBeginUndoLevel":function(){},"mceEndUndoLevel,mceAddUndoLevel":function(){s.undoManager.add()},"Cut,Copy,Paste":function(e){var t,n=s.getDoc();try{u(e)}catch(o){t=!0}if("paste"!==e||n.queryCommandEnabled(e)||(t=!0),t||!n.queryCommandSupported(e)){var r=s.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");he.mac&&(r=r.replace(/Ctrl\+/g,"\u2318+")),s.notificationManager.open({text:r,type:"error"})}},unlink:function(){if(i.isCollapsed()){var e=s.dom.getParent(s.selection.getStart(),"a");e&&s.dom.remove(e,!0)}else a.remove("link")},"JustifyLeft,JustifyCenter,JustifyRight,JustifyFull,JustifyNone":function(e){var t=e.substring(7);"full"===t&&(t="justify"),hx("left,center,right,justify".split(","),function(e){t!==e&&a.remove("align"+e)}),"none"!==t&&c("align"+t)},"InsertUnorderedList,InsertOrderedList":function(e){var t,n;u(e),(t=o.getParent(i.getNode(),"ol,ul"))&&(n=t.parentNode,/^(H[1-6]|P|ADDRESS|PRE)$/.test(n.nodeName)&&(f(),o.split(n,t),d()))},"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){c(e)},"ForeColor,HiliteColor":function(e,t,n){c(e,n)},FontName:function(e,t,n){var r,o;o=n,(r=s).formatter.toggle("fontname",{value:ax(r,o)}),r.nodeChanged()},FontSize:function(e,t,n){var r,o;o=n,(r=s).formatter.toggle("fontsize",{value:ax(r,o)}),r.nodeChanged()},RemoveFormat:function(e){a.remove(e)},mceBlockQuote:function(){c("blockquote")},FormatBlock:function(e,t,n){return c(n||"p")},mceCleanup:function(){var e=i.getBookmark();s.setContent(s.getContent()),i.moveToBookmark(e)},mceRemoveNode:function(e,t,n){var r=n||i.getNode();r!==s.getBody()&&(f(),s.dom.remove(r,!0),d())},mceSelectNodeDepth:function(e,t,n){var r=0;o.getParent(i.getNode(),function(e){if(1===e.nodeType&&r++===n)return i.select(e),!1},s.getBody())},mceSelectNode:function(e,t,n){i.select(n)},mceInsertContent:function(e,t,n){Qw(s,n)},mceInsertRawHTML:function(e,t,n){i.setContent("tiny_mce_marker");var r=s.getContent();s.setContent(r.replace(/tiny_mce_marker/g,function(){return n}))},mceInsertNewLine:function(e,t,n){LC(s,n)},mceToggleFormat:function(e,t,n){c(n)},mceSetContent:function(e,t,n){s.setContent(n)},"Indent,Outdent":function(e){dx(s,e)},mceRepaint:function(){},InsertHorizontalRule:function(){s.execCommand("mceInsertContent",!1,"<hr />")},mceToggleVisualAid:function(){s.hasVisual=!s.hasVisual,s.addVisual()},mceReplaceContent:function(e,t,n){s.execCommand("mceInsertContent",!1,n.replace(/\{\$selection\}/g,i.getContent({format:"text"})))},mceInsertLink:function(e,t,n){var r;"string"==typeof n&&(n={href:n}),r=o.getParent(i.getNode(),"a"),n.href=n.href.replace(" ","%20"),r&&n.href||a.remove("link"),n.href&&a.apply("link",n,r)},selectAll:function(){var e=o.getParent(i.getStart(),Bo.isContentEditableTrue);if(e){var t=o.createRng();t.selectNodeContents(e),i.setRng(t)}},"delete":function(){ex(s)},forwardDelete:function(){tx(s)},mceNewDocument:function(){s.setContent("")},InsertLineBreak:function(e,t,n){return xC(s,n),!0}});var h=function(n){return function(){var e=i.isCollapsed()?[o.getParent(i.getNode(),o.isBlock)]:i.getSelectedBlocks(),t=gx(e,function(e){return!!a.matchNode(e,n)});return-1!==px(t,!0)}};e({JustifyLeft:h("alignleft"),JustifyCenter:h("aligncenter"),JustifyRight:h("alignright"),JustifyFull:h("alignjustify"),"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){return r(e)},mceBlockQuote:function(){return r("blockquote")},Outdent:function(){return lx(s)},"InsertUnorderedList,InsertOrderedList":function(e){var t=o.getParent(i.getNode(),"ul,ol");return t&&("insertunorderedlist"===e&&"UL"===t.tagName||"insertorderedlist"===e&&"OL"===t.tagName)}},"state"),e({Undo:function(){s.undoManager.undo()},Redo:function(){s.undoManager.redo()}}),n("FontName",function(){return ix(t=s).fold(function(){return ox(t).map(function(e){return rx.getFontFamily(t.getBody(),e)}).getOr("")},function(e){return rx.getFontFamily(t.getBody(),e)});var t},this),n("FontSize",function(){return ix(t=s).fold(function(){return ox(t).map(function(e){return rx.getFontSize(t.getBody(),e)}).getOr("")},function(e){return rx.getFontSize(t.getBody(),e)});var t},this)}var bx=Gt.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend"," "),yx=function(a){var u,s,l=this,c={},f=function(){return!1},d=function(){return!0};u=(a=a||{}).scope||l,s=a.toggleEvent||f;var r=function(e,t,n,r){var o,i,a;if(!1===t&&(t=f),t)for(t={func:t},r&&Gt.extend(t,r),a=(i=e.toLowerCase().split(" ")).length;a--;)e=i[a],(o=c[e])||(o=c[e]=[],s(e,!0)),n?o.unshift(t):o.push(t);return l},h=function(e,t){var n,r,o,i,a;if(e)for(n=(i=e.toLowerCase().split(" ")).length;n--;){if(e=i[n],r=c[e],!e){for(o in c)s(o,!1),delete c[o];return l}if(r){if(t)for(a=r.length;a--;)r[a].func===t&&(r=r.slice(0,a).concat(r.slice(a+1)),c[e]=r);else r.length=0;r.length||(s(e,!1),delete c[e])}}else{for(e in c)s(e,!1);c={}}return l};l.fire=function(e,t){var n,r,o,i;if(e=e.toLowerCase(),(t=t||{}).type=e,t.target||(t.target=u),t.preventDefault||(t.preventDefault=function(){t.isDefaultPrevented=d},t.stopPropagation=function(){t.isPropagationStopped=d},t.stopImmediatePropagation=function(){t.isImmediatePropagationStopped=d},t.isDefaultPrevented=f,t.isPropagationStopped=f,t.isImmediatePropagationStopped=f),a.beforeFire&&a.beforeFire(t),n=c[e])for(r=0,o=n.length;r<o;r++){if((i=n[r]).once&&h(e,i.func),t.isImmediatePropagationStopped())return t.stopPropagation(),t;if(!1===i.func.call(u,t))return t.preventDefault(),t}return t},l.on=r,l.off=h,l.once=function(e,t,n){return r(e,t,n,{once:!0})},l.has=function(e){return e=e.toLowerCase(),!(!c[e]||0===c[e].length)}};yx.isNative=function(e){return!!bx[e.toLowerCase()]};var Cx,wx=function(n){return n._eventDispatcher||(n._eventDispatcher=new yx({scope:n,toggleEvent:function(e,t){yx.isNative(e)&&n.toggleNativeEvent&&n.toggleNativeEvent(e,t)}})),n._eventDispatcher},xx={fire:function(e,t,n){if(this.removed&&"remove"!==e&&"detach"!==e)return t;if(t=wx(this).fire(e,t,n),!1!==n&&this.parent)for(var r=this.parent();r&&!t.isPropagationStopped();)r.fire(e,t,!1),r=r.parent();return t},on:function(e,t,n){return wx(this).on(e,t,n)},off:function(e,t){return wx(this).off(e,t)},once:function(e,t){return wx(this).once(e,t)},hasEventListeners:function(e){return wx(this).has(e)}},Nx=vi.DOM,Ex=function(e,t){return"selectionchange"===t?e.getDoc():!e.inline&&/^mouse|touch|click|contextmenu|drop|dragover|dragend/.test(t)?e.getDoc().documentElement:e.settings.event_root?(e.eventRoot||(e.eventRoot=Nx.select(e.settings.event_root)[0]),e.eventRoot):e.getBody()},zx=function(e,t,n){var r;(r=e).hidden||r.readonly?!0===e.readonly&&n.preventDefault():e.fire(t,n)},Sx=function(i,a){var e,t;if(i.delegates||(i.delegates={}),!i.delegates[a]&&!i.removed)if(e=Ex(i,a),i.settings.event_root){if(Cx||(Cx={},i.editorManager.on("removeEditor",function(){var e;if(!i.editorManager.activeEditor&&Cx){for(e in Cx)i.dom.unbind(Ex(i,e));Cx=null}})),Cx[a])return;t=function(e){for(var t=e.target,n=i.editorManager.get(),r=n.length;r--;){var o=n[r].getBody();(o===t||Nx.isChildOf(t,o))&&zx(n[r],a,e)}},Cx[a]=t,Nx.bind(e,a,t)}else t=function(e){zx(i,a,e)},Nx.bind(e,a,t),i.delegates[a]=t},kx={bindPendingEventDelegates:function(){var t=this;Gt.each(t._pendingNativeEvents,function(e){Sx(t,e)})},toggleNativeEvent:function(e,t){var n=this;"focus"!==e&&"blur"!==e&&(t?n.initialized?Sx(n,e):n._pendingNativeEvents?n._pendingNativeEvents.push(e):n._pendingNativeEvents=[e]:n.initialized&&(n.dom.unbind(Ex(n,e),e,n.delegates[e]),delete n.delegates[e]))},unbindAllNativeEvents:function(){var e,t=this,n=t.getBody(),r=t.dom;if(t.delegates){for(e in t.delegates)t.dom.unbind(Ex(t,e),e,t.delegates[e]);delete t.delegates}!t.inline&&n&&r&&(n.onload=null,r.unbind(t.getWin()),r.unbind(t.getDoc())),r&&(r.unbind(n),r.unbind(t.getContainer()))}},Tx=kx=Gt.extend({},xx,kx),Ax=Gt.each,Rx=Gt.explode,Dx={f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123},Mx=Gt.makeMap("alt,ctrl,shift,meta,access");function Bx(i){var a={},r=[],u=function(e){var t,n,r={};for(n in Ax(Rx(e,"+"),function(e){e in Mx?r[e]=!0:/^[0-9]{2,}$/.test(e)?r.keyCode=parseInt(e,10):(r.charCode=e.charCodeAt(0),r.keyCode=Dx[e]||e.toUpperCase().charCodeAt(0))}),t=[r.keyCode],Mx)r[n]?t.push(n):r[n]=!1;return r.id=t.join(","),r.access&&(r.alt=!0,he.mac?r.ctrl=!0:r.shift=!0),r.meta&&(he.mac?r.meta=!0:(r.ctrl=!0,r.meta=!1)),r},s=function(e,t,n,r){var o;return(o=Gt.map(Rx(e,">"),u))[o.length-1]=Gt.extend(o[o.length-1],{func:n,scope:r||i}),Gt.extend(o[0],{desc:i.translate(t),subpatterns:o.slice(1)})},o=function(e,t){return!!t&&t.ctrl===e.ctrlKey&&t.meta===e.metaKey&&t.alt===e.altKey&&t.shift===e.shiftKey&&!!(e.keyCode===t.keyCode||e.charCode&&e.charCode===t.charCode)&&(e.preventDefault(),!0)},l=function(e){return e.func?e.func.call(e.scope):null};i.on("keyup keypress keydown",function(t){var e,n;((n=t).altKey||n.ctrlKey||n.metaKey||"keydown"===(e=t).type&&112<=e.keyCode&&e.keyCode<=123)&&!t.isDefaultPrevented()&&(Ax(a,function(e){if(o(t,e))return r=e.subpatterns.slice(0),"keydown"===t.type&&l(e),!0}),o(t,r[0])&&(1===r.length&&"keydown"===t.type&&l(r[0]),r.shift()))}),this.add=function(e,n,r,o){var t;return"string"==typeof(t=r)?r=function(){i.execCommand(t,!1,null)}:Gt.isArray(t)&&(r=function(){i.execCommand(t[0],t[1],t[2])}),Ax(Rx(Gt.trim(e.toLowerCase())),function(e){var t=s(e,n,r,o);a[t.id]=t}),!0},this.remove=function(e){var t=s(e);return!!a[t.id]&&(delete a[t.id],!0)}}var _x=Gt.each,Ox=Gt.trim,Hx="source protocol authority userInfo user password host port relative path directory file query anchor".split(" "),Px={ftp:21,http:80,https:443,mailto:25},Lx=function(r,e){var t,n,o=this;if(r=Ox(r),t=(e=o.settings=e||{}).base_uri,/^([\w\-]+):([^\/]{2})/i.test(r)||/^\s*#/.test(r))o.source=r;else{var i=0===r.indexOf("//");0!==r.indexOf("/")||i||(r=(t&&t.protocol||"http")+"://mce_host"+r),/^[\w\-]*:?\/\//.test(r)||(n=e.base_uri?e.base_uri.path:new Lx(j.document.location.href).directory,r=""==e.base_uri.protocol?"//mce_host"+o.toAbsPath(n,r):(r=/([^#?]*)([#?]?.*)/.exec(r),(t&&t.protocol||"http")+"://mce_host"+o.toAbsPath(n,r[1])+r[2])),r=r.replace(/@@/g,"(mce_at)"),r=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(r),_x(Hx,function(e,t){var n=r[t];n&&(n=n.replace(/\(mce_at\)/g,"@@")),o[e]=n}),t&&(o.protocol||(o.protocol=t.protocol),o.userInfo||(o.userInfo=t.userInfo),o.port||"mce_host"!==o.host||(o.port=t.port),o.host&&"mce_host"!==o.host||(o.host=t.host),o.source=""),i&&(o.protocol="")}};Lx.prototype={setPath:function(e){e=/^(.*?)\/?(\w+)?$/.exec(e),this.path=e[0],this.directory=e[1],this.file=e[2],this.source="",this.getURI()},toRelative:function(e){var t;if("./"===e)return e;if("mce_host"!==(e=new Lx(e,{base_uri:this})).host&&this.host!==e.host&&e.host||this.port!==e.port||this.protocol!==e.protocol&&""!==e.protocol)return e.getURI();var n=this.getURI(),r=e.getURI();return n===r||"/"===n.charAt(n.length-1)&&n.substr(0,n.length-1)===r?n:(t=this.toRelPath(this.path,e.path),e.query&&(t+="?"+e.query),e.anchor&&(t+="#"+e.anchor),t)},toAbsolute:function(e,t){return(e=new Lx(e,{base_uri:this})).getURI(t&&this.isSameOrigin(e))},isSameOrigin:function(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;var t=Px[this.protocol];if(t&&(this.port||t)==(e.port||t))return!0}return!1},toRelPath:function(e,t){var n,r,o,i=0,a="";if(e=(e=e.substring(0,e.lastIndexOf("/"))).split("/"),n=t.split("/"),e.length>=n.length)for(r=0,o=e.length;r<o;r++)if(r>=n.length||e[r]!==n[r]){i=r+1;break}if(e.length<n.length)for(r=0,o=n.length;r<o;r++)if(r>=e.length||e[r]!==n[r]){i=r+1;break}if(1===i)return t;for(r=0,o=e.length-(i-1);r<o;r++)a+="../";for(r=i-1,o=n.length;r<o;r++)a+=r!==i-1?"/"+n[r]:n[r];return a},toAbsPath:function(e,t){var n,r,o,i=0,a=[];for(r=/\/$/.test(t)?"/":"",e=e.split("/"),t=t.split("/"),_x(e,function(e){e&&a.push(e)}),e=a,n=t.length-1,a=[];0<=n;n--)0!==t[n].length&&"."!==t[n]&&(".."!==t[n]?0<i?i--:a.push(t[n]):i++);return 0!==(o=(n=e.length-i)<=0?a.reverse().join("/"):e.slice(0,n).join("/")+"/"+a.reverse().join("/")).indexOf("/")&&(o="/"+o),r&&o.lastIndexOf("/")!==o.length-1&&(o+=r),o},getURI:function(e){var t,n=this;return n.source&&!e||(t="",e||(n.protocol?t+=n.protocol+"://":t+="//",n.userInfo&&(t+=n.userInfo+"@"),n.host&&(t+=n.host),n.port&&(t+=":"+n.port)),n.path&&(t+=n.path),n.query&&(t+="?"+n.query),n.anchor&&(t+="#"+n.anchor),n.source=t),n.source}},Lx.parseDataUri=function(e){var t,n;return e=decodeURIComponent(e).split(","),(n=/data:([^;]+)/.exec(e[0]))&&(t=n[1]),{type:t,data:e[1]}},Lx.getDocumentBaseUrl=function(e){var t;return t=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?e.href:e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),t};var Vx=vi.DOM,Ix=Gt.extend,Fx=Gt.each,Ux=Gt.resolve,jx=he.ie,qx=function(e,t,n){var r,o,i,a,u,s,l,c=this,f=c.documentBaseUrl=n.documentBaseURL,d=n.baseURI;r=c,o=e,i=f,a=n.defaultSettings,u=t,l={id:o,theme:"silver",popup_css:"",plugins:"",document_base_url:i,add_form_submit_trigger:!0,submit_patch:!0,add_unload_trigger:!0,convert_urls:!0,relative_urls:!0,remove_script_host:!0,object_resizing:!0,doctype:"<!DOCTYPE html>",visual:!0,font_size_style_values:"xx-small,x-small,small,medium,large,x-large,xx-large",font_size_legacy_values:"xx-small,small,medium,large,x-large,xx-large,300%",forced_root_block:"p",hidden_input:!0,render_ui:!0,inline_styles:!0,convert_fonts_to_spans:!0,indent:"simple",indent_before:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",indent_after:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",entity_encoding:"named",url_converter:(s=r).convertURL,url_converter_scope:s,ie7_compat:!0},t=Tf(wf,l,a,u),c.settings=t,Ai.languageLoad=t.language_load,Ai.baseURL=n.baseURL,c.id=e,c.setDirty(!1),c.plugins={},c.documentBaseURI=new Lx(t.document_base_url,{base_uri:d}),c.baseURI=d,c.contentCSS=[],c.contentStyles=[],c.shortcuts=new Bx(c),c.loadedCSS={},c.editorCommands=new vx(c),c.suffix=n.suffix,c.editorManager=n,c.inline=t.inline,c.buttons={},c.menuItems={},t.cache_suffix&&(he.cacheSuffix=t.cache_suffix.replace(/^[\?\&]+/,"")),!1===t.override_viewport&&(he.overrideViewPort=!1);var h,m,g,p,v,b,y,C,w=(m={},g={},p={},v={},b={},y={},{addButton:(C=function(n,r){return function(e,t){return n[e.toLowerCase()]=Pl({type:r},t)}})(h={},"button"),addToggleButton:C(h,"togglebutton"),addMenuButton:C(h,"menubutton"),addSplitButton:C(h,"splitbutton"),addMenuItem:C(m,"menuitem"),addNestedMenuItem:C(m,"nestedmenuitem"),addToggleMenuItem:C(m,"togglemenuitem"),addAutocompleter:C(g,"autocompleter"),addContextMenu:C(v,"contextmenu"),addContextToolbar:C(b,"contexttoolbar"),addContextForm:C(b,"contextform"),addSidebar:C(y,"sidebar"),addIcon:function(e,t){return p[e.toLowerCase()]=t},getAll:function(){return{buttons:h,menuItems:m,icons:p,popups:g,contextMenus:v,contextToolbars:b,sidebars:y}}});c.ui={registry:w},n.fire("SetupEditor",{editor:c}),c.execCallback("setup",c),c.$=pn.overrideDefaults(function(){return{context:c.inline?c.getBody():c.getDoc(),element:c.getBody()}})};Ix(qx.prototype={render:function(){Ow(this)},focus:function(e){nf(this,e)},hasFocus:function(){return rf(this)},execCallback:function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r,o=this.settings[e];if(o)return this.callbackLookup&&(r=this.callbackLookup[e])&&(o=r.func,r=r.scope),"string"==typeof o&&(r=(r=o.replace(/\.\w+$/,""))?Ux(r):0,o=Ux(o),this.callbackLookup=this.callbackLookup||{},this.callbackLookup[e]={func:o,scope:r}),o.apply(r||this,Array.prototype.slice.call(arguments,1))},translate:function(e){return ki.translate(e)},getParam:function(e,t,n){return Df(this,e,t,n)},nodeChanged:function(e){this._nodeChangeDispatcher.nodeChanged(e)},addButton:function(){throw new Error("editor.addButton has been removed in tinymce 5x, use editor.ui.registry.addButton or editor.ui.registry.addToggleButton or editor.ui.registry.addSplitButton instead")},addSidebar:function(){throw new Error("editor.addSidebar has been removed in tinymce 5x, use editor.ui.registry.addSidebar instead")},addMenuItem:function(){throw new Error("editor.addMenuItem has been removed in tinymce 5x, use editor.ui.registry.addMenuItem instead")},addContextToolbar:function(){throw new Error("editor.addContextToolbar has been removed in tinymce 5x, use editor.ui.registry.addContextToolbar instead")},addCommand:function(e,t,n){this.editorCommands.addCommand(e,t,n)},addQueryStateHandler:function(e,t,n){this.editorCommands.addQueryStateHandler(e,t,n)},addQueryValueHandler:function(e,t,n){this.editorCommands.addQueryValueHandler(e,t,n)},addShortcut:function(e,t,n,r){this.shortcuts.add(e,t,n,r)},execCommand:function(e,t,n,r){return this.editorCommands.execCommand(e,t,n,r)},queryCommandState:function(e){return this.editorCommands.queryCommandState(e)},queryCommandValue:function(e){return this.editorCommands.queryCommandValue(e)},queryCommandSupported:function(e){return this.editorCommands.queryCommandSupported(e)},show:function(){this.hidden&&(this.hidden=!1,this.inline?this.getBody().contentEditable=!0:(Vx.show(this.getContainer()),Vx.hide(this.id)),this.load(),this.fire("show"))},hide:function(){var e=this,t=e.getDoc();e.hidden||(jx&&t&&!e.inline&&t.execCommand("SelectAll"),e.save(),e.inline?(e.getBody().contentEditable=!1,e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(Vx.hide(e.getContainer()),Vx.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.fire("hide"))},isHidden:function(){return!!this.hidden},setProgressState:function(e,t){this.fire("ProgressState",{state:e,time:t})},load:function(e){var t,n=this.getElement();return this.removed?"":n?((e=e||{}).load=!0,t=this.setContent(n.value!==undefined?n.value:n.innerHTML,e),e.element=n,e.no_events||this.fire("LoadContent",e),e.element=n=null,t):void 0},save:function(e){var t,n,r=this,o=r.getElement();if(o&&r.initialized&&!r.removed)return(e=e||{}).save=!0,e.element=o,e.content=r.getContent(e),e.no_events||r.fire("SaveContent",e),"raw"===e.format&&r.fire("RawSaveContent",e),t=e.content,/TEXTAREA|INPUT/i.test(o.nodeName)?o.value=t:(!e.is_removing&&r.inline||(o.innerHTML=t),(n=Vx.getParent(r.id,"form"))&&Fx(n.elements,function(e){if(e.name===r.id)return e.value=t,!1})),e.element=o=null,!1!==e.set_dirty&&r.setDirty(!1),t},setContent:function(e,t){return uf(this,e,t)},getContent:function(e){return t=this,void 0===(n=e)&&(n={}),T.from(t.getBody()).fold($("tree"===n.format?new Fl("body",11):""),function(e){return Ec(t,n,e)});var t,n},insertContent:function(e,t){t&&(e=Ix({content:e},t)),this.execCommand("mceInsertContent",!1,e)},isDirty:function(){return!this.isNotDirty},setDirty:function(e){var t=!this.isNotDirty;this.isNotDirty=!e,e&&e!==t&&this.fire("dirty")},setMode:function(e){var t,n;(n=e)!==Vw(t=this)&&(t.initialized?Lw(t,"readonly"===n):t.on("init",function(){Lw(t,"readonly"===n)}),df(t,n))},getContainer:function(){return this.container||(this.container=Vx.get(this.editorContainer||this.id+"_parent")),this.container},getContentAreaContainer:function(){return this.contentAreaContainer},getElement:function(){return this.targetElm||(this.targetElm=Vx.get(this.id)),this.targetElm},getWin:function(){var e;return this.contentWindow||(e=this.iframeElement)&&(this.contentWindow=e.contentWindow),this.contentWindow},getDoc:function(){var e;return this.contentDocument||(e=this.getWin())&&(this.contentDocument=e.document),this.contentDocument},getBody:function(){var e=this.getDoc();return this.bodyElement||(e?e.body:null)},convertURL:function(e,t,n){var r=this.settings;return r.urlconverter_callback?this.execCallback("urlconverter_callback",e,n,!0,t):!r.convert_urls||n&&"LINK"===n.nodeName||0===e.indexOf("file:")||0===e.length?e:r.relative_urls?this.documentBaseURI.toRelative(e):e=this.documentBaseURI.toAbsolute(e,r.remove_script_host)},addVisual:function(e){var n,r=this,o=r.settings,i=r.dom;e=e||r.getBody(),r.hasVisual===undefined&&(r.hasVisual=o.visual),Fx(i.select("table,a",e),function(e){var t;switch(e.nodeName){case"TABLE":return n=o.visual_table_class||"mce-item-table",void((t=i.getAttrib(e,"border"))&&"0"!==t||!r.hasVisual?i.removeClass(e,n):i.addClass(e,n));case"A":return void(i.getAttrib(e,"href")||(t=i.getAttrib(e,"name")||e.id,n=o.visual_anchor_class||"mce-item-anchor",t&&r.hasVisual?i.addClass(e,n):i.removeClass(e,n)))}}),r.fire("VisualAid",{element:e,hasVisual:r.hasVisual})},remove:function(){vf(this)},destroy:function(e){bf(this,e)},uploadImages:function(e){return this.editorUpload.uploadImages(e)},_scanForImages:function(){return this.editorUpload.scanForImages()}},Tx);var $x,Wx,Kx,Xx={isEditorUIElement:function(e){return-1!==e.className.toString().indexOf("tox-")||-1!==e.className.toString().indexOf("mce-")}},Yx=function(n,e){var t,r;nr.detect().browser.isIE()?(r=n).on("focusout",function(){Kc(r)}):(t=e,n.on("mouseup touchend",function(e){t.throttle()})),n.on("keyup nodechange",function(e){var t;"nodechange"===(t=e).type&&t.selectionChange||Kc(n)})},Gx=function(e){var t,n,r,o=Li(function(){Kc(e)},0);e.inline&&(t=e,n=o,r=function(){n.throttle()},vi.DOM.bind(j.document,"mouseup",r),t.on("remove",function(){vi.DOM.unbind(j.document,"mouseup",r)})),e.on("init",function(){Yx(e,o)}),e.on("remove",function(){o.cancel()})},Jx=vi.DOM,Qx=function(e){return Xx.isEditorUIElement(e)},Zx=function(t,e){var n=t?t.settings.custom_ui_selector:"";return null!==Jx.getParent(e,function(e){return Qx(e)||!!n&&t.dom.is(e,n)})},eN=function(r,e){var t=e.editor;Gx(t),t.on("focusin",function(){var e=r.focusedEditor;e!==this&&(e&&e.fire("blur",{focusedEditor:this}),r.setActive(this),(r.focusedEditor=this).fire("focus",{blurredEditor:e}),this.focus(!0))}),t.on("focusout",function(){var t=this;be.setEditorTimeout(t,function(){var e=r.focusedEditor;Zx(t,function(){try{return j.document.activeElement}catch(e){return j.document.body}}())||e!==t||(t.fire("blur",{focusedEditor:null}),r.focusedEditor=null)})}),$x||($x=function(e){var t,n=r.activeEditor;t=e.target,n&&t.ownerDocument===j.document&&(t===j.document.body||Zx(n,t)||r.focusedEditor!==n||(n.fire("blur",{focusedEditor:null}),r.focusedEditor=null))},Jx.bind(j.document,"focusin",$x))},tN=function(e,t){e.focusedEditor===t.editor&&(e.focusedEditor=null),e.activeEditor||(Jx.unbind(j.document,"focusin",$x),$x=null)},nN=function(e){e.on("AddEditor",d(eN,e)),e.on("RemoveEditor",d(tN,e))},rN=vi.DOM,oN=Gt.explode,iN=Gt.each,aN=Gt.extend,uN=0,sN=!1,lN=[],cN=[],fN=function(t){var n=t.type;iN(Kx.get(),function(e){switch(n){case"scroll":e.fire("ScrollWindow",t);break;case"resize":e.fire("ResizeWindow",t)}})},dN=function(e){e!==sN&&(e?pn(window).on("resize scroll",fN):pn(window).off("resize scroll",fN),sN=e)},hN=function(t){var e=cN;delete lN[t.id];for(var n=0;n<lN.length;n++)if(lN[n]===t){lN.splice(n,1);break}return cN=V(cN,function(e){return t!==e}),Kx.activeEditor===t&&(Kx.activeEditor=0<cN.length?cN[0]:null),Kx.focusedEditor===t&&(Kx.focusedEditor=null),e.length!==cN.length};aN(Kx={defaultSettings:{},$:pn,majorVersion:"5",minorVersion:"0.1",releaseDate:"2019-02-21",editors:lN,i18n:ki,activeEditor:null,settings:{},setup:function(){var e,t,n,r,o="";if(t=Lx.getDocumentBaseUrl(j.document.location),/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),n=window.tinymce||window.tinyMCEPreInit)e=n.base||n.baseURL,o=n.suffix;else{for(var i=j.document.getElementsByTagName("script"),a=0;a<i.length;a++){var u=(r=i[a].src).substring(r.lastIndexOf("/"));if(/tinymce(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(r)){-1!==u.indexOf(".min")&&(o=".min"),e=r.substring(0,r.lastIndexOf("/"));break}}!e&&j.document.currentScript&&(-1!==(r=j.document.currentScript.src).indexOf(".min")&&(o=".min"),e=r.substring(0,r.lastIndexOf("/")))}this.baseURL=new Lx(t).toAbsolute(e),this.documentBaseURL=t,this.baseURI=new Lx(this.baseURL),this.suffix=o,nN(this)},overrideDefaults:function(e){var t,n;(t=e.base_url)&&(this.baseURL=new Lx(this.documentBaseURL).toAbsolute(t.replace(/\/+$/,"")),this.baseURI=new Lx(this.baseURL)),n=e.suffix,e.suffix&&(this.suffix=n);var r=(this.defaultSettings=e).plugin_base_urls;for(var o in r)Ai.PluginManager.urls[o]=r[o]},init:function(r){var n,u,s=this;u=Gt.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option table tbody tfoot thead tr th td script noscript style textarea video audio iframe object menu"," ");var l=function(e){var t=e.id;return t||(t=(t=e.name)&&!rN.get(t)?e.name:rN.uniqueId(),e.setAttribute("id",t)),t},c=function(e,t){return t.constructor===RegExp?t.test(e.className):rN.hasClass(e,t)},f=function(e){n=e},e=function(){var o,i=0,a=[],n=function(e,t,n){var r=new qx(e,t,s);a.push(r),r.on("init",function(){++i===o.length&&f(a)}),r.targetElm=r.targetElm||n,r.render()};rN.unbind(window,"ready",e),function(e){var t=r[e];t&&t.apply(s,Array.prototype.slice.call(arguments,2))}("onpageload"),o=pn.unique(function(t){var e,n=[];if(he.ie&&he.ie<11)return Xf.initError("TinyMCE does not support the browser you are using. For a list of supported browsers please see: https://www.tinymce.com/docs/get-started/system-requirements/"),[];if(t.types)return iN(t.types,function(e){n=n.concat(rN.select(e.selector))}),n;if(t.selector)return rN.select(t.selector);if(t.target)return[t.target];switch(t.mode){case"exact":0<(e=t.elements||"").length&&iN(oN(e),function(t){var e;(e=rN.get(t))?n.push(e):iN(j.document.forms,function(e){iN(e.elements,function(e){e.name===t&&(t="mce_editor_"+uN++,rN.setAttrib(e,"id",t),n.push(e))})})});break;case"textareas":case"specific_textareas":iN(rN.select("textarea"),function(e){t.editor_deselector&&c(e,t.editor_deselector)||t.editor_selector&&!c(e,t.editor_selector)||n.push(e)})}return n}(r)),r.types?iN(r.types,function(t){Gt.each(o,function(e){return!rN.is(e,t.selector)||(n(l(e),aN({},r,t),e),!1)})}):(Gt.each(o,function(e){var t;(t=s.get(e.id))&&t.initialized&&!(t.getContainer()||t.getBody()).parentNode&&(hN(t),t.unbindAllNativeEvents(),t.destroy(!0),t.removed=!0,t=null)}),0===(o=Gt.grep(o,function(e){return!s.get(e.id)})).length?f([]):iN(o,function(e){var t;t=e,r.inline&&t.tagName.toLowerCase()in u?Xf.initError("Could not initialize inline editor on invalid inline target element",e):n(l(e),r,e)}))};return s.settings=r,rN.bind(window,"ready",e),new me(function(t){n?t(n):f=function(e){t(e)}})},get:function(t){return 0===arguments.length?cN.slice(0):A(t)?F(cN,function(e){return e.id===t}).getOr(null):O(t)&&cN[t]?cN[t]:null},add:function(e){var t=this;return lN[e.id]===e||(null===t.get(e.id)&&("length"!==e.id&&(lN[e.id]=e),lN.push(e),cN.push(e)),dN(!0),t.activeEditor=e,t.fire("AddEditor",{editor:e}),Wx||(Wx=function(){t.fire("BeforeUnload")},rN.bind(window,"beforeunload",Wx))),e},createEditor:function(e,t){return this.add(new qx(e,t,this))},remove:function(e){var t,n,r=this;if(e){if(!A(e))return n=e,M(r.get(n.id))?null:(hN(n)&&r.fire("RemoveEditor",{editor:n}),0===cN.length&&rN.unbind(window,"beforeunload",Wx),n.remove(),dN(0<cN.length),n);iN(rN.select(e),function(e){(n=r.get(e.id))&&r.remove(n)})}else for(t=cN.length-1;0<=t;t--)r.remove(cN[t])},execCommand:function(e,t,n){var r=this.get(n);switch(e){case"mceAddEditor":return this.get(n)||new qx(n,this.settings,this).render(),!0;case"mceRemoveEditor":return r&&r.remove(),!0;case"mceToggleEditor":return r?r.isHidden()?r.show():r.hide():this.execCommand("mceAddEditor",0,n),!0}return!!this.activeEditor&&this.activeEditor.execCommand(e,t,n)},triggerSave:function(){iN(cN,function(e){e.save()})},addI18n:function(e,t){ki.add(e,t)},translate:function(e){return ki.translate(e)},setActive:function(e){var t=this.activeEditor;this.activeEditor!==e&&(t&&t.fire("deactivate",{relatedTarget:e}),e.fire("activate",{relatedTarget:t})),this.activeEditor=e}},xx),Kx.setup();var mN,gN=Kx;function pN(n){return{walk:function(e,t){return kl(n,e,t)},split:om,normalize:function(t){return qp(n,t).fold($(!1),function(e){return t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),!0})}}}(mN=pN||(pN={})).compareRanges=xd,mN.getCaretRangeFromPoint=lp,mN.getSelectedNode=Ka,mN.getNode=Xa;var vN,bN,yN=pN,CN=Math.min,wN=Math.max,xN=Math.round,NN=function(e,t,n){var r,o,i,a,u,s;return r=t.x,o=t.y,i=e.w,a=e.h,u=t.w,s=t.h,"b"===(n=(n||"").split(""))[0]&&(o+=s),"r"===n[1]&&(r+=u),"c"===n[0]&&(o+=xN(s/2)),"c"===n[1]&&(r+=xN(u/2)),"b"===n[3]&&(o-=a),"r"===n[4]&&(r-=i),"c"===n[3]&&(o-=xN(a/2)),"c"===n[4]&&(r-=xN(i/2)),EN(r,o,i,a)},EN=function(e,t,n,r){return{x:e,y:t,w:n,h:r}},zN={inflate:function(e,t,n){return EN(e.x-t,e.y-n,e.w+2*t,e.h+2*n)},relativePosition:NN,findBestRelativePosition:function(e,t,n,r){var o,i;for(i=0;i<r.length;i++)if((o=NN(e,t,r[i])).x>=n.x&&o.x+o.w<=n.w+n.x&&o.y>=n.y&&o.y+o.h<=n.h+n.y)return r[i];return null},intersect:function(e,t){var n,r,o,i;return n=wN(e.x,t.x),r=wN(e.y,t.y),o=CN(e.x+e.w,t.x+t.w),i=CN(e.y+e.h,t.y+t.h),o-n<0||i-r<0?null:EN(n,r,o-n,i-r)},clamp:function(e,t,n){var r,o,i,a,u,s,l,c,f,d;return u=e.x,s=e.y,l=e.x+e.w,c=e.y+e.h,f=t.x+t.w,d=t.y+t.h,r=wN(0,t.x-u),o=wN(0,t.y-s),i=wN(0,l-f),a=wN(0,c-d),u+=r,s+=o,n&&(l+=r,c+=o,u-=i,s-=a),EN(u,s,(l-=i)-u,(c-=a)-s)},create:EN,fromClientRect:function(e){return EN(e.left,e.top,e.width,e.height)}},SN=Gt.each,kN=Gt.extend,TN=function(){};TN.extend=vN=function(n){var e,t,r,o=this.prototype,i=function(){var e,t,n;if(!bN&&(this.init&&this.init.apply(this,arguments),t=this.Mixins))for(e=t.length;e--;)(n=t[e]).init&&n.init.apply(this,arguments)},a=function(){return this},u=function(n,r){return function(){var e,t=this._super;return this._super=o[n],e=r.apply(this,arguments),this._super=t,e}};for(t in bN=!0,e=new this,bN=!1,n.Mixins&&(SN(n.Mixins,function(e){for(var t in e)"init"!==t&&(n[t]=e[t])}),o.Mixins&&(n.Mixins=o.Mixins.concat(n.Mixins))),n.Methods&&SN(n.Methods.split(","),function(e){n[e]=a}),n.Properties&&SN(n.Properties.split(","),function(e){var t="_"+e;n[e]=function(e){return e!==undefined?(this[t]=e,this):this[t]}}),n.Statics&&SN(n.Statics,function(e,t){i[t]=e}),n.Defaults&&o.Defaults&&(n.Defaults=kN({},o.Defaults,n.Defaults)),n)"function"==typeof(r=n[t])&&o[t]?e[t]=u(t,r):e[t]=r;return i.prototype=e,(i.constructor=i).extend=vN,i};var AN=Math.min,RN=Math.max,DN=Math.round,MN={serialize:function(e){var t=JSON.stringify(e);return A(t)?t.replace(/[\u0080-\uFFFF]/g,function(e){var t=e.charCodeAt(0).toString(16);return"\\u"+"0000".substring(t.length)+t}):t},parse:function(e){try{return JSON.parse(e)}catch(t){}}},BN={callbacks:{},count:0,send:function(t){var n=this,r=vi.DOM,o=t.count!==undefined?t.count:n.count,i="tinymce_jsonp_"+o;n.callbacks[o]=function(e){r.remove(i),delete n.callbacks[o],t.callback(e)},r.add(r.doc.body,"script",{id:i,src:t.url,type:"text/javascript"}),n.count++}},_N={send:function(e){var t,n=0,r=function(){!e.async||4===t.readyState||1e4<n++?(e.success&&n<1e4&&200===t.status?e.success.call(e.success_scope,""+t.responseText,t,e):e.error&&e.error.call(e.error_scope,1e4<n?"TIMED_OUT":"GENERAL",t,e),t=null):setTimeout(r,10)};if(e.scope=e.scope||this,e.success_scope=e.success_scope||e.scope,e.error_scope=e.error_scope||e.scope,e.async=!1!==e.async,e.data=e.data||"",_N.fire("beforeInitialize",{settings:e}),t=Qf()){if(t.overrideMimeType&&t.overrideMimeType(e.content_type),t.open(e.type||(e.data?"POST":"GET"),e.url,e.async),e.crossDomain&&(t.withCredentials=!0),e.content_type&&t.setRequestHeader("Content-Type",e.content_type),e.requestheaders&&Gt.each(e.requestheaders,function(e){t.setRequestHeader(e.key,e.value)}),t.setRequestHeader("X-Requested-With","XMLHttpRequest"),(t=_N.fire("beforeSend",{xhr:t,settings:e}).xhr).send(e.data),!e.async)return r();setTimeout(r,10)}}};Gt.extend(_N,xx);var ON,HN,PN,LN,VN=Gt.extend,IN=function(e){this.settings=VN({},e),this.count=0};IN.sendRPC=function(e){return(new IN).send(e)},IN.prototype={send:function(n){var r=n.error,o=n.success;(n=VN(this.settings,n)).success=function(e,t){void 0===(e=MN.parse(e))&&(e={error:"JSON Parse error."}),e.error?r.call(n.error_scope||n.scope,e.error,t):o.call(n.success_scope||n.scope,e.result)},n.error=function(e,t){r&&r.call(n.error_scope||n.scope,e,t)},n.data=MN.serialize({id:n.id||"c"+this.count++,method:n.method,params:n.params}),n.content_type="application/json",_N.send(n)}};try{ON=j.window.localStorage}catch($N){HN={},PN=[],LN={getItem:function(e){var t=HN[e];return t||null},setItem:function(e,t){PN.push(e),HN[e]=String(t)},key:function(e){return PN[e]},removeItem:function(t){PN=PN.filter(function(e){return e===t}),delete HN[t]},clear:function(){PN=[],HN={}},length:0},Object.defineProperty(LN,"length",{get:function(){return PN.length},configurable:!1,enumerable:!1}),ON=LN}var FN,UN=gN,jN={geom:{Rect:zN},util:{Promise:me,Delay:be,Tools:Gt,VK:ch,URI:Lx,Class:TN,EventDispatcher:yx,Observable:xx,I18n:ki,XHR:_N,JSON:MN,JSONRequest:IN,JSONP:BN,LocalStorage:ON,Color:function(e){var n={},u=0,s=0,l=0,t=function(e){var t;return"object"==typeof e?"r"in e?(u=e.r,s=e.g,l=e.b):"v"in e&&function(e,t,n){var r,o,i,a;if(e=(parseInt(e,10)||0)%360,t=parseInt(t,10)/100,n=parseInt(n,10)/100,t=RN(0,AN(t,1)),n=RN(0,AN(n,1)),0!==t){switch(r=e/60,i=(o=n*t)*(1-Math.abs(r%2-1)),a=n-o,Math.floor(r)){case 0:u=o,s=i,l=0;break;case 1:u=i,s=o,l=0;break;case 2:u=0,s=o,l=i;break;case 3:u=0,s=i,l=o;break;case 4:u=i,s=0,l=o;break;case 5:u=o,s=0,l=i;break;default:u=s=l=0}u=DN(255*(u+a)),s=DN(255*(s+a)),l=DN(255*(l+a))}else u=s=l=DN(255*n)}(e.h,e.s,e.v):(t=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)[^\)]*\)/gi.exec(e))?(u=parseInt(t[1],10),s=parseInt(t[2],10),l=parseInt(t[3],10)):(t=/#([0-F]{2})([0-F]{2})([0-F]{2})/gi.exec(e))?(u=parseInt(t[1],16),s=parseInt(t[2],16),l=parseInt(t[3],16)):(t=/#([0-F])([0-F])([0-F])/gi.exec(e))&&(u=parseInt(t[1]+t[1],16),s=parseInt(t[2]+t[2],16),l=parseInt(t[3]+t[3],16)),u=u<0?0:255<u?255:u,s=s<0?0:255<s?255:s,l=l<0?0:255<l?255:l,n};return e&&t(e),n.toRgb=function(){return{r:u,g:s,b:l}},n.toHsv=function(){return e=u,t=s,n=l,o=0,(i=AN(e/=255,AN(t/=255,n/=255)))===(a=RN(e,RN(t,n)))?{h:0,s:0,v:100*(o=i)}:(r=(a-i)/a,{h:DN(60*((e===i?3:n===i?1:5)-(e===i?t-n:n===i?e-t:n-e)/((o=a)-i))),s:DN(100*r),v:DN(100*o)});var e,t,n,r,o,i,a},n.toHex=function(){var e=function(e){return 1<(e=parseInt(e,10).toString(16)).length?e:"0"+e};return"#"+e(u)+e(s)+e(l)},n.parse=t,n}},dom:{EventUtils:ke,Sizzle:kt,DomQuery:pn,TreeWalker:io,DOMUtils:vi,ScriptLoader:xi,RangeUtils:yN,Serializer:Jg,ControlSelection:op,BookmarkManager:tp,Selection:ev,Event:ke.Event},html:{Styles:ai,Entities:Ko,Node:Fl,Schema:oi,SaxParser:$l,DomParser:Wg,Writer:Sc,Serializer:kc},Env:he,AddOnManager:Ai,Annotator:_l,Formatter:Rg,UndoManager:qh,EditorCommands:vx,WindowManager:Ff,NotificationManager:If,EditorObservable:Tx,Shortcuts:Bx,Editor:qx,FocusManager:Xx,EditorManager:gN,DOM:vi.DOM,ScriptLoader:xi.ScriptLoader,PluginManager:Ai.PluginManager,ThemeManager:Ai.ThemeManager,IconManager:Yf,trim:Gt.trim,isArray:Gt.isArray,is:Gt.is,toArray:Gt.toArray,makeMap:Gt.makeMap,each:Gt.each,map:Gt.map,grep:Gt.grep,inArray:Gt.inArray,extend:Gt.extend,create:Gt.create,walk:Gt.walk,createNS:Gt.createNS,resolve:Gt.resolve,explode:Gt.explode,_addCacheSuffix:Gt._addCacheSuffix,isOpera:he.opera,isWebKit:he.webkit,isIE:he.ie,isGecko:he.gecko,isMac:he.mac},qN=UN=Gt.extend(UN,jN);FN=qN,window.tinymce=FN,window.tinyMCE=FN,function(e){if("object"==typeof module)try{module.exports=e}catch(t){}}(qN)}(window);